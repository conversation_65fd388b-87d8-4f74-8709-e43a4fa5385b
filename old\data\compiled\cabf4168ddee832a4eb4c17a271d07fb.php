<!DOCTYPE html>
<?php
// 获取主题设置
$theme = isset($_COOKIE['site_theme']) ? $_COOKIE['site_theme'] : 'red';
$theme_class = 'theme-' . $theme;
?>
<html lang="zh-CN" class="<?php echo $theme_class; ?>">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <title>新闻中心 - <?php echo $site_name ?? ""; ?></title>
    <meta name="keywords" content="新闻,资讯,<?php echo $site_name ?? ""; ?>" />
    <meta name="description" content="新闻中心 - <?php echo $site_name ?? ""; ?>" />

    <link rel="stylesheet" href="/static/font-awesome/css/all.min.css">
    <link rel="stylesheet" href="/static/css/themes.css?v=<?php echo time(); ?>">
    <link rel="stylesheet" href="/template/m/css/common.css?v=<?php echo time(); ?>">
    <link rel="stylesheet" href="/template/m/css/news.css?v=<?php echo time(); ?>">
    <script src="/static/js/common.js"></script>
    <script src="/static/js/page-stabilizer.js"></script>
    <style>
        /* 面包屑导航 */
        .breadcrumb {
            background: #f8f9fa;
            padding: 12px 0;
            border-bottom: 1px solid #e9ecef;
        }

        .breadcrumb .container {
            padding: 0 15px;
        }

        .breadcrumb a {
            color: #007bff;
            text-decoration: none;
            font-size: 14px;
        }

        .breadcrumb .separator {
            margin: 0 8px;
            color: #6c757d;
        }

        .breadcrumb .current {
            color: #333;
            font-weight: 500;
        }

        /* 新闻列表 */
        .news-list {
            background: white;
            padding: 0 8px; /* 减少两侧边距 */
        }

        .news-card {
            background: white;
            margin-bottom: 1px;
            border-bottom: 1px solid #f0f0f0;
            transition: all 0.3s ease;
            position: relative;
        }

        .news-card:hover {
            background: #f8f9fa;
        }

        .news-card-content {
            padding: 12px 8px; /* 减少内边距 */
        }

        /* 标题和时间在一行 */
        .news-item-row {
            display: flex;
            justify-content: space-between;
            align-items: center;
            gap: 10px;
        }

        .news-title {
            font-size: 15px;
            font-weight: 500;
            line-height: 1.4;
            color: #333;
            flex: 1;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap; /* 单行显示 */
        }

        .news-time {
            color: #999;
            font-size: 12px;
            white-space: nowrap;
            flex-shrink: 0;
        }

        .news-tags {
            display: flex;
            gap: 6px;
            margin-top: 8px;
        }

        .tag {
            padding: 4px 8px;
            font-size: 11px;
            border-radius: 12px;
            font-weight: 500;
            color: white;
        }

        .tag.top {
            background: linear-gradient(135deg, #ff6b6b, #ee5a52);
        }

        .tag.rec {
            background: linear-gradient(135deg, #feca57, #ff9ff3);
        }

        /* 空状态 */
        .empty-state {
            text-align: center;
            padding: 60px 20px;
            color: #999;
        }

        .empty-icon {
            font-size: 48px;
            margin-bottom: 16px;
            opacity: 0.5;
        }

        .empty-text {
            font-size: 16px;
        }

        /* 响应式 */
        @media (max-width: 480px) {
            .news-list {
                padding: 0 5px; /* 小屏幕更小的边距 */
            }

            .news-card-content {
                padding: 10px 6px; /* 小屏幕更小的内边距 */
            }

            .news-title {
                font-size: 14px;
            }
        }
    </style>
</head>
<body>
    <header>
        <div class="header-inner">
            <div class="header-left">
                <a href="javascript:history.back();" class="header-back">
                    <i class="fas fa-chevron-left"></i>
                </a>
            </div>
            <div class="header-title">新闻中心</div>
            <div class="header-right">
                <!-- 右侧可以为空 -->
            </div>
        </div>
    </header>
    
    <!-- 面包屑导航 -->
    <div class="breadcrumb">
        <div class="container">
            <a href="/">首页</a>
            <span class="separator">></span>
            <span class="current">新闻中心</span>
        </div>
    </div>

    <!-- 新闻列表 -->
    <div class="news-list">
        <?php if($news_list): ?>
        <?php if(null !== ($news_list ?? null) && is_array($news_list)): foreach($news_list as $news): ?>
        <div class="news-card">
            <a href="/news/<?php echo (isset($news['id'])) ? $news['id'] : ""; ?>.html" style="text-decoration: none; color: inherit;">
                <div class="news-card-content">
                    <div class="news-item-row">
                        <div class="news-title"><?php echo (isset($news['title'])) ? $news['title'] : ""; ?></div>
                        <div class="news-time">
                            <?php echo date('m-d H:i', $news['addtime']); ?>
                        </div>
                    </div>
                    <?php if((null !== ($news ?? null) && is_array($news) && array_key_exists('is_top', $news) && $news['is_top']) || (null !== ($news ?? null) && is_array($news) && array_key_exists('is_recommend', $news) && $news['is_recommend'])): ?>
                    <div class="news-tags">
                        <?php if(null !== ($news ?? null) && is_array($news) && array_key_exists('is_top', $news) && $news['is_top']): ?><span class="tag top">置顶</span><?php endif; ?>
                        <?php if(null !== ($news ?? null) && is_array($news) && array_key_exists('is_recommend', $news) && $news['is_recommend']): ?><span class="tag rec">推荐</span><?php endif; ?>
                    </div>
                    <?php endif; ?>
                </div>
            </a>
        </div>
        <?php endforeach; endif; ?>
        <?php else: ?>
        <div class="empty-state">
            <div class="empty-icon">
                <i class="far fa-newspaper"></i>
            </div>
            <div class="empty-text">暂无新闻</div>
        </div>
        <?php endif; ?>
    </div>

    <footer>
    <div class="container">
        <div class="footer-nav">
            <a href="/login.php">登录/注册</a>
            <a href="/fee.php">电话费用</a>
            <a href="/feedback.php">用户反馈</a>
        </div>
        <div class="theme-switcher">
            <p>主题切换</p>
            <div class="theme-dots">
                <a href="javascript:void(0);" onclick="switchTheme('red')" class="theme-dot theme-red" title="红色主题"></a>
                <a href="javascript:void(0);" onclick="switchTheme('blue')" class="theme-dot theme-blue" title="蓝色主题"></a>
                <a href="javascript:void(0);" onclick="switchTheme('green')" class="theme-dot theme-green" title="绿色主题"></a>
                <a href="javascript:void(0);" onclick="switchTheme('purple')" class="theme-dot theme-purple" title="紫色主题"></a>
                <a href="javascript:void(0);" onclick="switchTheme('orange')" class="theme-dot theme-orange" title="橙色主题"></a>
                <a href="javascript:void(0);" onclick="switchTheme('pink')" class="theme-dot theme-pink" title="粉色主题"></a>
                <a href="javascript:void(0);" onclick="switchTheme('ocean')" class="theme-dot theme-ocean" title="海洋主题"></a>
                <a href="javascript:void(0);" onclick="switchTheme('wechat')" class="theme-dot theme-wechat" title="微信风格"></a>
                <a href="javascript:void(0);" onclick="switchTheme('alipay')" class="theme-dot theme-alipay" title="支付宝风格"></a>
                <a href="javascript:void(0);" onclick="switchTheme('simple')" class="theme-dot theme-simple" title="简约主题"></a>
            </div>
        </div>
        <div class="footer-info">
            <p>京ICP证060405号</p>
            <p>客户服务热线: 10105858</p>
        </div>
    </div>
    <script>
    // 主题切换功能
    function switchTheme(theme) {
        // 设置cookie，有效期30天
        var date = new Date();
        date.setTime(date.getTime() + (30 * 24 * 60 * 60 * 1000));
        document.cookie = "site_theme=" + theme + "; expires=" + date.toUTCString() + "; path=/";
        
        // 更新页面上的主题类
        document.documentElement.className = document.documentElement.className.replace(/theme-\w+/g, '');
        document.documentElement.classList.add('theme-' + theme);
        
        // 同时更新body的主题类，确保背景色只应用于内容区域
        document.body.className = document.body.className.replace(/theme-\w+/g, '');
        document.body.classList.add('theme-' + theme);
        
        // 更新当前选中的主题点
        highlightCurrentTheme(theme);
        
        // 显示切换成功提示
        var themeNames = {
            'red': '红色',
            'blue': '蓝色',
            'green': '绿色',
            'purple': '紫色',
            'orange': '橙色',
            'pink': '粉色',
            'ocean': '海洋',
            'wechat': '微信风格',
            'alipay': '支付宝风格',
            'miui': '小米风格',
            'douyin': '抖音风格',
            'simple': '简约'
        };
        
        // 创建提示元素
        var toast = document.createElement('div');
        toast.className = 'theme-toast';
        toast.textContent = '已切换到' + themeNames[theme] + '主题';
        document.body.appendChild(toast);
        
        // 2秒后移除提示
        setTimeout(function() {
            toast.classList.add('hide');
            setTimeout(function() {
                document.body.removeChild(toast);
            }, 300);
        }, 2000);
    }
    
    // 高亮当前主题
    function highlightCurrentTheme(theme) {
        // 移除所有主题点的高亮
        var themeDots = document.querySelectorAll('.theme-dot');
        themeDots.forEach(function(dot) {
            dot.classList.remove('active');
        });
        
        // 添加当前主题的高亮
        var currentThemeDot = document.querySelector('.theme-dot.theme-' + theme);
        if (currentThemeDot) {
            currentThemeDot.classList.add('active');
        }
    }
    
    // 在页面加载时，根据当前主题高亮对应的主题点
    document.addEventListener('DOMContentLoaded', function() {
        // 获取当前主题
        var currentTheme = 'red'; // 默认主题
        var htmlClass = document.documentElement.className;
        var themeMatch = htmlClass.match(/theme-(\w+)/);
        
        if (themeMatch && themeMatch[1]) {
            currentTheme = themeMatch[1];
            
            // 确保body也具有相同的主题类
            document.body.className = document.body.className.replace(/theme-\w+/g, '');
            document.body.classList.add('theme-' + currentTheme);
        }
        
        // 高亮当前主题
        highlightCurrentTheme(currentTheme);
    });
    </script>
    <style>
    /* 主题切换样式 */
    html, body {
        min-height: 100%;
    }
    
    body {
        background-color: #f5f5f5; /* 默认背景色 */
        margin: 0;
        padding: 0;
    }
    
    /* 确保主题颜色只应用于body */
    html.theme-red, html.theme-blue, html.theme-green, 
    html.theme-purple, html.theme-orange, html.theme-pink,
    html.theme-ocean, html.theme-wechat, html.theme-alipay,
    html.theme-miui, html.theme-douyin {
        background-color: #fff; /* 重置HTML背景为白色 */
    }
    
    /* 主题背景色应用到body */
    body.theme-red { background-color: #fff5f5; }
    body.theme-blue { background-color: #f5f8ff; }
    body.theme-green { background-color: #f5fff8; }
    body.theme-purple { background-color: #f8f5ff; }
    body.theme-orange { background-color: #fff9f5; }
    body.theme-pink { background-color: #fff5f9; }
    body.theme-ocean { background-color: #f5faff; }
    body.theme-wechat { background-color: #f5fff7; }
    body.theme-alipay { background-color: #f5faff; }
    body.theme-simple { background-color: #f8f8f8; }
    
    .theme-switcher {
        text-align: center;
        margin: 15px 0;
        padding: 10px;
        background-color: rgba(255, 255, 255, 0.9);
        border-radius: 10px;
    }
    
    .theme-switcher p {
        margin: 0 0 8px 0;
        font-size: 14px;
        color: #666;
        font-weight: 500;
    }
    
    .theme-dots {
        display: flex;
        flex-wrap: wrap;
        justify-content: center;
        gap: 12px;
        padding: 5px;
    }
    
    .theme-dot {
        display: inline-block;
        width: 22px;
        height: 22px;
        border-radius: 50%;
        cursor: pointer;
        transition: transform 0.2s, box-shadow 0.2s;
        border: 2px solid #fff;
        box-shadow: 0 1px 3px rgba(0,0,0,0.1);
    }
    
    .theme-dot:hover {
        transform: scale(1.2);
        box-shadow: 0 2px 5px rgba(0,0,0,0.2);
    }
    
    .theme-dot.active {
        transform: scale(1.2);
        box-shadow: 0 0 0 2px #fff, 0 0 0 4px var(--primary-color, currentColor);
    }
    
    /* 主题颜色 */
    .theme-red {
        background-color: #e53935;
    }
    
    .theme-blue {
        background-color: #4285f4;
    }
    
    .theme-green {
        background-color: #00a878;
    }
    
    .theme-purple {
        background-color: #7b68ee;
    }
    
    .theme-orange {
        background-color: #ff6b01;
    }
    
    .theme-pink {
        background-color: #e91e63;
    }
    
    .theme-ocean {
        background-color: #006994;
    }
    
    .theme-wechat {
        background-color: #07c160;
    }
    
    .theme-alipay {
        background-color: #1677ff;
    }
    
    .theme-simple {
        background-color: #ffffff;
        border: 1px solid #eeeeee;
    }
    
    /* 主题切换toast提示 */
    .theme-toast {
        position: fixed;
        bottom: 80px;
        left: 50%;
        transform: translateX(-50%);
        background-color: rgba(0, 0, 0, 0.7);
        color: white;
        padding: 10px 20px;
        border-radius: 20px;
        font-size: 14px;
        z-index: 1000;
        opacity: 1;
        transition: opacity 0.3s;
    }
    
    .theme-toast.hide {
        opacity: 0;
    }
    
    /* 简约主题特殊处理 */
    .theme-simple header {
        background-color: #ffffff !important;
        color: #333333 !important;
        border-bottom: 1px solid #eeeeee !important;
        box-shadow: 0 1px 3px rgba(0,0,0,0.05) !important;
    }
    
    .theme-simple .header-back,
    .theme-simple .header-title,
    .theme-simple .header-share,
    .theme-simple .header-search-icon {
        color: #333333 !important;
    }
    
    .theme-simple .header-back:active,
    .theme-simple .header-share:active,
    .theme-simple .header-search-icon:active {
        background-color: rgba(0,0,0,0.05) !important;
    }
    </style>
</footer>


    <!-- 底部导航栏 -->
    <!-- 移动端底部导航栏 -->
<nav class="navbar">
    <a href="/" class="nav-item <?php if($current_page == 'index'): ?>active<?php endif; ?>">
        <span class="nav-icon"><i class="fas fa-home"></i></span>
        <span class="nav-text">首页</span>
    </a>
    <a href="/category.php" class="nav-item <?php if($current_page == 'category'): ?>active<?php endif; ?>">
        <span class="nav-icon"><i class="fas fa-th-large"></i></span>
        <span class="nav-text">分类</span>
    </a>
    <a href="/post.php" class="nav-item publish <?php if($current_page == 'post'): ?>active<?php endif; ?>">
        <span class="nav-icon"><i class="fas fa-plus"></i></span>
        <span class="nav-text">发布</span>
    </a>
    <a href="/message.php" class="nav-item <?php if($current_page == 'message'): ?>active<?php endif; ?>">
        <span class="nav-icon"><i class="fas fa-comment-alt"></i></span>
        <span class="nav-text">消息</span>
    </a>
    <a href="/member/" class="nav-item <?php if($current_page == 'member'): ?>active<?php endif; ?>">
        <span class="nav-icon"><i class="fas fa-user"></i></span>
        <span class="nav-text">我的</span>
    </a>
</nav>

</body>
</html>
