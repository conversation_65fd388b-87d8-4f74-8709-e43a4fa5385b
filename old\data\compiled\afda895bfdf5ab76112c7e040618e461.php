<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php if(null !== ($page_title ?? null)): ?><?php echo $page_title ?? ""; ?> - <?php endif; ?>分类信息网站后台管理</title>
    <link href="../static/font-awesome/css/all.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="static/css/admin_clean.css?v=<?php echo time(); ?>" rel="stylesheet">
    <link href="static/css/pagination.css" rel="stylesheet">
    <link href="../static/css/image-compress.css" rel="stylesheet">
</head>
<body>
    <div class="wrapper" id="wrapper">
        <!-- 侧边栏 -->
        <div class="sidebar" id="sidebar">
            <div class="sidebar-header">
                <a href="index.php" class="logo">
                    <i class="fas fa-cube"></i>
                    <span>管理系统</span>
                </a>
            </div>
            <!-- 侧边栏菜单 -->
<div class="menu-item <?php if($current_page == 'index'): ?>active<?php endif; ?>">
    <a href="index.php">
        <i class="fas fa-home"></i>
        <span>控制面板</span>
    </a>
</div>

<div class="menu-item <?php if($current_page == 'category'): ?>active<?php endif; ?>">
    <a href="category.php">
        <i class="fas fa-list"></i>
        <span>分类管理</span>
    </a>
</div>

<div class="menu-item <?php if($current_page == 'region'): ?>active<?php endif; ?>">
    <a href="region.php">
        <i class="fas fa-map-marker-alt"></i>
        <span>区域管理</span>
    </a>
</div>

<div class="menu-item <?php if($current_page == 'info'): ?>active<?php endif; ?>">
    <a href="info.php">
        <i class="fas fa-file-alt"></i>
        <span>信息管理</span>
    </a>
</div>

<div class="menu-item <?php if($current_page == 'news_category'): ?>active<?php endif; ?>">
    <a href="news_category.php">
        <i class="fas fa-newspaper"></i>
        <span>新闻栏目</span>
    </a>
</div>

<div class="menu-item <?php if($current_page == 'news'): ?>active<?php endif; ?>">
    <a href="news.php">
        <i class="fas fa-edit"></i>
        <span>新闻管理</span>
    </a>
</div>

<div class="menu-item <?php if($current_page == 'pages'): ?>active<?php endif; ?>">
    <a href="pages.php">
        <i class="fas fa-file-alt"></i>
        <span>单页管理</span>
    </a>
</div>

<div class="menu-item <?php if($current_page == 'links'): ?>active<?php endif; ?>">
    <a href="links.php">
        <i class="fas fa-link"></i>
        <span>友情链接</span>
    </a>
</div>

<div class="menu-item <?php if($current_page == 'report'): ?>active<?php endif; ?>">
    <a href="report.php">
        <i class="fas fa-flag"></i>
        <span>举报管理</span>
    </a>
</div>

<div class="menu-item <?php if($current_page == 'admin'): ?>active<?php endif; ?>">
    <a href="admin.php">
        <i class="fas fa-user-shield"></i>
        <span>管理员管理</span>
    </a>
</div>

<div class="menu-item <?php if($current_page == 'operation_logs'): ?>active<?php endif; ?>">
    <a href="operation_logs.php">
        <i class="fas fa-history"></i>
        <span>操作日志</span>
    </a>
</div>

<div class="menu-item <?php if($current_page == 'mobile_security'): ?>active<?php endif; ?>">
    <a href="mobile_security.php">
        <i class="fas fa-shield-alt"></i>
        <span>手机号安全</span>
    </a>
</div>

<div class="menu-item <?php if($current_page == 'setting'): ?>active<?php endif; ?>">
    <a href="setting.php">
        <i class="fas fa-cog"></i>
        <span>系统设置</span>
    </a>
</div>

<div class="menu-item <?php if($current_page == 'cache_manager'): ?>active<?php endif; ?>">
    <a href="cache_manager.php">
        <i class="fas fa-memory"></i>
        <span>缓存管理</span>
    </a>
</div>



<div class="menu-item <?php if($current_page == 'db_backup'): ?>active<?php endif; ?>">
    <a href="db_backup.php">
        <i class="fas fa-database"></i>
        <span>数据库备份</span>
    </a>
</div>
        </div>

        <!-- 顶部导航 -->
<div class="top-nav">
    <div class="nav-left">
        <div class="toggle-sidebar" id="toggle-sidebar">
            <i class="fas fa-bars"></i>
        </div>
        <div class="breadcrumb">
            <span class="admin-badge"><?php echo (isset($admin['username'])) ? $admin['username'] : ""; ?></span>
            <i class="fas fa-chevron-right"></i>
            <span>控制台</span>
            <?php if(null !== ($breadcrumb ?? null)): ?>
            <i class="fas fa-chevron-right"></i>
            <span><?php echo $breadcrumb ?? ""; ?></span>
            <?php endif; ?>
        </div>
    </div>
    <div class="nav-right">
        <div class="nav-item" id="clear-cache-btn" title="清理所有缓存">
            <i class="fas fa-trash-alt"></i>
        </div>
        <div class="nav-item" title="前台首页">
            <a href="../" target="_blank" style="color:inherit;text-decoration:none;">
                <i class="fas fa-home"></i>
            </a>
        </div>
        <div class="user-item">
            <div class="user-avatar"><i class="fas fa-user"></i></div>
            <span class="user-name"><?php echo (isset($admin['username'])) ? $admin['username'] : ""; ?></span>
            <a href="logout.php" class="logout-link" title="退出登录">
                <i class="fas fa-sign-out-alt"></i>
            </a>
        </div>
    </div>
</div>

<!-- 清理缓存功能的遮罩层和对话框 -->
<div id="cache-overlay" style="display:none; position:fixed; top:0; left:0; width:100%; height:100%; background:rgba(0,0,0,0.5); z-index:2000;"></div>
<div id="cache-modal" style="display:none; position:fixed; top:50%; left:50%; transform:translate(-50%,-50%); background:#fff; border-radius:8px; box-shadow:0 4px 20px rgba(0,0,0,0.2); width:300px; padding:20px; z-index:2001;">
    <div style="margin-bottom:15px; font-size:16px; font-weight:600;">确认清理缓存</div>
    <p style="margin-bottom:20px; font-size:14px; color:#666;">此操作将清理所有缓存，包括：</p>
    <ul style="margin-bottom:20px; padding-left:20px; font-size:14px; color:#666;">
        <li>页面缓存</li>
        <li>数据缓存</li>
        <li>模板编译文件</li>
    </ul>
    <div style="display:flex; justify-content:flex-end; gap:10px;">
        <button id="cancel-clear-cache" style="padding:8px 16px; border:1px solid #ddd; background:#fff; border-radius:4px; cursor:pointer;">取消</button>
        <button id="confirm-clear-cache" style="padding:8px 16px; border:none; background:#dc3545; color:#fff; border-radius:4px; cursor:pointer;">确认清理</button>
    </div>
</div>

<!-- 成功提示框 -->
<div id="success-toast" style="display:none; position:fixed; top:50%; left:50%; transform:translate(-50%,-50%); background:#28a745; color:#fff; padding:15px 25px; border-radius:6px; box-shadow:0 4px 12px rgba(0,0,0,0.15); z-index:2002; font-size:14px;">
    <i class="fas fa-check-circle" style="margin-right:8px;"></i>
    缓存清理成功！
</div>

<script>
    document.addEventListener('DOMContentLoaded', function() {
        const clearCacheBtn = document.getElementById('clear-cache-btn');
        const cacheOverlay = document.getElementById('cache-overlay');
        const cacheModal = document.getElementById('cache-modal');
        const cancelClearCache = document.getElementById('cancel-clear-cache');
        const confirmClearCache = document.getElementById('confirm-clear-cache');
        const successToast = document.getElementById('success-toast');
        
        if (clearCacheBtn && cacheOverlay && cacheModal) {
            clearCacheBtn.addEventListener('click', function() {
                cacheOverlay.style.display = 'block';
                cacheModal.style.display = 'block';
            });
            
            cancelClearCache.addEventListener('click', function() {
                cacheOverlay.style.display = 'none';
                cacheModal.style.display = 'none';
            });
            
            cacheOverlay.addEventListener('click', function() {
                cacheOverlay.style.display = 'none';
                cacheModal.style.display = 'none';
            });
            
            confirmClearCache.addEventListener('click', function() {
                // 发送清理缓存请求
                const xhr = new XMLHttpRequest();
                xhr.open('POST', 'cache_manager.php', true);
                xhr.setRequestHeader('Content-Type', 'application/x-www-form-urlencoded');
                
                confirmClearCache.innerHTML = '清理中...';
                confirmClearCache.disabled = true;
                
                xhr.onreadystatechange = function() {
                    if (xhr.readyState === 4) {
                        cacheOverlay.style.display = 'none';
                        cacheModal.style.display = 'none';
                        
                        if (xhr.status === 200) {
                            // 显示成功提示
                            successToast.style.display = 'block';
                            successToast.style.transform = 'translate(-50%, -50%) scale(0.8)';
                            
                            setTimeout(function() {
                                successToast.style.transform = 'translate(-50%, -50%) scale(1)';
                            }, 100);
                            
                            setTimeout(function() {
                                successToast.style.transform = 'translate(-50%, -50%) scale(0.8)';
                                setTimeout(function() {
                                    successToast.style.display = 'none';
                                    successToast.style.transform = 'translate(-50%, -50%) scale(1)';
                                }, 300);
                            }, 2000);
                        }
                        
                        confirmClearCache.disabled = false;
                        confirmClearCache.innerHTML = '确认清理';
                    }
                };
                
                xhr.send('action=clear_cache&type=all');
            });
        }
    });
</script>


        <!-- 主内容区 (开始) -->
        <div class="main-content">

<script>
    document.addEventListener('DOMContentLoaded', function() {
        // 侧边栏折叠功能
        const toggleSidebar = document.getElementById('toggle-sidebar');
        const sidebar = document.getElementById('sidebar');
        const wrapper = document.getElementById('wrapper');
        
        if (toggleSidebar && sidebar && wrapper) {
            toggleSidebar.addEventListener('click', function() {
                sidebar.classList.toggle('collapsed');
                wrapper.classList.toggle('collapsed');
                localStorage.setItem('sidebar_collapsed', sidebar.classList.contains('collapsed'));
            });
            
            // 恢复侧边栏状态
            const isCollapsed = localStorage.getItem('sidebar_collapsed') === 'true';
            if (isCollapsed) {
                sidebar.classList.add('collapsed');
                wrapper.classList.add('collapsed');
            }
        }
        
        // 设置当前页面的菜单项为激活状态
        const currentPath = window.location.pathname;
        const menuItems = document.querySelectorAll('.menu-item a');
        
        menuItems.forEach(function(item) {
            const href = item.getAttribute('href');
            if (href) {
                // 检查完整路径匹配
                if (currentPath.endsWith(href)) {
                    const menuItem = item.closest('.menu-item');
                    if (menuItem) {
                        menuItem.classList.add('active');
                    }
                    
                    foundActive = true;
                }
            }
        });
        
        // 如果没有找到完全匹配的，尝试部分匹配
        if (!foundActive) {
            const pathParts = currentPath.split('/');
            const filename = pathParts[pathParts.length - 1];
            
            if (filename) {
                menuItems.forEach(function(item) {
                    const href = item.getAttribute('href');
                    if (href && href.includes(filename.split('.')[0])) {
                        const menuItem = item.closest('.menu-item');
                        if (menuItem) {
                            menuItem.classList.add('active');
                        }
                    }
                });
            }
        }
        
        // 保存当前激活的菜单项
        menuItems.forEach(function(item) {
            item.addEventListener('click', function() {
                const href = this.getAttribute('href');
                if (href) {
                    localStorage.setItem('active_menu_item', href);
                }
            });
        });
    });

    // 处理URL参数中的错误和成功消息
    function handleUrlMessages() {
        const urlParams = new URLSearchParams(window.location.search);
        const message = urlParams.get('message');
        const error = urlParams.get('error');

        if (message) {
            showSuccessMessage(message);
            // 清除URL中的message参数
            clearUrlParameter('message');
        }

        if (error) {
            showErrorMessage(error);
            // 清除URL中的error参数
            clearUrlParameter('error');
        }
    }

    // 显示成功消息
    function showSuccessMessage(message) {
        const alertHtml = `
            <div class="alert alert-success alert-dismissible fade show" role="alert" style="position: fixed; top: 70px; right: 20px; z-index: 9999; min-width: 300px; box-shadow: 0 4px 12px rgba(0,0,0,0.15);">
                <i class="fas fa-check-circle me-2"></i>
                <span>${message}</span>
                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
            </div>
        `;
        document.body.insertAdjacentHTML('beforeend', alertHtml);

        // 5秒后自动消失
        setTimeout(() => {
            const alert = document.querySelector('.alert-success');
            if (alert) {
                alert.remove();
            }
        }, 5000);
    }

    // 显示错误消息
    function showErrorMessage(message) {
        const alertHtml = `
            <div class="alert alert-danger alert-dismissible fade show" role="alert" style="position: fixed; top: 70px; right: 20px; z-index: 9999; min-width: 300px; box-shadow: 0 4px 12px rgba(0,0,0,0.15);">
                <i class="fas fa-exclamation-circle me-2"></i>
                <span>${message}</span>
                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
            </div>
        `;
        document.body.insertAdjacentHTML('beforeend', alertHtml);

        // 8秒后自动消失（错误消息显示时间稍长）
        setTimeout(() => {
            const alert = document.querySelector('.alert-danger');
            if (alert) {
                alert.remove();
            }
        }, 8000);
    }

    // 清除URL参数
    function clearUrlParameter(param) {
        const url = new URL(window.location);
        url.searchParams.delete(param);
        window.history.replaceState({}, document.title, url.toString());
    }

    // 页面加载完成后处理URL消息
    document.addEventListener('DOMContentLoaded', function() {
        handleUrlMessages();
    });
</script>


<style>
/* 筛选表单样式 */
.filter-form {
    display: flex;
    flex-wrap: wrap;
    align-items: center;
    gap: 15px;
    padding: 15px;
    background-color: #f8f9fa;
    border-radius: 6px;
    margin-bottom: 20px;
}

.filter-form-item {
    display: flex;
    align-items: center;
    gap: 8px;
}

.filter-form-item label {
    font-weight: 600;
    color: #333;
    white-space: nowrap;
    margin: 0;
    font-size: 14px;
}

.filter-form-item .form-control {
    min-width: 120px;
    height: 32px;
    padding: 4px 8px;
    font-size: 14px;
    border: 1px solid #ddd;
    border-radius: 4px;
}

.filter-form-item .form-control:focus {
    border-color: #1b68ff;
    box-shadow: 0 0 0 0.2rem rgba(27, 104, 255, 0.25);
}

/* 按钮样式 */
.btn {
    display: inline-block;
    padding: 6px 12px;
    font-size: 14px;
    font-weight: 500;
    line-height: 1.5;
    text-align: center;
    text-decoration: none;
    vertical-align: middle;
    cursor: pointer;
    border: 1px solid transparent;
    border-radius: 4px;
    transition: all 0.15s ease-in-out;
}

.btn-sm {
    padding: 4px 8px;
    font-size: 12px;
}

.btn-light-primary {
    background-color: #e6f0ff;
    color: #1b68ff;
    border: 1px solid #cce0ff;
}
.btn-light-primary:hover {
    background-color: #d1e3ff;
    color: #0056b3;
    text-decoration: none;
}

.btn-light-secondary {
    background-color: #f0f0f0;
    color: #666666;
    border: 1px solid #dddddd;
}
.btn-light-secondary:hover {
    background-color: #e0e0e0;
    color: #444444;
    text-decoration: none;
}

.btn-light-success {
    background-color: #e6ffe6;
    color: #00aa00;
    border: 1px solid #b3ffb3;
}
.btn-light-success:hover {
    background-color: #d1ffd1;
    color: #008800;
    text-decoration: none;
}

.btn-light-info {
    background-color: #e6f7ff;
    color: #00aaff;
    border: 1px solid #b3e0ff;
}
.btn-light-info:hover {
    background-color: #d1f0ff;
    color: #0088cc;
    text-decoration: none;
}

.btn-light-danger {
    background-color: #ffe6e6;
    color: #ff3333;
    border: 1px solid #ffb3b3;
}
.btn-light-danger:hover {
    background-color: #ffd1d1;
    color: #cc0000;
    text-decoration: none;
}

.btn-light-secondary {
    background-color: #f0f0f0;
    color: #6c757d;
    border: 1px solid #dddddd;
}
.btn-light-secondary:hover {
    background-color: #e0e0e0;
    color: #495057;
    text-decoration: none;
}

.btn-xs {
    padding: 0.125rem 0.25rem;
    font-size: 0.75rem;
    line-height: 1.2;
    border-radius: 3px;
}

.btn-danger {
    background-color: #dc3545;
    color: #fff;
    border: 1px solid #dc3545;
}
.btn-danger:hover {
    background-color: #c82333;
    border-color: #bd2130;
    color: #fff;
    text-decoration: none;
}

/* 表格样式 */
.table-responsive {
    overflow-x: auto;
}

.content-table {
    width: 100%;
    border-collapse: collapse;
    background-color: #fff;
    border: 1px solid #dee2e6;
    table-layout: fixed;
}

.content-table th,
.content-table td {
    padding: 8px 12px;
    text-align: left;
    border-bottom: 1px solid #dee2e6;
    vertical-align: middle;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.content-table th {
    background-color: #f8f9fa;
    font-weight: 600;
    color: #495057;
    border-bottom: 2px solid #dee2e6;
}

.content-table tbody tr:hover {
    background-color: #f5f5f5;
}

/* 设置列宽 */
.content-table .col-checkbox { width: 40px; }
.content-table .col-id { width: 80px; }
.content-table .col-title { width: 35%; }
.content-table .col-category { width: 120px; }
.content-table .col-time { width: 140px; }
.content-table .col-clicks { width: 80px; }
.content-table .col-actions { width: 220px; }

/* 操作按钮样式 */
.info-actions {
    display: flex;
    flex-wrap: nowrap;
    justify-content: flex-end;
    gap: 3px;
    min-width: 200px;
}

.info-actions .btn {
    padding: 0.25rem 0.5rem;
    font-size: 0.875rem;
    white-space: nowrap;
    text-decoration: none !important;
}



/* 快捷操作按钮的激活状态 */
.info-actions .btn-sm.btn-light-success {
    background-color: #d4edda;
    color: #155724;
    border-color: #c3e6cb;
}

.info-actions .btn-sm.btn-light-danger {
    background-color: #f8d7da;
    color: #721c24;
    border-color: #f5c6cb;
}

/* 快捷操作按钮悬停效果 */
.info-actions .btn-sm:hover {
    transform: scale(1.05);
    transition: transform 0.2s ease;
}

/* 加载状态的旋转动画 */
@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.fa-spin {
    animation: spin 1s linear infinite;
}

/* 标签样式 */
.label {
    display: inline-block;
    padding: 2px 6px;
    font-size: 11px;
    font-weight: bold;
    line-height: 1;
    color: #fff;
    text-align: center;
    white-space: nowrap;
    vertical-align: baseline;
    border-radius: 3px;
    margin-left: 5px;
}

.label-danger {
    background-color: #d9534f;
}

.label-success {
    background-color: #5cb85c;
}

.label-warning {
    background-color: #f0ad4e;
}

/* 批量操作样式 */
.batch-actions {
    padding: 20px 0;
    border-top: 1px solid #dee2e6;
    background-color: #f8f9fa;
    border-radius: 0 0 6px 6px;
}

.batch-actions-group {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-bottom: 12px;
    flex-wrap: wrap;
}

.batch-actions-group:last-child {
    margin-bottom: 0;
}

.batch-actions-label {
    font-weight: 600;
    color: #495057;
    font-size: 13px;
    min-width: 80px;
    margin: 0;
    white-space: nowrap;
}

.batch-actions .btn {
    margin-bottom: 0;
}

.batch-actions .btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

.batch-actions .btn-success {
    background-color: #28a745;
    border-color: #28a745;
    color: #fff;
}

.batch-actions .btn-success:hover:not(:disabled) {
    background-color: #218838;
    border-color: #1e7e34;
}

.batch-actions .btn-info {
    background-color: #17a2b8;
    border-color: #17a2b8;
    color: #fff;
}

.batch-actions .btn-info:hover:not(:disabled) {
    background-color: #138496;
    border-color: #117a8b;
}

.batch-actions .btn-warning {
    background-color: #ffc107;
    border-color: #ffc107;
    color: #212529;
}

.batch-actions .btn-warning:hover:not(:disabled) {
    background-color: #e0a800;
    border-color: #d39e00;
}

.batch-actions .btn-outline-secondary {
    color: #6c757d;
    border-color: #6c757d;
    background-color: transparent;
}

.batch-actions .btn-outline-secondary:hover {
    color: #fff;
    background-color: #6c757d;
    border-color: #6c757d;
}

.batch-actions .btn-primary {
    background-color: #007bff;
    border-color: #007bff;
    color: #fff;
}

.batch-actions .btn-primary:hover:not(:disabled) {
    background-color: #0056b3;
    border-color: #004085;
}

.batch-actions .btn-dark {
    background-color: #343a40;
    border-color: #343a40;
    color: #fff;
}

.batch-actions .btn-dark:hover:not(:disabled) {
    background-color: #23272b;
    border-color: #1d2124;
}

/* 快速筛选标签样式 */
.filter-tag {
    display: inline-block;
    padding: 6px 12px;
    font-size: 12px;
    font-weight: normal;
    color: #fff;
    text-decoration: none;
    border-radius: 4px;
    margin-right: 5px;
    margin-bottom: 5px;
    transition: all 0.2s;
}

.filter-tag.active-all, .filter-tag.active-top, .filter-tag.active-recommend, .filter-tag.active-hidden {
    background-color: #1b68ff;
    font-weight: 600;
    box-shadow: 0 2px 4px rgba(27, 104, 255, 0.3);
}

.filter-tag.inactive-all, .filter-tag.inactive-top, .filter-tag.inactive-recommend, .filter-tag.inactive-hidden {
    background-color: #6c757d;
    font-weight: normal;
}

.filter-tag:hover {
    opacity: 0.8;
    transform: translateY(-1px);
}

/* 新闻标题链接样式 */
.news-title-link {
    color: #333;
    font-weight: 500;
    text-decoration: none !important;
}

.news-title-link:hover {
    color: #1b68ff;
    text-decoration: none !important;
}

/* 移除链接下划线 */
a, a:hover, a:focus, a:active {
    text-decoration: none !important;
}

/* 响应式调整 */
@media (max-width: 768px) {
    .filter-form {
        flex-direction: column;
        align-items: stretch;
        gap: 10px;
    }

    .filter-form-item {
        flex-direction: column;
        align-items: stretch;
        gap: 5px;
    }

    .filter-form-item label {
        text-align: left;
    }

    .info-actions {
        flex-direction: column;
        gap: 3px;
    }

    .batch-actions-group {
        flex-direction: column;
        align-items: stretch;
        gap: 8px;
    }

    .batch-actions-label {
        min-width: auto;
        text-align: center;
        margin-bottom: 5px;
    }

    .batch-actions-group .btn {
        width: 100%;
    }
}
</style>

<div class="container-fluid">
    <!-- 消息提示 -->
    <?php if($message): ?>
    <div class="alert alert-success">
        <?php echo $message ?? ""; ?>
    </div>
    <?php endif; ?>
    
    <?php if($error): ?>
    <div class="alert alert-danger">
        <?php echo $error ?? ""; ?>
    </div>
    <?php endif; ?>
    
    <!-- 新闻管理 -->
    <div class="card mb-4">
        <div class="card-header d-flex justify-content-between align-items-center">
            <div>
                <h3 class="card-title">新闻文章管理</h3>
            </div>
            <!-- <div>
                <a href="news.php?op=add" class="btn btn-primary btn-sm">添加文章</a>
                <a href="test_ueditor.php" class="btn btn-info btn-sm">UEditor测试</a>
            </div> -->
        </div>
        <div class="card-body">
            <!-- 快速筛选按钮 -->
            <div style="margin-bottom: 20px;">
                <div style="display: flex; flex-wrap: wrap; gap: 8px;">
                    <a href="news.php" class="filter-tag <?php if(!$smarty['get']['is_top'] && !$smarty['get']['is_recommend'] && !$smarty['get']['is_show']): ?>active-all<?php else: ?>inactive-all<?php endif; ?>">全部文章</a>
                    <a href="news.php?is_top=1" class="filter-tag <?php if($smarty['get']['is_top'] == '1'): ?>active-top<?php else: ?>inactive-top<?php endif; ?>">置顶文章</a>
                    <a href="news.php?is_recommend=1" class="filter-tag <?php if($smarty['get']['is_recommend'] == '1'): ?>active-recommend<?php else: ?>inactive-recommend<?php endif; ?>">推荐文章</a>
                    <a href="news.php?is_show=0" class="filter-tag <?php if($smarty['get']['is_show'] == '0'): ?>active-hidden<?php else: ?>inactive-hidden<?php endif; ?>">隐藏文章</a>
                </div>
            </div>

            <!-- 高级筛选表单 -->
            <form action="news.php" method="get">
                <div class="filter-form">
                    <div class="filter-form-item">
                        <label>栏目:</label>
                        <select name="catid" id="catid" class="form-control" style="min-width: 150px;">
                            <option value="0">所有栏目</option>
                            <?php if(null !== ($categories ?? null) && is_array($categories)): foreach($categories as $cat): ?>
                            <option value="<?php echo (isset($cat['catid'])) ? $cat['catid'] : ""; ?>" <?php if($catid == $cat['catid']): ?>selected<?php endif; ?>><?php echo (isset($cat['catname'])) ? $cat['catname'] : ""; ?></option>
                            <?php if(null !== ($cat ?? null) && is_array($cat) && array_key_exists('children', $cat) && $cat['children']): ?>
                            <?php if(null !== ($cat ?? null) && is_array($cat['children'])): foreach($cat['children'] as $child): ?>
                            <option value="<?php echo (isset($child['catid'])) ? $child['catid'] : ""; ?>" <?php if($catid == $child['catid']): ?>selected<?php endif; ?>>-- <?php echo (isset($child['catname'])) ? $child['catname'] : ""; ?></option>
                            <?php endforeach; endif; ?>
                            <?php endif; ?>
                            <?php endforeach; endif; ?>
                        </select>
                    </div>

                    <div class="filter-form-item">
                        <label>标题:</label>
                        <input type="text" name="title" value="<?php echo $title ?? ""; ?>" placeholder="输入文章标题..." class="form-control" style="min-width: 200px;">
                    </div>

                    <div class="filter-form-item">
                        <label>ID:</label>
                        <input type="text" name="id" value="<?php if(null !== ($id ?? null)): ?><?php echo $id ?? ""; ?><?php endif; ?>" placeholder="输入ID..." class="form-control" style="width: 80px;">
                    </div>

                    <div class="filter-form-item">
                        <button type="submit" class="btn btn-sm btn-light-primary">
                            <i class="fas fa-search"></i> 筛选
                        </button>
                        <a href="news.php" class="btn btn-sm btn-light-secondary">
                            <i class="fas fa-undo"></i> 重置
                        </a>
                    </div>

                    <div style="margin-left: auto;">
                        <a href="news.php?op=add" class="btn btn-sm btn-light-success">
                            <i class="fas fa-plus"></i> 添加文章
                        </a>
                        <a href="javascript:if(confirm('确定要取消所有文章的置顶和推荐状态吗？此操作不可撤销！'))location='news.php?op=reset_all_status'" class="btn btn-sm btn-light-warning">
                            <i class="fas fa-undo"></i> 重置状态
                        </a>
                    </div>
                </div>
            </form>

            <!-- 新闻列表 -->
            <form id="batch-form" action="news.php?op=batch_delete" method="post">
                <div class="table-responsive">
                    <table class="content-table">
                        <thead>
                            <tr>
                                <th class="col-checkbox" style="width: 40px;"><input type="checkbox" id="check-all"></th>
                                <th class="col-id" style="width: 80px;">ID</th>
                                <th class="col-title">标题</th>
                                <th class="col-category">栏目</th>
                                <th class="col-time">发布时间</th>
                                <th class="col-clicks">点击</th>
                                <th class="col-actions">操作</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php if($news_list): ?>
                            <?php if(null !== ($news_list ?? null) && is_array($news_list)): foreach($news_list as $news): ?>
                            <tr>
                                <td><input type="checkbox" name="ids[]" value="<?php echo (isset($news['id'])) ? $news['id'] : ""; ?>" class="news-checkbox"></td>
                                <td><?php echo (isset($news['id'])) ? $news['id'] : ""; ?></td>
                                <td>
                                    <a href="news.php?op=edit&id=<?php echo (isset($news['id'])) ? $news['id'] : ""; ?>" class="news-title-link"><?php echo (isset($news['title'])) ? $news['title'] : ""; ?></a>
                                    <?php if($news['is_top']): ?><span class="label label-danger">置顶</span><?php endif; ?>
                                    <?php if($news['is_recommend']): ?><span class="label label-success">推荐</span><?php endif; ?>
                                    <?php if($news['is_show'] == 0): ?><span class="label label-warning">隐藏</span><?php endif; ?>
                                </td>
                                <td><?php echo (isset($news['catname'])) ? $news['catname'] : ""; ?></td>
                                <td><?php echo null !== ((null !== ($news ?? null)) ? ($news['addtime']) : null) ? Template::date_format((null !== ($news ?? null)) ? ($news['addtime']) : null, 'Y-m-d H:i') : ""; ?></td>
                                <td class="text-center"><?php echo (isset($news['click'])) ? $news['click'] : ""; ?></td>
                                <td class="text-right">
                                    <div class="info-actions">
                                        <!-- 快捷操作按钮 -->
                                        <?php if($news['is_recommend']): ?>
                                        <a href="news.php?op=toggle_recommend&id=<?php echo (isset($news['id'])) ? $news['id'] : ""; ?>" class="btn btn-sm btn-light-success" title="取消推荐" onclick="return confirm('确定要取消推荐吗？')">
                                            推荐
                                        </a>
                                        <?php else: ?>
                                        <a href="news.php?op=toggle_recommend&id=<?php echo (isset($news['id'])) ? $news['id'] : ""; ?>" class="btn btn-sm btn-light-secondary" title="设为推荐" onclick="return confirm('确定要设为推荐吗？')">
                                            推荐
                                        </a>
                                        <?php endif; ?>

                                        <?php if($news['is_top']): ?>
                                        <a href="news.php?op=toggle_top&id=<?php echo (isset($news['id'])) ? $news['id'] : ""; ?>" class="btn btn-sm btn-light-danger" title="取消置顶" onclick="return confirm('确定要取消置顶吗？')">
                                            置顶
                                        </a>
                                        <?php else: ?>
                                        <a href="news.php?op=toggle_top&id=<?php echo (isset($news['id'])) ? $news['id'] : ""; ?>" class="btn btn-sm btn-light-secondary" title="设为置顶" onclick="return confirm('确定要设为置顶吗？')">
                                            置顶
                                        </a>
                                        <?php endif; ?>

                                        <a href="../news.php?op=detail&id=<?php echo (isset($news['id'])) ? $news['id'] : ""; ?>" target="_blank" class="btn btn-sm btn-light-info" title="查看">查看</a>
                                        <a href="news.php?op=edit&id=<?php echo (isset($news['id'])) ? $news['id'] : ""; ?>" class="btn btn-sm btn-light-primary" title="编辑">编辑</a>
                                        <a href="javascript:if(confirm('确定要删除这篇文章吗？'))location='news.php?op=delete&id=<?php echo (isset($news['id'])) ? $news['id'] : ""; ?>'" class="btn btn-sm btn-light-danger" title="删除">删除</a>
                                    </div>
                                </td>
                            </tr>
                            <?php endforeach; endif; ?>
                            <?php else: ?>
                            <tr>
                                <td colspan="7" class="text-center">暂无记录</td>
                            </tr>
                            <?php endif; ?>
                        </tbody>
                    </table>
                </div>
                
                <?php if($news_list): ?>
                <!-- 批量操作区域 -->
                <div class="batch-actions mt-3">
                    <div class="batch-actions-group">
                        <label class="batch-actions-label">选择操作：</label>
                        <button type="button" id="select-all" class="btn btn-sm btn-outline-secondary">全选</button>
                        <button type="button" id="select-none" class="btn btn-sm btn-outline-secondary">取消全选</button>
                    </div>

                    <div class="batch-actions-group">
                        <label class="batch-actions-label">推荐操作：</label>
                        <button type="button" id="batch-recommend-btn" class="btn btn-sm btn-success" disabled>批量推荐</button>
                        <button type="button" id="batch-unrecommend-btn" class="btn btn-sm btn-warning" disabled>取消推荐</button>
                    </div>

                    <div class="batch-actions-group">
                        <label class="batch-actions-label">置顶操作：</label>
                        <button type="button" id="batch-top-btn" class="btn btn-sm btn-info" disabled>批量置顶</button>
                        <button type="button" id="batch-untop-btn" class="btn btn-sm btn-secondary" disabled>取消置顶</button>
                    </div>

                    <div class="batch-actions-group">
                        <label class="batch-actions-label">显示操作：</label>
                        <button type="button" id="batch-show-btn" class="btn btn-sm btn-primary" disabled>批量显示</button>
                        <button type="button" id="batch-hide-btn" class="btn btn-sm btn-dark" disabled>批量隐藏</button>
                    </div>

                    <div class="batch-actions-group">
                        <label class="batch-actions-label">危险操作：</label>
                        <button type="button" id="batch-delete-btn" class="btn btn-sm btn-danger" disabled>批量删除</button>
                    </div>
                </div>
                <?php endif; ?>
            </form>
            
            <?php if($total > 0): ?>
            <div class="pagination-wrapper">
                <div class="pagination-container">
                    <?php echo $pagination_html ?? ""; ?>
                    <span class="pagination-total">共 <?php echo (isset($pagination['total_page'])) ? $pagination['total_page'] : ""; ?> 页</span>
                </div>
            </div>
            <?php endif; ?>
        </div>
    </div>
</div>

<!-- 批量操作的JavaScript -->
<script type="text/javascript">
document.addEventListener('DOMContentLoaded', function() {
    // 全选按钮
    var checkAll = document.getElementById('check-all');
    var newsCheckboxes = document.getElementsByClassName('news-checkbox');
    var batchDeleteBtn = document.getElementById('batch-delete-btn');
    
    // 全选/取消全选
    checkAll.addEventListener('change', function() {
        for (var i = 0; i < newsCheckboxes.length; i++) {
            newsCheckboxes[i].checked = this.checked;
        }
        updateBatchDeleteButton();
    });
    
    // 全选按钮
    document.getElementById('select-all').addEventListener('click', function() {
        checkAll.checked = true;
        for (var i = 0; i < newsCheckboxes.length; i++) {
            newsCheckboxes[i].checked = true;
        }
        updateBatchDeleteButton();
    });
    
    // 取消全选按钮
    document.getElementById('select-none').addEventListener('click', function() {
        checkAll.checked = false;
        for (var i = 0; i < newsCheckboxes.length; i++) {
            newsCheckboxes[i].checked = false;
        }
        updateBatchDeleteButton();
    });
    
    // 更新批量操作按钮状态
    function updateBatchDeleteButton() {
        var checkedCount = 0;
        for (var i = 0; i < newsCheckboxes.length; i++) {
            if (newsCheckboxes[i].checked) {
                checkedCount++;
            }
        }
        var hasSelection = checkedCount > 0;

        // 更新所有批量操作按钮状态
        batchDeleteBtn.disabled = !hasSelection;
        document.getElementById('batch-recommend-btn').disabled = !hasSelection;
        document.getElementById('batch-unrecommend-btn').disabled = !hasSelection;
        document.getElementById('batch-top-btn').disabled = !hasSelection;
        document.getElementById('batch-untop-btn').disabled = !hasSelection;
        document.getElementById('batch-show-btn').disabled = !hasSelection;
        document.getElementById('batch-hide-btn').disabled = !hasSelection;
    }
    
    // 当单个复选框状态改变时，更新全选复选框状态
    for (var i = 0; i < newsCheckboxes.length; i++) {
        newsCheckboxes[i].addEventListener('change', function() {
            var allChecked = true;
            for (var j = 0; j < newsCheckboxes.length; j++) {
                if (!newsCheckboxes[j].checked) {
                    allChecked = false;
                    break;
                }
            }
            checkAll.checked = allChecked;
            updateBatchDeleteButton();
        });
    }
    
    // 批量删除按钮点击事件
    batchDeleteBtn.addEventListener('click', function() {
        var checkedCount = 0;
        for (var i = 0; i < newsCheckboxes.length; i++) {
            if (newsCheckboxes[i].checked) {
                checkedCount++;
            }
        }

        if (checkedCount === 0) {
            alert('请选择要删除的文章');
            return;
        }

        if (confirm('确定要删除选中的 ' + checkedCount + ' 篇文章吗？')) {
            document.getElementById('batch-form').action = 'news.php?op=batch_delete';
            document.getElementById('batch-form').submit();
        }
    });

    // 批量推荐按钮点击事件
    document.getElementById('batch-recommend-btn').addEventListener('click', function() {
        var checkedCount = getCheckedCount();
        if (checkedCount === 0) {
            alert('请选择要推荐的文章');
            return;
        }

        if (confirm('确定要推荐选中的 ' + checkedCount + ' 篇文章吗？')) {
            document.getElementById('batch-form').action = 'news.php?op=batch_recommend';
            document.getElementById('batch-form').submit();
        }
    });

    // 批量取消推荐按钮点击事件
    document.getElementById('batch-unrecommend-btn').addEventListener('click', function() {
        var checkedCount = getCheckedCount();
        if (checkedCount === 0) {
            alert('请选择要取消推荐的文章');
            return;
        }

        if (confirm('确定要取消推荐选中的 ' + checkedCount + ' 篇文章吗？')) {
            document.getElementById('batch-form').action = 'news.php?op=batch_unrecommend';
            document.getElementById('batch-form').submit();
        }
    });

    // 批量置顶按钮点击事件
    document.getElementById('batch-top-btn').addEventListener('click', function() {
        var checkedCount = getCheckedCount();
        if (checkedCount === 0) {
            alert('请选择要置顶的文章');
            return;
        }

        if (confirm('确定要置顶选中的 ' + checkedCount + ' 篇文章吗？')) {
            document.getElementById('batch-form').action = 'news.php?op=batch_top';
            document.getElementById('batch-form').submit();
        }
    });

    // 批量取消置顶按钮点击事件
    document.getElementById('batch-untop-btn').addEventListener('click', function() {
        var checkedCount = getCheckedCount();
        if (checkedCount === 0) {
            alert('请选择要取消置顶的文章');
            return;
        }

        if (confirm('确定要取消置顶选中的 ' + checkedCount + ' 篇文章吗？')) {
            document.getElementById('batch-form').action = 'news.php?op=batch_untop';
            document.getElementById('batch-form').submit();
        }
    });

    // 批量显示按钮点击事件
    document.getElementById('batch-show-btn').addEventListener('click', function() {
        var checkedCount = getCheckedCount();
        if (checkedCount === 0) {
            alert('请选择要显示的文章');
            return;
        }

        if (confirm('确定要显示选中的 ' + checkedCount + ' 篇文章吗？')) {
            document.getElementById('batch-form').action = 'news.php?op=batch_show';
            document.getElementById('batch-form').submit();
        }
    });

    // 批量隐藏按钮点击事件
    document.getElementById('batch-hide-btn').addEventListener('click', function() {
        var checkedCount = getCheckedCount();
        if (checkedCount === 0) {
            alert('请选择要隐藏的文章');
            return;
        }

        if (confirm('确定要隐藏选中的 ' + checkedCount + ' 篇文章吗？')) {
            document.getElementById('batch-form').action = 'news.php?op=batch_hide';
            document.getElementById('batch-form').submit();
        }
    });

    // 获取选中数量的辅助函数
    function getCheckedCount() {
        var count = 0;
        for (var i = 0; i < newsCheckboxes.length; i++) {
            if (newsCheckboxes[i].checked) {
                count++;
            }
        }
        return count;
    }
    
    // 初始化批量删除按钮状态
    updateBatchDeleteButton();

    // 快捷操作按钮的加载状态
    document.querySelectorAll('.info-actions .btn-light-success, .info-actions .btn-light-secondary, .info-actions .btn-light-danger').forEach(function(btn) {
        // 只处理推荐和置顶按钮（包含"推荐"或"置顶"文字的按钮）
        if (btn.textContent.trim() === '推荐' || btn.textContent.trim() === '置顶') {
            btn.addEventListener('click', function(e) {
                // 为快捷操作按钮添加加载状态
                var originalText = this.textContent;
                this.textContent = '处理中...';
                this.style.opacity = '0.6';

                // 如果操作失败，恢复原始文本
                setTimeout(function() {
                    btn.textContent = originalText;
                    btn.style.opacity = '1';
                }, 3000);
            });
        }
    });
});
</script>

        </div>
        <!-- 主内容区 (结束) -->
    </div>
    <!-- wrapper (结束) -->

    <!-- jQuery (必须在Bootstrap之前加载) -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <!-- Bootstrap JavaScript -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html> 