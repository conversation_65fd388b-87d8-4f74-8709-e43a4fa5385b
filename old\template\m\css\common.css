:root {
    --font-family: -apple-system, BlinkMacSystemFont, "PingFang SC", "Hiragino Sans GB", "Microsoft YaHei", "Helvetica Neue", Helvetica, Arial, sans-serif;
    --heading-font-weight: 500;
}

/* 移动端通用样式 */

/* 重置样式 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    margin: 0;
    padding: 0;
    background-color: #f5f5f5;
    color: #333;
    font-family: var(--font-family);
    font-size: 15px;
    line-height: 1.5;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

/* 预加载样式，防止内容晃动 */
html {
    overflow-y: scroll;
}

/* 设置图片默认占位尺寸 */
img {
    width: 100%;
    height: auto;
    /* 添加默认背景色作为占位 */
    background-color: #f0f0f0;
}

/* 对图片容器设置固定的宽高比 */
.post-image, .category-icon {
    aspect-ratio: 1/1;
    background-color: #f0f0f0;
}

/* 内容骨架屏 */
.skeleton {
    background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
    background-size: 200% 100%;
    animation: loading 1.5s infinite;
    border-radius: 4px;
}

@keyframes loading {
    0% {
        background-position: 200% 0;
    }
    100% {
        background-position: -200% 0;
    }
}

/* 页面加载状态控制 */
body:not(.content-loaded) .tab-container,
body:not(.content-loaded) .category-section,
body:not(.content-loaded) .post-list,
body:not(.content-loaded) .simple-list {
    opacity: 0.8;
}

body.content-loaded .tab-container,
body.content-loaded .category-section,
body.content-loaded .post-list,
body.content-loaded .simple-list {
    opacity: 1;
    transition: opacity 0.3s ease;
}

/* 防止图片加载时的尺寸变化 */
.post-image img {
    opacity: 0;
    transition: opacity 0.2s;
}

.post-image img.loaded {
    opacity: 1;
}

/* 防止FOUC闪烁 */
.no-fouc {
    visibility: hidden;
}

/* 防止内容变化导致的布局晃动 */
.post-title, .simple-title {
    min-height: 1.4em;
}

.container {
    width: 100%;
    padding: 0;
}

/* 页面头部基础样式 */
header {
    background-color: var(--primary-color, #ff6600);
    color: #fff;
    padding: 0;
    position: sticky;
    top: 0;
    z-index: 100;
    border-bottom: 1px solid rgba(0,0,0,0.1);
    text-align: center;
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
    height: 50px;
}

/* 统一的头部样式 */
.header-inner {
    display: flex;
    justify-content: space-between;
    align-items: center;
    position: relative;
    padding: 0;
    width: 100%;
    height: 50px;
}

.header-left {
    position: absolute;
    left: 8px;
    top: 50%;
    transform: translateY(-50%);
}

.header-back {
    position: absolute;
    left: 0px;
    top: 50%;
    transform: translateY(-50%);
    color: white;
    text-decoration: none;
    font-size: 16px;
    width: 32px;
    height: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    z-index: 5;
}

.header-back:active {
    background-color: rgba(255,255,255,0.2);
}

.header-title, .logo {
    font-size: 17px;
    font-weight: 600;
    color: #fff;
    text-align: center;
    flex: 1;
    margin: 0 35px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    position: relative;
    z-index: 1;
}

/* 简约风格头部样式 */
.header-menu-btn {
    color: #333;
    font-size: 20px;
    cursor: pointer;
    text-decoration: none;
    width: 32px;
    height: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
}

.header-menu-btn:active {
    background-color: rgba(0,0,0,0.05);
}

.header-right {
    position: absolute;
    right: 8px;
    top: 50%;
    transform: translateY(-50%);
}

.header-share, .header-search-icon {
    color: #fff;
    font-size: 16px;
    cursor: pointer;
    text-decoration: none;
    width: 32px;
    height: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
}

.header-share:active, .header-search-icon:active {
    background-color: rgba(255,255,255,0.2);
}

.section {
    margin-bottom: 0;
    background-color: #fff;
    border-radius: 0;
    border: 1px solid var(--border-color);
    overflow: hidden;
}

/* 搜索层统一样式 */
.search-layer {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    background-color: var(--primary-color);
    z-index: 101;
    display: none;
    opacity: 0;
    transform: translateY(-10px);
    transition: opacity 0.25s ease, transform 0.25s ease;
}

.search-layer.active {
    display: block;
    opacity: 1;
    transform: translateY(0);
}

.search-header {
    display: flex;
    align-items: center;
    height: 50px;
    padding: 0 15px;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.search-back {
    color: #fff;
    padding: 5px;
    margin-right: 10px;
    font-size: 16px;
    display: flex;
    align-items: center;
    text-decoration: none;
}

.search-form {
    display: flex;
    align-items: center;
    flex: 1;
    background-color: rgba(255, 255, 255, 0.15);
    border-radius: 18px;
    margin-right: 10px;
    padding: 0 15px;
    height: 36px;
}

.search-icon {
    color: rgba(255, 255, 255, 0.8);
    font-size: 16px;
    margin-right: 8px;
}

.search-loading {
    color: rgba(255, 255, 255, 0.8);
    font-size: 16px;
    margin-right: 8px;
    display: none;
}

.search-form.loading .search-icon {
    display: none;
}

.search-form.loading .search-loading {
    display: inline-block;
}

.search-input {
    flex: 1;
    background: none;
    border: none;
    color: #fff;
    padding: 0;
    font-size: 15px;
    outline: none;
    height: 36px;
}

.search-input::placeholder {
    color: rgba(255, 255, 255, 0.8);
}

.search-cancel {
    color: #fff;
    padding: 8px 5px;
    font-size: 15px;
    background: none;
    border: none;
    cursor: pointer;
    white-space: nowrap;
}

.search-content {
    background-color: #fff;
    min-height: 100vh;
    padding: 10px 15px;
}

.search-history {
    margin-bottom: 20px;
}

.search-section-title {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 10px;
    color: #999;
    font-size: 14px;
}

.search-clear {
    color: #999;
    font-size: 14px;
    background: none;
    border: none;
    padding: 5px;
    cursor: pointer;
}

.search-tags {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
}

.search-tag {
    background-color: #f5f5f5;
    color: #666;
    padding: 6px 12px;
    border-radius: 15px;
    font-size: 13px;
    text-decoration: none;
}

.section-title {
    font-size: 16px;
    padding: 12px 10px;
    color: #333;
    margin: 0;
    font-weight: 600;
    border-bottom: 1px solid #f5f5f5;
}

.section-title i {
    color: var(--primary-color);
    margin-right: 5px;
}

/* 面包屑导航 */
.breadcrumb {
    background-color: #fff !important;

    font-size: 13px;
    white-space: nowrap;
    overflow-x: auto;
    position: relative;
    padding: 10px;
    border-bottom: 1px solid #eee !important;
}

.breadcrumb::-webkit-scrollbar {
    display: none;
}

.breadcrumb .container {
    display: flex;
    align-items: center;
    padding: 0;
}

.breadcrumb a {
    color: #666 !important;
    text-decoration: none;
    transition: color 0.2s;
}

.breadcrumb a:hover {
    color: #666 !important;
    text-decoration: underline;
}

.breadcrumb .separator {
    margin: 0 8px;
    color: #ccc !important;
}

.breadcrumb .separator:after {
    content: '>';
    font-size: 12px;
}

.breadcrumb .current {
    color: #333 !important;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    max-width: 150px;
}

/* 添加发光效果当滚动到最右侧时 */
.breadcrumb::after {
    content: "";
    position: absolute;
    top: 0;
    right: 0;
    height: 100%;
    width: 25px;
    background: linear-gradient(to right, rgba(255,255,255,0), rgba(255,255,255,1)) !important;
    pointer-events: none;
}

/* 响应式：在小屏幕上调整尺寸 */
@media (max-width: 360px) {
    .breadcrumb {
        font-size: 12px;
        padding: 8px 12px;
    }
    
    .breadcrumb .separator {
        margin: 0 6px;
    }
    
    .breadcrumb .current {
        max-width: 120px;
    }
}

/* 列表样式 */
.post-list {
    padding: 0;
    background-color: #fff;
    margin-bottom: 0;
}

.post-item {
    padding: 5px 10px;
    border-bottom: 1px solid #f0f0f0;
    display: flex;
}

.post-item:last-child {
    border-bottom: none;
}

.post-image {
    width: 80px;
    height: 60px;
    margin-right: 10px;
    overflow: hidden;
    background-color: #f5f5f5;
    border-radius: 4px;
}

.post-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.post-info {
    flex: 1;
    overflow: hidden;
}

.post-title {
    font-size: 16px;
    margin-bottom: 6px;
    line-height: 1.4;
    font-weight: 400;
    letter-spacing: -0.2px;
}

.post-title a {
    color: #222;
    text-decoration: none;
}

/* 置顶信息标题颜色 */
.post-item .post-title a {
    color: #333;
    text-decoration: none;
}

.post-item.is-top {
    background-color: rgba(var(--primary-color-rgb), 0.05);
    border-left: 3px solid var(--primary-color);
}

.post-item.is-top .post-title a {
    color: var(--accent-color);
    font-weight: bold;
}

.post-meta {
    color: #999;
    font-size: 12px;
    display: flex;
    flex-wrap: wrap;
    font-weight: 400;
    letter-spacing: 0.2px;
}

.post-meta div {
    margin-right: 10px;
}

.top-tag {
    display: inline-block;
    padding: 1px 3px;
    background-color: var(--primary-color);
    color: #fff;
    font-size: 10px;
    border-radius: 2px;
    margin-right: 3px;
    vertical-align: middle;
}

/* 简洁列表样式 */
.simple-list {
    padding: 0;
    background-color: #fff;
    margin-bottom: 0;
    font-family: system-ui, -apple-system, 'Microsoft YaHei', 'PingFang SC', sans-serif;
}

.simple-item {
    padding: 7px 10px;
    border-bottom: 1px solid #f0f0f0;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.simple-item:hover {
    background-color: #f9f9f9;
}

.simple-item:last-child {
    border-bottom: none;
}

.simple-title {
    font-size: 16px;
    flex: 1;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    font-weight: 400;
    letter-spacing: -0.2px;
}

.simple-title a {
    color: #222;
    text-decoration: none;
}

.simple-title a:hover {
    color: var(--primary-color);
}

/* 置顶信息标题颜色 */
.simple-item.is-top {
    background-color: rgba(var(--primary-color-rgb), 0.05);
    border-left: 3px solid var(--primary-color);
}

.simple-item.is-top .simple-title a {
    color: var(--accent-color);
    font-weight: bold;
}

.simple-time, .simple-meta {
    color: #999;
    font-size: 12px;
    margin-left: 10px;
    white-space: nowrap;
}

.top-dot {
    display: inline-block;
    width: 6px;
    height: 6px;
    background-color: var(--primary-color);
    border-radius: 50%;
    margin-right: 5px;
    vertical-align: middle;
}

/* 选项卡样式 */
.tab-container {
    background-color: #fff;
    margin-bottom: 0;
}

.tab-header {
    display: flex;
    border-bottom: 1px solid #f0f0f0;
}

.tab-item {
    flex: 0 0 auto;
    text-align: center;
    padding: 12px 20px;
    font-size: 16px;
    cursor: pointer;
    position: relative;
    color: #666;
}

.tab-item.active {
    color: var(--primary-color);
    font-weight: 500;
}

.tab-item.active:after {
    content: "";
    position: absolute;
    bottom: 0;
    left: 25%;
    width: 50%;
    height: 2px;
    background-color: var(--primary-color);
}

.tab-content {
    display: none;
}

.tab-content.active {
    display: block;
}

/* 底部样式 */
footer {
    background-color: #fff;
    padding: 10px 0 65px;
    color: #999;
    text-align: center;
    font-size: 12px;

    border-top: 1px solid #f0f0f0;
}

.footer-nav {
    display: flex;
    justify-content: center;
    padding: 10px 0;
    border-bottom: 1px solid #f0f0f0;
}

.footer-nav a {
    color: #666;
    text-decoration: none;
    padding: 0 15px;
    position: relative;
}

.footer-nav a:not(:last-child):after {
    content: "";
    position: absolute;
    right: 0;
    top: 50%;
    transform: translateY(-50%);
    width: 1px;
    height: 12px;
    background-color: #ddd;
}

.footer-info {
    padding: 10px 0;
    line-height: 1.8;
    color: #999;
}

.no-data {
    text-align: center;
    padding: 20px;
    color: #999;
    font-size: 14px;
}

/* 底部导航栏 */
.navbar {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    background-color: #fff;
    display: flex;
    border-top: 1px solid var(--border-color);
    z-index: 100;
    padding: 8px 0 5px;
    height: 60px;
}

.nav-item {
    flex: 1;
    text-align: center;
    padding: 2px 0;
    color: #8a8a8a;
    text-decoration: none;
    font-size: 11px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    position: relative;
    transition: all 0.3s ease;
}

.nav-icon {
    display: block;
    font-size: 20px;
    margin-bottom: 3px;
    line-height: 1;
    transition: transform 0.2s ease;
}

.nav-item:not(.publish) .nav-icon {
    height: 22px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.nav-item.active {
    color: var(--primary-color);
    font-weight: 500;
}

.nav-item.active .nav-icon {
    transform: scale(1.1);
    color: var(--primary-color);
}

.nav-item.publish {
    margin-top: -24px;
    position: relative;
}

.nav-item.publish .nav-icon {
    display: flex;
    justify-content: center;
    align-items: center;
    width: 44px;
    height: 44px;
    background-color: var(--primary-color, #ff6600);
    border-radius: 50%;
    color: #fff;
    font-size: 18px;
    border: 1px solid rgba(255,255,255,0.1);
    margin: 0 auto 5px;
}

.nav-item:active .nav-icon {
    transform: scale(0.9);
}

.nav-item.publish:active .nav-icon {
    transform: scale(0.95);
    box-shadow: 0 1px 4px rgba(255, 102, 0, 0.3);
}

.nav-text {
    font-size: 13px;
    color: #999;
    margin-top: 2px;
    transition: color 0.3s ease;
}

.nav-item.publish .nav-text {
    margin-top: 2px;
    font-weight: 500;
}

/* 分页样式 */
.pagination {
    background-color: #fff;
    padding: 10px 10px;
    text-align: center;
    margin-bottom: 0;
}

.pagination a, .pagination span {
    display: inline-block;
    padding: 6px 12px;
    margin: 0 3px;
    border: 1px solid #eee;
    border-radius: 4px;
    text-decoration: none;
    color: var(--text-color);
    font-size: 14px;
    min-width: 36px;
    transition: all 0.3s ease;
}

.pagination a:hover {
    border-color: var(--primary-color);
    color: var(--primary-color);
    background-color: rgba(var(--primary-color-rgb), 0.05);
}

.pagination .current {
    background-color: var(--primary-color);
    color: #fff;
    border-color: var(--primary-color);
}

.pagination .disabled {
    color: #ccc;
    cursor: not-allowed;
    border-color: #f5f5f5;
    background-color: #fafafa;
}

/* 消息提示页面的footer样式 */
.message-page footer {
    text-align: center;
    padding: 15px 0;
    color: #999;
    font-size: 12px;
    position: absolute;
    bottom: 0;
    width: 100%;
    background: none;
    border-top: none;
    margin-top: 0;
}

/* 标题样式调整 */
h1, h2, h3, h4, h5, h6 {
    font-family: var(--font-family);
    font-weight: var(--heading-font-weight);
    color: #333;
}

/* 文章标题样式 */
.post-title, .simple-title {
    font-family: var(--font-family);
    font-weight: 400;
    letter-spacing: -0.2px;
    color: #222;
}

/* 调整导航和标签字体 */
.tab-item, .nav-text, .category-name {
    font-family: var(--font-family);
}