<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <title>信息置顶 - <?php echo $post['title']; ?></title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        body {
            font-family: 'Microsoft YaHei', sans-serif;
            line-height: 1.5;
            color: #333;
            background-color: #f5f5f5;
            font-size: 14px;
        }
        header {
            background-color: #ff6600;
            padding: 15px 0;
            color: #fff;
            text-align: center;
            position: relative;
            width: 100%;
        }
        .header-back {
            position: absolute;
            left: 15px;
            top: 50%;
            transform: translateY(-50%);
            color: #fff;
            text-decoration: none;
            font-size: 16px;
        }
        .logo {
            font-size: 18px;
            font-weight: bold;
        }
        .breadcrumb {
            background-color: #fff;
            padding: 12px 15px;
            margin-bottom: 0;
            font-size: 13px;
            white-space: nowrap;
            overflow-x: auto;
            width: 100%;
            border-bottom: 1px solid #eee;
        }
        .breadcrumb a {
            color: #666;
            text-decoration: none;
        }
        .breadcrumb span {
            margin: 0 5px;
            color: #999;
        }
        .post-meta {
            background-color: white;
            padding: 15px;
            width: 100%;
        }
        .post-title {
            font-size: 16px;
            font-weight: 600;
            margin-bottom: 10px;
            color: #333;
        }
        .post-info {
            display: flex;
            flex-wrap: wrap;
            font-size: 13px;
            color: #666;
        }
        .post-info-item {
            margin-right: 15px;
            margin-bottom: 5px;
        }
        .form-section {
            background-color: white;
            padding: 15px;
            margin-top: 10px;
            width: 100%;
        }
        .form-title {
            font-size: 16px;
            margin-bottom: 15px;
            font-weight: 600;
        }
        .form-group {
            margin-bottom: 15px;
        }
        .form-group label {
            display: block;
            margin-bottom: 8px;
            font-weight: 500;
            color: #333;
        }
        .radio-group {
            margin-bottom: 10px;
        }
        .radio-option {
            display: block;
            margin-bottom: 10px;
            padding: 10px 12px;
            background-color: #f9f9f9;
            border: 1px solid #eee;
            border-radius: 4px;
        }
        .radio-option input[type="radio"] {
            margin-right: 8px;
        }
        .form-control {
            width: 100%;
            padding: 12px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
        }
        .form-control:focus {
            outline: none;
            border-color: #ff6600;
        }
        .btn-section {
            background-color: white;
            padding: 15px;
            margin-top: 10px;
            width: 100%;
        }
        .btn {
            display: block;
            width: 100%;
            padding: 12px 0;
            background-color: #ff6600;
            color: #fff;
            border: none;
            border-radius: 4px;
            text-align: center;
            font-size: 15px;
            font-weight: 500;
            cursor: pointer;
            text-decoration: none;
            margin-bottom: 10px;
        }
        .btn-secondary {
            background-color: #f0f0f0;
            color: #666;
            border: 1px solid #ddd;
        }
        .bottom-nav {
            position: fixed;
            bottom: 0;
            left: 0;
            width: 100%;
            height: 56px;
            background: white;
            display: flex;
            box-shadow: 0 -2px 10px rgba(0,0,0,0.08);
            z-index: 100;
        }
        .nav-item {
            flex: 1;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            color: #666;
            text-decoration: none;
            font-size: 12px;
            font-weight: 500;
        }
        .nav-item.active {
            color: #ff6600;
        }
    </style>
</head>
<body>
    <header>
        <a href="javascript:history.back();" class="header-back"><i class="fas fa-arrow-left"></i></a>
        <div class="logo">信息置顶</div>
    </header>

    <div class="breadcrumb">
        <a href="/">首页</a>
        <span>&gt;</span>
        <a href="/<?php echo $post['category_pinyin']; ?>/"><?php echo $post['category_name']; ?></a>
        <span>&gt;</span>
        <span>置顶</span>
    </div>

    <div class="post-meta">
        <h2 class="post-title"><?php echo $post['title']; ?></h2>
        <div class="post-info">
            <div class="post-info-item">分类：<?php echo $post['category_name']; ?></div>
            <?php if (!empty($post['region_name'])): ?>
            <div class="post-info-item">区域：<?php echo $post['region_name']; ?></div>
            <?php endif; ?>
            <div class="post-info-item">发布时间：<?php echo friendlyTime($post['created_at']); ?></div>
        </div>
    </div>
    
    <form action="/top.php?id=<?php echo $post['id']; ?>" method="post">
        <div class="form-section">
            <h3 class="form-title">置顶设置</h3>
            
            <div class="form-group">
                <label>置顶类型</label>
                <div class="radio-group">
                    <label class="radio-option">
                        <input type="radio" name="top_type" value="0" checked> 取消置顶
                    </label>
                    <label class="radio-option">
                        <input type="radio" name="top_type" value="1"> 栏目置顶
                    </label>
                    <label class="radio-option">
                        <input type="radio" name="top_type" value="2"> 首页置顶
                    </label>
                </div>
            </div>
            
            <div class="form-group">
                <label for="top_days">置顶天数</label>
                <select class="form-control" id="top_days" name="top_days">
                    <option value="1">1天</option>
                    <option value="3">3天</option>
                    <option value="7">7天</option>
                    <option value="15">15天</option>
                    <option value="30">30天</option>
                </select>
            </div>
        </div>
        
        <div class="form-section">
            <div class="form-group">
                <label for="password">管理密码</label>
                <input type="password" class="form-control" id="password" name="password" required placeholder="请输入发布时设置的管理密码">
            </div>
        </div>
        
        <div class="btn-section">
            <input type="submit" name="submit" class="btn" value="确定置顶">
            <a href="/<?php echo $post['category_pinyin']; ?>/<?php echo $post['id']; ?>.html" class="btn btn-secondary">返回详情</a>
        </div>
    </form>
    
    <div style="height: 65px;"></div><!-- 底部导航占位 -->

    <script>
        // 当置顶类型变化时
        document.querySelectorAll('input[name="top_type"]').forEach(function(radio) {
            radio.addEventListener('change', function() {
                var topDaysSelect = document.getElementById('top_days');
                // 如果选择"取消置顶"，禁用天数选择
                if(this.value === "0") {
                    topDaysSelect.disabled = true;
                } else {
                    topDaysSelect.disabled = false;
                }
            });
        });
        
        // 页面加载时检查初始状态
        window.addEventListener('DOMContentLoaded', function() {
            if(document.querySelector('input[name="top_type"]:checked').value === "0") {
                document.getElementById('top_days').disabled = true;
            }
        });
    </script>

    <div class="bottom-nav">
        <a href="/" class="nav-item">
            首页
        </a>
        <a href="/<?php echo $post['category_pinyin']; ?>/" class="nav-item">
            分类
        </a>
        <a href="/post.php" class="nav-item">
            发布
        </a>
        <a href="/search.php" class="nav-item">
            搜索
        </a>
        <a href="/manage.php?id=<?php echo $post['id']; ?>" class="nav-item active">
            管理
        </a>
    </div>
</body>
</html> 