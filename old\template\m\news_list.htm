<!DOCTYPE html>
<?php
// 获取主题设置
$theme = isset($_COOKIE['site_theme']) ? $_COOKIE['site_theme'] : 'red';
$theme_class = 'theme-' . $theme;
?>
<html lang="zh-CN" class="<?php echo $theme_class; ?>">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <title>{if $category_info}{$category_info.catname} - {/if}新闻中心 - {$site_name}</title>
    <meta name="keywords" content="{if $category_info}{$category_info.catname},{/if}新闻,资讯,{$site_name}" />
    <meta name="description" content="{if $category_info}{$category_info.catname}新闻列表{else}新闻中心{/if} - {$site_name}" />

    <link rel="stylesheet" href="/static/font-awesome/css/all.min.css">
    <link rel="stylesheet" href="/static/css/themes.css?v=<?php echo time(); ?>">
    <link rel="stylesheet" href="/template/m/css/common.css?v=<?php echo time(); ?>">
    <script src="/static/js/common.js"></script>
    <script src="/static/js/page-stabilizer.js"></script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
            background: #f8f9fa;
            min-height: 100vh;
            color: #333;
            margin: 0;
            padding: 0;
        }
        
        .back-btn {
            color: white;
            font-size: 20px;
            text-decoration: none;
            margin-right: 15px;
            padding: 5px;
        }
        
        .app-title {
            font-size: 18px;
            font-weight: 600;
            flex: 1;
        }
        
        .header-action {
            color: white;
            font-size: 18px;
            text-decoration: none;
            padding: 5px;
        }

        /* 面包屑导航 */
        .breadcrumb {
            background: white;
            padding: 10px 0;
            border-bottom: 1px solid #f0f0f0;
            font-size: 14px;
        }

        .breadcrumb .container {
            padding: 0 20px;
        }

        .breadcrumb a {
            color: #666;
            text-decoration: none;
        }

        .breadcrumb a:hover {
            color: #333;
        }

        .breadcrumb .separator {
            margin: 0 8px;
            color: #999;
        }

        .breadcrumb .current {
            color: #333;
            font-weight: 500;
        }

        /* 分类标签 */
        .category-tabs {
            background: white;
            padding: 15px 0 10px;
            border-bottom: 1px solid #f0f0f0;
        }
        
        .tabs-scroll {
            display: flex;
            overflow-x: auto;
            padding: 0 20px;
            gap: 15px;
            scrollbar-width: none;
            -ms-overflow-style: none;
        }
        
        .tabs-scroll::-webkit-scrollbar {
            display: none;
        }
        
        .tab-item {
            text-decoration: none;
            color: #666;
            font-size: 15px;
            font-weight: 500;
            white-space: nowrap;
            padding: 8px 16px;
            border-radius: 20px;
            background: #f8f9fa;
            transition: all 0.3s ease;
            position: relative;
        }
        
        .tab-item.active {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
        }
        
        /* 新闻列表 */
        .news-list {
            background: white;
            padding: 0 8px; /* 减少两侧边距 */
        }
        
        .news-card {
            background: white;
            margin-bottom: 1px;
            border-bottom: 1px solid #f0f0f0;
            transition: all 0.3s ease;
            position: relative;
        }

        .news-card:hover {
            background: #f8f9fa;
        }
        
        .news-card-content {
            padding: 12px 8px; /* 减少内边距 */
        }
        
        /* 标题和时间在一行 */
        .news-item-row {
            display: flex;
            justify-content: space-between;
            align-items: center;
            gap: 10px;
        }

        .news-title {
            font-size: 15px;
            font-weight: 500;
            line-height: 1.4;
            color: #333;
            flex: 1;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap; /* 单行显示 */
        }

        .news-time {
            color: #999;
            font-size: 12px;
            white-space: nowrap;
            flex-shrink: 0;
        }
        
        .news-tags {
            display: flex;
            gap: 6px;
            margin-top: 8px;
        }
        
        .tag {
            padding: 4px 8px;
            font-size: 11px;
            border-radius: 12px;
            font-weight: 500;
            color: white;
        }
        
        .tag.top {
            background: linear-gradient(135deg, #ff6b6b, #ee5a52);
        }
        
        .tag.rec {
            background: linear-gradient(135deg, #feca57, #ff9ff3);
        }
        

        
        /* 空状态 */
        .empty-state {
            text-align: center;
            padding: 60px 20px;
            color: #999;
        }
        
        .empty-icon {
            font-size: 48px;
            margin-bottom: 16px;
            color: #ddd;
        }
        
        .empty-text {
            font-size: 16px;
            margin-bottom: 8px;
        }
        
        .empty-desc {
            font-size: 14px;
            color: #bbb;
        }
        
        /* 分页 */
        .pagination {
            padding: 20px;
            text-align: center;
        }
        
        .load-more {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 12px 30px;
            border-radius: 25px;
            font-size: 15px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .load-more:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
        }
        
        /* 响应式 */
        @media (max-width: 480px) {
            .app-header {
                padding: 12px 15px;
            }
            
            .app-title {
                font-size: 16px;
            }
            
            .news-list {
                padding: 0 5px; /* 小屏幕更小的边距 */
            }

            .news-card-content {
                padding: 10px 6px; /* 小屏幕更小的内边距 */
            }

            .news-title {
                font-size: 14px;
            }
        }
    </style>
</head>
<body>
    <header>
        <div class="header-inner">
            <div class="header-left">
                <a href="javascript:history.back();" class="header-back">
                    <i class="fas fa-chevron-left"></i>
                </a>
            </div>
            <div class="header-title">{if $category_info}{$category_info.catname}{else}新闻中心{/if}</div>
            <div class="header-right">
                <!-- 右侧可以为空 -->
            </div>
        </div>
    </header>

    <!-- 面包屑导航 -->
    <div class="breadcrumb">
        <div class="container">
            <a href="/">首页</a>
            <span class="separator">></span>
            <a href="/news/">新闻中心</a>
            {if $category_info}
            <span class="separator">></span>
            <span class="current">{$category_info.catname}</span>
            {/if}
        </div>
    </div>

    <!-- 分类标签 -->
    {if $news_categories}
    <div class="category-tabs">
        <div class="tabs-scroll">
            <a href="/news/" class="tab-item {if !$current_catid}active{/if}">
                <span>全部</span>
            </a>
            {loop $news_categories $category}
            <a href="/news/{$category.pinyin}/" class="tab-item {if $current_catid == $category.catid}active{/if}">
                <span>{$category.catname}</span>
            </a>
            {/loop}
        </div>
    </div>
    {/if}

        <!-- 新闻列表 -->
        <div class="news-list">
            {if $news_list}
            {loop $news_list $news}
            <div class="news-card">
                <a href="/news/{$news.id}.html" style="text-decoration: none; color: inherit;">
                    <div class="news-card-content">
                        <div class="news-item-row">
                            <div class="news-title">{$news.title}</div>
                            <div class="news-time">
                                <?php echo date('m-d H:i', $news['addtime']); ?>
                            </div>
                        </div>
                        {if (isset($news.is_top) && $news.is_top) || (isset($news.is_recommend) && $news.is_recommend)}
                        <div class="news-tags">
                            {if isset($news.is_top) && $news.is_top}<span class="tag top">置顶</span>{/if}
                            {if isset($news.is_recommend) && $news.is_recommend}<span class="tag rec">推荐</span>{/if}
                        </div>
                        {/if}
                    </div>
                </a>
            </div>
            {/loop}
            {else}
            <div class="empty-state">
                <div class="empty-icon">
                    <i class="far fa-newspaper"></i>
                </div>
                <div class="empty-text">暂无新闻</div>
                <div class="empty-desc">当前分类下还没有发布新闻</div>
            </div>
            {/if}
        </div>

        <!-- 分页 -->
        {if $pagebar}
        <div class="pagination">
            {$pagebar}
        </div>
        {/if}



    {include file="footer.htm"}

    <!-- 底部导航栏 -->
    {include file="navbar.htm"}
</body>
</html>
