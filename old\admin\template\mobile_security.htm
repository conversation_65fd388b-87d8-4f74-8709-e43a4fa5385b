{include file="header.htm"}

<div class="d-flex justify-content-between align-items-center mb-1">
    <h5 class="mb-0">手机号监控 <small class="text-muted">(限制: {$mobile_daily_limit}条/日)</small></h5>
    <div class="d-flex align-items-center">
        <input type="text" class="form-control form-control-sm me-1" id="reset-mobile" placeholder="手机号" style="width:120px;">
        <button class="btn btn-warning btn-sm me-2" onclick="resetMobileLimit()">重置</button>
        <a href="setting.php" class="btn btn-sm btn-outline-secondary">设置</a>
    </div>
</div>

<div class="table-responsive">
    <table class="table table-sm table-striped table-bordered">
        <thead class="table-dark">
            <tr>
                <th width="15%">手机号</th>
                <th width="20%">发布时间</th>
                <th width="15%">IP地址</th>
                <th width="10%">发布数</th>
                <th width="12%">时间差异</th>
                <th width="10%">状态</th>
                <th width="8%">操作</th>
            </tr>
        </thead>
        <tbody>
            {foreach from=$records item=record}
            <tr>
                <td class="fw-bold">{$record.mobile}</td>
                <td><small class="text-muted">{$record.formatted_time}</small></td>
                <td><small>{$record.ip_address}</small></td>
                <td class="text-center">
                    <span class="badge {if $record.post_count >= $mobile_daily_limit}bg-danger{else}bg-success{/if}">
                        {$record.post_count}/{$mobile_daily_limit}
                    </span>
                </td>
                <td class="text-center">
                    <span class="badge bg-{$record.time_diff_status}">{$record.time_diff_text}</span>
                </td>
                <td class="text-center">
                    {if $record.post_count >= $mobile_daily_limit}
                        <span class="badge bg-danger">限制</span>
                    {else}
                        <span class="badge bg-success">正常</span>
                    {/if}
                </td>
                <td class="text-center">
                    <button class="btn btn-xs btn-warning" onclick="resetMobileLimit('{$record.mobile}')" style="font-size:11px;padding:2px 6px;">重置</button>
                </td>
            </tr>
            {foreachelse}
            <tr>
                <td colspan="7" class="text-center text-muted py-3">暂无记录</td>
            </tr>
            {/foreach}
        </tbody>
    </table>
</div>

<!-- 简约分页 -->
{if $total_pages > 1}
<div class="d-flex justify-content-center mt-2">
    <nav>
        <ul class="pagination pagination-sm">
            {if $current_page > 1}
            <li class="page-item">
                <a class="page-link" href="?page={$current_page-1}">上一页</a>
            </li>
            {/if}

            {for $i=1; $i<=$total_pages; $i++}
            <li class="page-item {if $i == $current_page}active{/if}">
                <a class="page-link" href="?page={$i}">{$i}</a>
            </li>
            {/for}

            {if $current_page < $total_pages}
            <li class="page-item">
                <a class="page-link" href="?page={$current_page+1}">下一页</a>
            </li>
            {/if}
        </ul>
    </nav>
</div>
{/if}

<script>
function resetMobileLimit(mobile) {
    if (!mobile) {
        mobile = document.getElementById('reset-mobile').value.trim();
    }
    
    if (!mobile) {
        alert('请输入手机号');
        return;
    }
    
    if (!confirm('确定要重置手机号 ' + mobile + ' 的发布限制吗？')) {
        return;
    }
    
    $.post('mobile_security.php', {
        action: 'reset_mobile_limit',
        mobile: mobile
    }, function(response) {
        if (response.success) {
            alert('重置成功');
            location.reload();
        } else {
            alert('重置失败: ' + response.message);
        }
    }, 'json');
}
</script>

{include file="footer.htm"}
