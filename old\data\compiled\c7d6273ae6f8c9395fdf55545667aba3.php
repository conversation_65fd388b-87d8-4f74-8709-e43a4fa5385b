<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php if(null !== ($page_title ?? null)): ?><?php echo $page_title ?? ""; ?> - <?php endif; ?>分类信息网站后台管理</title>
    <link href="../static/font-awesome/css/all.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="static/css/admin_clean.css?v=<?php echo time(); ?>" rel="stylesheet">
    <link href="static/css/pagination.css" rel="stylesheet">
    <link href="../static/css/image-compress.css" rel="stylesheet">
</head>
<body>
    <div class="wrapper" id="wrapper">
        <!-- 侧边栏 -->
        <div class="sidebar" id="sidebar">
            <div class="sidebar-header">
                <a href="index.php" class="logo">
                    <i class="fas fa-cube"></i>
                    <span>管理系统</span>
                </a>
            </div>
            <!-- 侧边栏菜单 -->
<div class="menu-item <?php if($current_page == 'index'): ?>active<?php endif; ?>">
    <a href="index.php">
        <i class="fas fa-home"></i>
        <span>控制面板</span>
    </a>
</div>

<div class="menu-item <?php if($current_page == 'category'): ?>active<?php endif; ?>">
    <a href="category.php">
        <i class="fas fa-list"></i>
        <span>分类管理</span>
    </a>
</div>

<div class="menu-item <?php if($current_page == 'region'): ?>active<?php endif; ?>">
    <a href="region.php">
        <i class="fas fa-map-marker-alt"></i>
        <span>区域管理</span>
    </a>
</div>

<div class="menu-item <?php if($current_page == 'info'): ?>active<?php endif; ?>">
    <a href="info.php">
        <i class="fas fa-file-alt"></i>
        <span>信息管理</span>
    </a>
</div>

<div class="menu-item <?php if($current_page == 'news_category'): ?>active<?php endif; ?>">
    <a href="news_category.php">
        <i class="fas fa-newspaper"></i>
        <span>新闻栏目</span>
    </a>
</div>

<div class="menu-item <?php if($current_page == 'news'): ?>active<?php endif; ?>">
    <a href="news.php">
        <i class="fas fa-edit"></i>
        <span>新闻管理</span>
    </a>
</div>

<div class="menu-item <?php if($current_page == 'pages'): ?>active<?php endif; ?>">
    <a href="pages.php">
        <i class="fas fa-file-alt"></i>
        <span>单页管理</span>
    </a>
</div>

<div class="menu-item <?php if($current_page == 'links'): ?>active<?php endif; ?>">
    <a href="links.php">
        <i class="fas fa-link"></i>
        <span>友情链接</span>
    </a>
</div>

<div class="menu-item <?php if($current_page == 'report'): ?>active<?php endif; ?>">
    <a href="report.php">
        <i class="fas fa-flag"></i>
        <span>举报管理</span>
    </a>
</div>

<div class="menu-item <?php if($current_page == 'admin'): ?>active<?php endif; ?>">
    <a href="admin.php">
        <i class="fas fa-user-shield"></i>
        <span>管理员管理</span>
    </a>
</div>

<div class="menu-item <?php if($current_page == 'operation_logs'): ?>active<?php endif; ?>">
    <a href="operation_logs.php">
        <i class="fas fa-history"></i>
        <span>操作日志</span>
    </a>
</div>

<div class="menu-item <?php if($current_page == 'mobile_security'): ?>active<?php endif; ?>">
    <a href="mobile_security.php">
        <i class="fas fa-shield-alt"></i>
        <span>手机号安全</span>
    </a>
</div>

<div class="menu-item <?php if($current_page == 'setting'): ?>active<?php endif; ?>">
    <a href="setting.php">
        <i class="fas fa-cog"></i>
        <span>系统设置</span>
    </a>
</div>

<div class="menu-item <?php if($current_page == 'cache_manager'): ?>active<?php endif; ?>">
    <a href="cache_manager.php">
        <i class="fas fa-memory"></i>
        <span>缓存管理</span>
    </a>
</div>



<div class="menu-item <?php if($current_page == 'db_backup'): ?>active<?php endif; ?>">
    <a href="db_backup.php">
        <i class="fas fa-database"></i>
        <span>数据库备份</span>
    </a>
</div>
        </div>

        <!-- 顶部导航 -->
<div class="top-nav">
    <div class="nav-left">
        <div class="toggle-sidebar" id="toggle-sidebar">
            <i class="fas fa-bars"></i>
        </div>
        <div class="breadcrumb">
            <span class="admin-badge"><?php echo (isset($admin['username'])) ? $admin['username'] : ""; ?></span>
            <i class="fas fa-chevron-right"></i>
            <span>控制台</span>
            <?php if(null !== ($breadcrumb ?? null)): ?>
            <i class="fas fa-chevron-right"></i>
            <span><?php echo $breadcrumb ?? ""; ?></span>
            <?php endif; ?>
        </div>
    </div>
    <div class="nav-right">
        <div class="nav-item" id="clear-cache-btn" title="清理所有缓存">
            <i class="fas fa-trash-alt"></i>
        </div>
        <div class="nav-item" title="前台首页">
            <a href="../" target="_blank" style="color:inherit;text-decoration:none;">
                <i class="fas fa-home"></i>
            </a>
        </div>
        <div class="user-item">
            <div class="user-avatar"><i class="fas fa-user"></i></div>
            <span class="user-name"><?php echo (isset($admin['username'])) ? $admin['username'] : ""; ?></span>
            <a href="logout.php" class="logout-link" title="退出登录">
                <i class="fas fa-sign-out-alt"></i>
            </a>
        </div>
    </div>
</div>

<!-- 清理缓存功能的遮罩层和对话框 -->
<div id="cache-overlay" style="display:none; position:fixed; top:0; left:0; width:100%; height:100%; background:rgba(0,0,0,0.5); z-index:2000;"></div>
<div id="cache-modal" style="display:none; position:fixed; top:50%; left:50%; transform:translate(-50%,-50%); background:#fff; border-radius:8px; box-shadow:0 4px 20px rgba(0,0,0,0.2); width:300px; padding:20px; z-index:2001;">
    <div style="margin-bottom:15px; font-size:16px; font-weight:600;">确认清理缓存</div>
    <p style="margin-bottom:20px; font-size:14px; color:#666;">此操作将清理所有缓存，包括：</p>
    <ul style="margin-bottom:20px; padding-left:20px; font-size:14px; color:#666;">
        <li>页面缓存</li>
        <li>数据缓存</li>
        <li>模板编译文件</li>
    </ul>
    <div style="display:flex; justify-content:flex-end; gap:10px;">
        <button id="cancel-clear-cache" style="padding:8px 16px; border:1px solid #ddd; background:#fff; border-radius:4px; cursor:pointer;">取消</button>
        <button id="confirm-clear-cache" style="padding:8px 16px; border:none; background:#dc3545; color:#fff; border-radius:4px; cursor:pointer;">确认清理</button>
    </div>
</div>

<!-- 成功提示框 -->
<div id="success-toast" style="display:none; position:fixed; top:50%; left:50%; transform:translate(-50%,-50%); background:#28a745; color:#fff; padding:15px 25px; border-radius:6px; box-shadow:0 4px 12px rgba(0,0,0,0.15); z-index:2002; font-size:14px;">
    <i class="fas fa-check-circle" style="margin-right:8px;"></i>
    缓存清理成功！
</div>

<script>
    document.addEventListener('DOMContentLoaded', function() {
        const clearCacheBtn = document.getElementById('clear-cache-btn');
        const cacheOverlay = document.getElementById('cache-overlay');
        const cacheModal = document.getElementById('cache-modal');
        const cancelClearCache = document.getElementById('cancel-clear-cache');
        const confirmClearCache = document.getElementById('confirm-clear-cache');
        const successToast = document.getElementById('success-toast');
        
        if (clearCacheBtn && cacheOverlay && cacheModal) {
            clearCacheBtn.addEventListener('click', function() {
                cacheOverlay.style.display = 'block';
                cacheModal.style.display = 'block';
            });
            
            cancelClearCache.addEventListener('click', function() {
                cacheOverlay.style.display = 'none';
                cacheModal.style.display = 'none';
            });
            
            cacheOverlay.addEventListener('click', function() {
                cacheOverlay.style.display = 'none';
                cacheModal.style.display = 'none';
            });
            
            confirmClearCache.addEventListener('click', function() {
                // 发送清理缓存请求
                const xhr = new XMLHttpRequest();
                xhr.open('POST', 'cache_manager.php', true);
                xhr.setRequestHeader('Content-Type', 'application/x-www-form-urlencoded');
                
                confirmClearCache.innerHTML = '清理中...';
                confirmClearCache.disabled = true;
                
                xhr.onreadystatechange = function() {
                    if (xhr.readyState === 4) {
                        cacheOverlay.style.display = 'none';
                        cacheModal.style.display = 'none';
                        
                        if (xhr.status === 200) {
                            // 显示成功提示
                            successToast.style.display = 'block';
                            successToast.style.transform = 'translate(-50%, -50%) scale(0.8)';
                            
                            setTimeout(function() {
                                successToast.style.transform = 'translate(-50%, -50%) scale(1)';
                            }, 100);
                            
                            setTimeout(function() {
                                successToast.style.transform = 'translate(-50%, -50%) scale(0.8)';
                                setTimeout(function() {
                                    successToast.style.display = 'none';
                                    successToast.style.transform = 'translate(-50%, -50%) scale(1)';
                                }, 300);
                            }, 2000);
                        }
                        
                        confirmClearCache.disabled = false;
                        confirmClearCache.innerHTML = '确认清理';
                    }
                };
                
                xhr.send('action=clear_cache&type=all');
            });
        }
    });
</script>


        <!-- 主内容区 (开始) -->
        <div class="main-content">

<script>
    document.addEventListener('DOMContentLoaded', function() {
        // 侧边栏折叠功能
        const toggleSidebar = document.getElementById('toggle-sidebar');
        const sidebar = document.getElementById('sidebar');
        const wrapper = document.getElementById('wrapper');
        
        if (toggleSidebar && sidebar && wrapper) {
            toggleSidebar.addEventListener('click', function() {
                sidebar.classList.toggle('collapsed');
                wrapper.classList.toggle('collapsed');
                localStorage.setItem('sidebar_collapsed', sidebar.classList.contains('collapsed'));
            });
            
            // 恢复侧边栏状态
            const isCollapsed = localStorage.getItem('sidebar_collapsed') === 'true';
            if (isCollapsed) {
                sidebar.classList.add('collapsed');
                wrapper.classList.add('collapsed');
            }
        }
        
        // 设置当前页面的菜单项为激活状态
        const currentPath = window.location.pathname;
        const menuItems = document.querySelectorAll('.menu-item a');
        
        menuItems.forEach(function(item) {
            const href = item.getAttribute('href');
            if (href) {
                // 检查完整路径匹配
                if (currentPath.endsWith(href)) {
                    const menuItem = item.closest('.menu-item');
                    if (menuItem) {
                        menuItem.classList.add('active');
                    }
                    
                    foundActive = true;
                }
            }
        });
        
        // 如果没有找到完全匹配的，尝试部分匹配
        if (!foundActive) {
            const pathParts = currentPath.split('/');
            const filename = pathParts[pathParts.length - 1];
            
            if (filename) {
                menuItems.forEach(function(item) {
                    const href = item.getAttribute('href');
                    if (href && href.includes(filename.split('.')[0])) {
                        const menuItem = item.closest('.menu-item');
                        if (menuItem) {
                            menuItem.classList.add('active');
                        }
                    }
                });
            }
        }
        
        // 保存当前激活的菜单项
        menuItems.forEach(function(item) {
            item.addEventListener('click', function() {
                const href = this.getAttribute('href');
                if (href) {
                    localStorage.setItem('active_menu_item', href);
                }
            });
        });
    });

    // 处理URL参数中的错误和成功消息
    function handleUrlMessages() {
        const urlParams = new URLSearchParams(window.location.search);
        const message = urlParams.get('message');
        const error = urlParams.get('error');

        if (message) {
            showSuccessMessage(message);
            // 清除URL中的message参数
            clearUrlParameter('message');
        }

        if (error) {
            showErrorMessage(error);
            // 清除URL中的error参数
            clearUrlParameter('error');
        }
    }

    // 显示成功消息
    function showSuccessMessage(message) {
        const alertHtml = `
            <div class="alert alert-success alert-dismissible fade show" role="alert" style="position: fixed; top: 70px; right: 20px; z-index: 9999; min-width: 300px; box-shadow: 0 4px 12px rgba(0,0,0,0.15);">
                <i class="fas fa-check-circle me-2"></i>
                <span>${message}</span>
                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
            </div>
        `;
        document.body.insertAdjacentHTML('beforeend', alertHtml);

        // 5秒后自动消失
        setTimeout(() => {
            const alert = document.querySelector('.alert-success');
            if (alert) {
                alert.remove();
            }
        }, 5000);
    }

    // 显示错误消息
    function showErrorMessage(message) {
        const alertHtml = `
            <div class="alert alert-danger alert-dismissible fade show" role="alert" style="position: fixed; top: 70px; right: 20px; z-index: 9999; min-width: 300px; box-shadow: 0 4px 12px rgba(0,0,0,0.15);">
                <i class="fas fa-exclamation-circle me-2"></i>
                <span>${message}</span>
                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
            </div>
        `;
        document.body.insertAdjacentHTML('beforeend', alertHtml);

        // 8秒后自动消失（错误消息显示时间稍长）
        setTimeout(() => {
            const alert = document.querySelector('.alert-danger');
            if (alert) {
                alert.remove();
            }
        }, 8000);
    }

    // 清除URL参数
    function clearUrlParameter(param) {
        const url = new URL(window.location);
        url.searchParams.delete(param);
        window.history.replaceState({}, document.title, url.toString());
    }

    // 页面加载完成后处理URL消息
    document.addEventListener('DOMContentLoaded', function() {
        handleUrlMessages();
    });
</script>


<style>
.table-responsive {
    overflow-x: auto;
}
.category-actions {
    display: flex;
    flex-wrap: nowrap;
    justify-content: flex-end;
    gap: 5px;
    min-width: 120px;
}
.category-actions .btn {
    padding: 0.25rem 0.5rem;
    font-size: 0.875rem;
    white-space: nowrap;
    text-decoration: none !important;
}
.category-actions .btn-group {
    display: inline-block;
}
.dropdown-toggle::after {
    display: none !important;
}
.dropdown-item {
    text-decoration: none;
    padding: 0.25rem 1rem;
}
a, a:hover, a:focus, a:active {
    text-decoration: none !important;
}
.nav-link {
    text-decoration: none !important;
}
.nav-link.active {
    background-color: #3490dc;
    color: #fff;
}

/* 淡色按钮样式 */
.btn-light-primary {
    background-color: #e6f0ff;
    color: #1b68ff;
    border: 1px solid #cce0ff;
}
.btn-light-primary:hover {
    background-color: #d1e3ff;
    color: #0056b3;
}
.btn-light-warning {
    background-color: #fff8e6;
    color: #ffa500;
    border: 1px solid #ffe6b3;
}
.btn-light-warning:hover {
    background-color: #fff0d1;
    color: #cc8400;
}
.btn-light-danger {
    background-color: #ffe6e6;
    color: #ff3333;
    border: 1px solid #ffb3b3;
}
.btn-light-danger:hover {
    background-color: #ffd1d1;
    color: #cc0000;
}
.btn-light-info {
    background-color: #e6f7ff;
    color: #00aaff;
    border: 1px solid #b3e0ff;
}
.btn-light-info:hover {
    background-color: #d1f0ff;
    color: #0088cc;
}
.btn-light-success {
    background-color: #e6ffe6;
    color: #00aa00;
    border: 1px solid #b3ffb3;
}
.btn-light-success:hover {
    background-color: #d1ffd1;
    color: #008800;
}
.btn-light-secondary {
    background-color: #f0f0f0;
    color: #666666;
    border: 1px solid #dddddd;
}
.btn-light-secondary:hover {
    background-color: #e0e0e0;
    color: #444444;
}

/* 筛选标签样式 */
.filter-tag {
    display: inline-block;
    padding: 6px 10px;
    font-size: 12px;
    font-weight: normal;
    color: #fff;
    text-decoration: none;
    border-radius: 4px;
    margin-right: 5px;
    margin-bottom: 5px;
}
.filter-tag.active-all { background-color: #007bff; }
.filter-tag.inactive-all { background-color: #6c757d; }

/* 分页样式 */
.simple-pagination {
    display: flex;
    justify-content: center;
    align-items: center;
    flex-wrap: wrap;
    gap: 5px;
}
.pagination-btn {
    display: inline-block;
    padding: 5px 12px;
    background: #fff;
    border: 1px solid #ddd;
    color: #333;
    text-decoration: none;
    border-radius: 3px;
    transition: all 0.2s;
}
.pagination-btn:hover {
    background: #f8f9fa;
    border-color: #ccc;
}
.pagination-btn.active {
    background: #1b68ff;
    color: white;
    border-color: #1b68ff;
}
.pagination-btn.disabled {
    color: #aaa;
    background: #f8f8f8;
    cursor: not-allowed;
}

/* 固定表格列宽 */
.table {
    width: 100%;
    table-layout: fixed;
    white-space: nowrap;
}
.table th {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}
.table td {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}
/* 表格样式已统一使用内联样式 */
</style>

<!-- 消息提示 -->
<?php if($message): ?>
<div class="alert alert-success"><?php echo $message ?? ""; ?></div>
<?php endif; ?>
<?php if($error): ?>
<div class="alert alert-danger"><?php echo $error ?? ""; ?></div>
<?php endif; ?>

<!-- 分类管理 -->
<div class="card mb-4">
    <div class="card-body">
        <!-- 快速操作按钮 -->
        <div style="margin-bottom: 20px;">
            <div style="display: flex; flex-wrap: wrap; gap: 10px; align-items: center;">
                <a href="category.php?action=add" class="btn btn-sm btn-light-success">
                    <i class="fas fa-plus"></i>
                    添加分类
                </a>
                <a href="category.php?action=batch" class="btn btn-sm btn-light-secondary">
                    <i class="fas fa-layer-group"></i>
                    批量添加
                </a>
                <?php if(null !== ($current_parent_name ?? null)): ?>
                <div style="margin-left: auto; padding: 8px 12px; background-color: #f8f9fa; border-radius: 4px; border-left: 4px solid #1b68ff;">
                    <span style="color: #6c757d; font-size: 13px;">当前位置: </span>
                    <a href="category.php?parent_id=-1" style="color: #1b68ff; text-decoration: none; margin: 0 5px; font-weight: 500;">一级分类</a>
                    <i class="fas fa-chevron-right" style="color: #6c757d; font-size: 12px;"></i>
                    <span style="margin: 0 5px; font-weight: 600; color: #333;"><?php echo $current_parent_name ?? ""; ?>的子分类</span>
                </div>
                <?php endif; ?>
            </div>
        </div>

        <!-- 搜索筛选 -->
        <div style="margin-bottom: 20px;">
            <form action="category.php" method="get" style="display: flex; align-items: center; gap: 10px; flex-wrap: wrap;">
                <input type="hidden" name="view" value="list">
                <input type="hidden" name="parent_id" value="-1">

                <div style="display: flex; align-items: center; gap: 8px;">
                    <label style="font-weight: 500; color: #333; white-space: nowrap;">关键字:</label>
                    <input type="text" name="keyword" value="<?php echo $keyword ?? ""; ?>" placeholder="搜索分类名称..."
                           style="padding: 6px 12px; border: 1px solid #ddd; border-radius: 4px; width: 200px;">
                </div>

                <div style="display: flex; align-items: center; gap: 8px;">
                    <label style="font-weight: 500; color: #333; white-space: nowrap;">状态:</label>
                    <select name="status" style="padding: 6px 12px; border: 1px solid #ddd; border-radius: 4px; width: 120px;">
                        <option value="-1" <?php if($status == -1): ?>selected<?php endif; ?>>全部</option>
                        <option value="1" <?php if($status == 1): ?>selected<?php endif; ?>>启用</option>
                        <option value="0" <?php if($status == 0): ?>selected<?php endif; ?>>禁用</option>
                    </select>
                </div>

                <div style="display: flex; gap: 8px;">
                    <button type="submit" class="btn btn-sm btn-light-primary">
                        <i class="fas fa-search"></i>
                        搜索
                    </button>
                    <a href="category.php?view=list&parent_id=-1" class="btn btn-sm btn-light-secondary">
                        <i class="fas fa-undo"></i>
                        重置
                    </a>
                </div>
            </form>
        </div>

        <!-- 分类列表 -->
        <form id="categoryForm" action="category.php?action=save_changes" method="post">
            <input type="hidden" name="return_parent_id" value="<?php echo $parent_id ?? ""; ?>">
            <div class="table-responsive">
                <table class="table table-logs">
                    <thead>
                        <tr>
                            <th style="width: 40px;"><input type="checkbox" id="selectAllHeader"></th>
                            <th style="width: 200px;">名称</th>
                            <th style="width: 120px;">所属分类</th>
                            <th style="width: 120px;">标识符</th>
                            <th style="width: 80px;">状态</th>
                            <th style="width: 80px;">排序</th>
                            <th style="width: 200px;">操作</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php if(empty($categories)): ?>
                        <tr>
                            <td colspan="7" class="text-center">暂无分类数据</td>
                        </tr>
                        <?php else: ?>
                        <?php if(null !== ($categories ?? null) && is_array($categories)): foreach($categories as $category): ?>
                        <tr>
                            <td>
                                <input type="checkbox" name="category_ids[]" class="category-checkbox" value="<?php echo (isset($category['id'])) ? $category['id'] : ""; ?>">
                            </td>
                            <td title="<?php echo (isset($category['name'])) ? $category['name'] : ""; ?>" style="font-weight: 500;">
                                <div style="display: flex; align-items: center; gap: 5px; overflow: hidden;">
                                    <span style="overflow: hidden; text-overflow: ellipsis; display: block;">
                                        <?php if($category['level'] > 1): ?>
                                            <?php echo (isset($category['name'])) ? $category['name'] : ""; ?>
                                        <?php else: ?>
                                            <strong><?php echo (isset($category['name'])) ? $category['name'] : ""; ?></strong>
                                        <?php endif; ?>
                                    </span>
                                    <?php if($category['level'] == 1): ?>
                                    <a href="category.php?parent_id=<?php echo (isset($category['id'])) ? $category['id'] : ""; ?>" class="btn btn-sm btn-light-info" title="查看子类">子类</a>
                                    <?php endif; ?>
                                    <a href="../category.php?id=<?php echo (isset($category['id'])) ? $category['id'] : ""; ?>" target="_blank" class="btn btn-sm btn-light-warning" title="查看前台">前台</a>
                                </div>
                            </td>
                            <td><?php if($category['parent_id'] > 0): ?><?php echo null !== ((null !== ($category ?? null)) ? ($category['parent_name']) : null) && ((null !== ($category ?? null)) ? ($category['parent_name']) : null) !== "" ? (null !== ($category ?? null)) ? ($category['parent_name']) : null : '无'; ?><?php else: ?>-<?php endif; ?></td>
                            <td>
                                <input type="text" name="pinyin[<?php echo (isset($category['id'])) ? $category['id'] : ""; ?>]" value="<?php echo (isset($category['pinyin'])) ? $category['pinyin'] : ""; ?>"
                                       style="width: 100px; padding: 6px 12px; border: 1px solid #ddd; border-radius: 4px; font-size: 14px;"
                                       maxlength="50">
                            </td>
                            <td>
                                <?php if($category['status'] == 1): ?>
                                <span style="display: inline-block; padding: 2px 6px; font-size: 11px; background-color: #e6ffe6; color: #00aa00; border-radius: 3px;">启用</span>
                                <?php else: ?>
                                <span style="display: inline-block; padding: 2px 6px; font-size: 11px; background-color: #ffe6e6; color: #ff3333; border-radius: 3px;">禁用</span>
                                <?php endif; ?>
                            </td>
                            <td>
                                <input type="text" name="sort[<?php echo (isset($category['id'])) ? $category['id'] : ""; ?>]" value="<?php echo (isset($category['sort_order'])) ? $category['sort_order'] : ""; ?>"
                                       style="width: 60px; padding: 6px 12px; border: 1px solid #ddd; border-radius: 4px; font-size: 14px; text-align: center;"
                                       maxlength="5">
                            </td>
                            <td>
                                <div class="category-actions">
                                    <a href="category.php?action=edit&id=<?php echo (isset($category['id'])) ? $category['id'] : ""; ?>" class="btn btn-sm btn-light-primary">编辑</a>
                                    <?php if($category['status'] == 1): ?>
                                    <a href="category.php?action=toggle_status&id=<?php echo (isset($category['id'])) ? $category['id'] : ""; ?>" class="btn btn-sm btn-light-warning"
                                       onclick="return confirm('确定要禁用此分类吗？');">禁用</a>
                                    <?php else: ?>
                                    <a href="category.php?action=toggle_status&id=<?php echo (isset($category['id'])) ? $category['id'] : ""; ?>" class="btn btn-sm btn-light-success">启用</a>
                                    <?php endif; ?>
                                    <a href="category.php?action=delete&id=<?php echo (isset($category['id'])) ? $category['id'] : ""; ?>&return_parent_id=<?php echo $parent_id ?? ""; ?>"
                                       class="btn btn-sm btn-light-danger"
                                       onclick="return confirm('确定要删除此分类吗？删除前请确保没有关联的信息和子分类！');">删除</a>
                                </div>
                            </td>
                        </tr>
                        <?php endforeach; endif; ?>
                        <?php endif; ?>
                    </tbody>
                </table>
            </div>

            <!-- 分页 -->
            <div style="margin-top: 20px; display: flex; justify-content: space-between; align-items: center; flex-wrap: wrap;">
                <!-- 左侧全选和批量操作 -->
                <div style="margin-bottom: 15px;">
                    <label style="margin-right: 10px; display: inline-flex; align-items: center; cursor: pointer;">
                        <input type="checkbox" id="selectAll" style="margin-right: 5px;"> 全选
                    </label>
                    <button type="button" id="batchDeleteBtn" class="btn btn-sm btn-light-danger" style="height: 32px; line-height: 1; padding: 0 12px;">批量删除</button>
                    <button type="button" id="saveChangesBtn" class="btn btn-sm btn-light-primary" style="height: 32px; line-height: 1; padding: 0 12px;">保存修改</button>
                </div>

                <!-- 分页 -->
                <div style="flex: 1; text-align: right;">
                    <?php if(null !== ($pagination ?? null) && $pagination['total_pages'] > 1): ?>
                    <div>
                        <div class="simple-pagination" style="justify-content: flex-end;">
                            <?php if($pagination['current_page'] > 1): ?>
                            <a href="<?php echo (isset($pagination['previous_link'])) ? $pagination['previous_link'] : ""; ?>" class="pagination-btn">上一页</a>
                            <?php else: ?>
                            <span class="pagination-btn disabled">上一页</span>
                            <?php endif; ?>

                            <?php if(null !== ($pagination ?? null) && is_array($pagination['page_links'])): foreach($pagination['page_links'] as $page => $link): ?>
                            <a href="<?php echo $link ?? ""; ?>" class="pagination-btn <?php if($page == $pagination['current_page']): ?>active<?php endif; ?>"><?php echo $page ?? ""; ?></a>
                            <?php endforeach; endif; ?>

                            <?php if($pagination['current_page'] < $pagination['total_pages']): ?>
                            <a href="<?php echo (isset($pagination['next_link'])) ? $pagination['next_link'] : ""; ?>" class="pagination-btn">下一页</a>
                            <?php else: ?>
                            <span class="pagination-btn disabled">下一页</span>
                            <?php endif; ?>
                        </div>
                        <div style="margin-top: 10px; color: #666; font-size: 14px; text-align: right;">
                            第 <?php echo (isset($pagination['current_page'])) ? $pagination['current_page'] : ""; ?> 页，共 <?php echo (isset($pagination['total_pages'])) ? $pagination['total_pages'] : ""; ?> 页
                        </div>
                    </div>
                    <?php endif; ?>
                </div>
            </div>
        </form>
    </div>
</div>

<!-- 批量删除表单 -->
<form id="deleteForm" action="category.php?action=batch_delete" method="post" style="display:none;">
    <input type="hidden" name="return_parent_id" value="<?php echo $parent_id ?? ""; ?>">
    <!-- 此处将由JavaScript动态添加选中的分类ID -->
</form>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // 全选功能
    const selectAllCheckbox = document.getElementById('selectAll');
    const selectAllHeaderCheckbox = document.getElementById('selectAllHeader');
    const categoryCheckboxes = document.querySelectorAll('.category-checkbox');

    // 底部全选复选框
    if (selectAllCheckbox) {
        selectAllCheckbox.addEventListener('change', function() {
            categoryCheckboxes.forEach(function(checkbox) {
                checkbox.checked = selectAllCheckbox.checked;
            });
            if (selectAllHeaderCheckbox) {
                selectAllHeaderCheckbox.checked = selectAllCheckbox.checked;
            }
        });
    }

    // 表头全选复选框
    if (selectAllHeaderCheckbox) {
        selectAllHeaderCheckbox.addEventListener('change', function() {
            categoryCheckboxes.forEach(function(checkbox) {
                checkbox.checked = selectAllHeaderCheckbox.checked;
            });
            if (selectAllCheckbox) {
                selectAllCheckbox.checked = selectAllHeaderCheckbox.checked;
            }
        });
    }

    // 单个复选框变化时更新全选状态
    categoryCheckboxes.forEach(function(checkbox) {
        checkbox.addEventListener('change', function() {
            const allChecked = Array.from(categoryCheckboxes).every(cb => cb.checked);
            const noneChecked = Array.from(categoryCheckboxes).every(cb => !cb.checked);

            if (selectAllCheckbox) {
                selectAllCheckbox.checked = allChecked;
                selectAllCheckbox.indeterminate = !allChecked && !noneChecked;
            }
            if (selectAllHeaderCheckbox) {
                selectAllHeaderCheckbox.checked = allChecked;
                selectAllHeaderCheckbox.indeterminate = !allChecked && !noneChecked;
            }
        });
    });
    
    // 添加输入框的数字限制
    var sortInputs = document.querySelectorAll('.sort-input');
    sortInputs.forEach(function(input) {
        // 限制只能输入数字
        input.addEventListener('keypress', function(e) {
            if (!/[\d]/.test(e.key)) {
                e.preventDefault();
            }
        });

        // 失去焦点时处理
        input.addEventListener('blur', function() {
            if (this.value === '') {
                this.value = '0';
            }
            var num = parseInt(this.value);
            if (isNaN(num) || num < 0) {
                this.value = '0';
            }
        });
    });

    // 批量删除按钮事件
    const batchDeleteBtn = document.getElementById('batchDeleteBtn');
    if (batchDeleteBtn) {
        batchDeleteBtn.addEventListener('click', function() {
            const checkedBoxes = document.querySelectorAll('.category-checkbox:checked');
            if (checkedBoxes.length === 0) {
                alert('请选择要删除的分类');
                return;
            }

            if (confirm('确定删除选中的 ' + checkedBoxes.length + ' 个分类吗？删除前请确保没有关联的信息和子分类！')) {
                const form = document.getElementById('categoryForm');
                form.action = 'category.php?action=batch_delete';
                form.submit();
            }
        });
    }

    // 保存修改按钮事件
    const saveChangesBtn = document.getElementById('saveChangesBtn');
    if (saveChangesBtn) {
        saveChangesBtn.addEventListener('click', function() {
            const form = document.getElementById('categoryForm');

            // 检查拼音输入
            const pinyinInputs = document.querySelectorAll('input[name^="pinyin["]');
            for (let input of pinyinInputs) {
                if (input.value.trim() === '') {
                    alert('拼音标识不能为空');
                    input.focus();
                    return;
                }
            }

            // 检查排序输入
            const sortInputs = document.querySelectorAll('input[name^="sort["]');
            for (let input of sortInputs) {
                if (input.value.trim() === '' || isNaN(parseInt(input.value))) {
                    input.value = '0';
                }
            }

            form.action = 'category.php?action=save_changes';
            form.submit();
        });
    }
});
</script>

        </div>
        <!-- 主内容区 (结束) -->
    </div>
    <!-- wrapper (结束) -->

    <!-- jQuery (必须在Bootstrap之前加载) -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <!-- Bootstrap JavaScript -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html> 