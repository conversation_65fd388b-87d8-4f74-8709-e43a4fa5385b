{include file="header.htm"}

<style>
    .form-group {
        margin-bottom: 15px;
        display: flex;
        flex-wrap: wrap;
        align-items: center;
    }
    .form-label {
        display: block;
        margin-bottom: 0;
        font-weight: 500;
        width: 120px;
        text-align: right;
        padding-right: 15px;
        color: #666;
        line-height: 32px;
    }
    .form-field {
        flex: 0 0 auto;
        min-width: 300px;
        max-width: 800px;
    }
    .form-hint {
        flex: 1;
        margin-left: 15px;
        font-size: 12px;
        color: var(--text-secondary);
        padding-top: 10px;
    }
    .form-control {
        display: block;
        width: 100%;
        padding: 8px 12px;
        font-size: 14px;
        line-height: 1.5;
        color: #333;
        background-color: #fff;
        border: 1px solid var(--border-color);
        border-radius: 4px;
        transition: border-color 0.15s ease-in-out;
    }
    .form-control:focus {
        border-color: var(--primary-color);
        outline: 0;
    }
    textarea.form-control {
        min-height: 250px;
        font-family: monospace;
    }
    .form-buttons {
        margin-top: 15px;
        padding-top: 15px;
        border-top: 1px solid #eee;
        padding-left: 120px;
    }
    .btn {
        display: inline-block;
        font-weight: 400;
        text-align: center;
        white-space: nowrap;
        vertical-align: middle;
        user-select: none;
        border: 1px solid transparent;
        padding: 0.375rem 0.75rem;
        font-size: 0.9rem;
        line-height: 1.5;
        border-radius: 0.25rem;
        transition: color 0.15s ease-in-out, background-color 0.15s ease-in-out, border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
        cursor: pointer;
        text-decoration: none;
    }
    
    /* 淡色按钮样式 */
    .btn-light-primary {
        background-color: #e6f0ff;
        color: #1b68ff;
        border: 1px solid #cce0ff;
    }
    .btn-light-primary:hover {
        background-color: #d1e3ff;
        color: #0056b3;
    }
    .btn-light-success {
        background-color: #e6ffe6;
        color: #00aa00;
        border: 1px solid #b3ffb3;
    }
    .btn-light-success:hover {
        background-color: #d1ffd1;
        color: #008800;
    }
    .btn-light-secondary {
        background-color: #f0f0f0;
        color: #666666;
        border: 1px solid #dddddd;
    }
    .btn-light-secondary:hover {
        background-color: #e0e0e0;
        color: #444444;
    }

    .alert-info {
        background-color: #d1ecf1;
        border-color: #bee5eb;
        color: #0c5460;
        padding: 15px;
        border-radius: 4px;
        margin-bottom: 20px;
    }
    
    .section-title {
        font-size: 1.5rem;
        font-weight: 500;
        margin-bottom: 1rem;
    }
    .card {
        background: #fff;
        border-radius: 4px;
        box-shadow: 0 1px 2px rgba(0,0,0,0.05);
        margin-bottom: 20px;
        padding: 20px;
    }
    .card-title {
        font-size: 1.1rem;
        font-weight: 500;
        margin-bottom: 15px;
        padding-bottom: 10px;
        border-bottom: 1px solid var(--border-color);
    }
    .ml-2 {
        margin-left: 8px;
    }
    .form-notice {
        margin-top: 15px;
        padding-left: 120px;
        font-size: 12px;
        color: #666;
    }
    .form-notice p {
        margin: 0 0 3px 0;
    }
    
    .btn-sm {
        padding: 4px 10px;
        font-size: 12px;
    }
    
    @media (max-width: 992px) {
        .form-group {
            flex-direction: column;
            align-items: flex-start;
        }
        .form-label {
            width: 100%;
            text-align: left;
            margin-bottom: 5px;
            padding-right: 0;
        }
        .form-field {
            width: 100%;
            max-width: 100%;
        }
        .form-hint {
            margin-left: 0;
            margin-top: 5px;
            width: 100%;
        }
        .form-notice, .form-buttons {
            padding-left: 0;
        }
    }
    
    @media (max-width: 768px) {
        .form-group {
            flex-direction: column;
            align-items: flex-start;
        }
        .form-label {
            width: 100%;
            text-align: left;
            margin-bottom: 5px;
            padding-right: 0;
        }
        .form-field {
            width: 100%;
        }
        .form-hint {
            margin-left: 0;
            margin-top: 5px;
            width: 100%;
        }
        .form-notice, .form-buttons {
            padding-left: 0;
        }
    }
</style>

<div class="section">
    <!-- 消息提示 -->
    {if !empty($message)}
    <div class="alert alert-success">
        <i class="fas fa-check-circle"></i>
        <div>
            <p>{$message}</p>
        </div>
    </div>
    {/if}
    
    {if !empty($error)}
    <div class="alert alert-danger">
        <i class="fas fa-exclamation-circle"></i>
        <div>
            <p>{$error}</p>
        </div>
    </div>
    {/if}
    
    <div class="card">
        <div class="card-title">
            {if $parent_id > 0 && $parent_region}
                批量添加 {$parent_region.name} 的子区域
            {else}
                批量添加顶级区域（省/直辖市）
            {/if}
        </div>
        
        <!-- 批量添加说明 -->
        <div class="alert-info">
            <p style="margin-bottom: 5px;"><i class="fa fa-info-circle"></i> <strong>批量添加说明</strong></p>
            <p style="margin-bottom: 3px;">每行输入一个区域名称，系统将自动为每个区域生成拼音标识。</p>
            {if $level == 1}
            <p style="margin-bottom: 0;">当前添加的是顶级区域（省/直辖市）。</p>
            {elseif $level == 2}
            <p style="margin-bottom: 0;">当前添加的是 {$parent_region.name} 下的市级区域。</p>
            {elseif $level == 3}
            <p style="margin-bottom: 0;">当前添加的是 {$parent_region.name} 下的区/县级区域。</p>
            {/if}
        </div>
        
        <!-- 批量添加表单 -->
        <form id="batch-form" action="region.php?action=batch" method="post">
            <input type="hidden" name="is_batch" value="1">
            
            {if $level > 1}
            <div class="form-group">
                <label class="form-label" for="parent_id">所属区域</label>
                <div class="form-field">
                    <select class="form-control" id="parent_id" name="parent_id">
                        {if $provinces}
                            {loop $provinces $province}
                                <option value="{$province.id}" {if $parent_id == $province.id}selected{/if}>{$province.name}</option>
                            {/loop}
                        {else}
                            <option value="{$parent_id}" selected>{$parent_region.name}</option>
                        {/if}
                    </select>
                </div>
                <span class="form-hint">选择所属的上级区域</span>
            </div>
            {else}
            <input type="hidden" name="parent_id" value="0">
            {/if}
            
            <div class="form-group">
                <label class="form-label" for="regions">区域列表</label>
                <div class="form-field">
                    <textarea class="form-control" id="regions" name="regions" rows="15" placeholder="每行输入一个区域名称，例如：
北京
上海
广州
深圳"></textarea>
                </div>
                <span class="form-hint">每行输入一个区域名称，系统会自动生成拼音标识</span>
            </div>
            
            <div class="form-group">
                <label class="form-label" for="examples">示例数据</label>
                <div class="form-field">
                    <button type="button" class="btn btn-sm btn-light-secondary" id="fill-province-example">填充省份示例</button>
                    <button type="button" class="btn btn-sm btn-light-secondary ml-2" id="fill-city-example">填充城市示例</button>
                    <button type="button" class="btn btn-sm btn-light-secondary ml-2" id="fill-district-example">填充区县示例</button>
                </div>
            </div>
            
            <div class="form-buttons">
                <button type="submit" class="btn btn-light-primary">批量添加</button>
                <a href="{if $parent_id > 0}region.php?parent_id={$parent_id}{else}region.php{/if}" class="btn btn-light-secondary ml-2">返回列表</a>
            </div>
        </form>
        
        <!-- 简单说明 -->
        <div class="form-notice">
            <p>1. 批量添加的区域将使用默认排序值0</p>
            <p>2. 系统会自动为每个区域生成拼音标识</p>
            <p>3. 添加完成后，您可以在区域列表中分别编辑各个区域的详细信息</p>
        </div>
    </div>
</div>

{include file="footer.htm"}

<script>
    $(function() {
        // 表单验证
        $('#batch-form').submit(function() {
            var regions = $('#regions').val();
            
            if (!regions) {
                alert('请输入区域列表');
                return false;
            }
            
            return true;
        });
        
        // 填充省份示例
        $('#fill-province-example').click(function() {
            var example = '北京\n天津\n河北\n山西\n内蒙古\n辽宁\n吉林\n黑龙江\n上海\n江苏\n浙江\n安徽';
            $('#regions').val(example);
        });
        
        // 填充城市示例
        $('#fill-city-example').click(function() {
            var example = '石家庄\n唐山\n秦皇岛\n邯郸\n邢台\n保定\n张家口\n承德\n沧州\n廊坊\n衡水';
            $('#regions').val(example);
        });
        
        // 填充区县示例
        $('#fill-district-example').click(function() {
            var example = '长安区\n桥西区\n新华区\n井陉矿区\n裕华区\n藁城区\n鹿泉区\n栾城区\n井陉县\n正定县\n行唐县';
            $('#regions').val(example);
        });
    });
</script> 