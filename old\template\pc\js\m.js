(function() {
  var currentUrl = window.location.href;
  var mobileRegex = /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i;
  var userAgent = navigator.userAgent;

  // 检测是否为移动设备
  if (mobileRegex.test(userAgent)) {
    // 如果当前已经在手机版域名，不需要跳转
    if (currentUrl.indexOf("m.fenlei.com") > -1) {
      return;
    }

    // 检测是否通过IP访问（用于测试）
    var hostname = window.location.hostname;
    var isIpAccess = /^\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3}$/.test(hostname);

    // 如果是通过IP访问，不进行跳转（方便测试）
    if (isIpAccess) {
      return;
    }

    // 构建手机版URL
    var mobileUrl;
    if (currentUrl.indexOf("www.fenlei.com") > -1) {
      // 从PC版跳转到手机版，保持相同的路径
      mobileUrl = currentUrl.replace("www.fenlei.com", "m.fenlei.com");
    } else {
      // 其他情况直接跳转到手机版首页
      mobileUrl = "http://m.fenlei.com";
    }

    // 跳转到手机版
    window.location.href = mobileUrl;
  }
})();