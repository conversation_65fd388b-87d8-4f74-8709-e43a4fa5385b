{"imageActionName": "uploadimage", "imageFieldName": "upfile", "imageMaxSize": 8388608, "imageAllowFiles": [".png", ".jpg", ".jpeg", ".gif", ".bmp", ".webp"], "imageCompressEnable": true, "imageCompressBorder": 1920, "imageInsertAlign": "none", "imageUrlPrefix": "", "imagePathFormat": "/uploads/images/{yyyy}/{mm}{dd}/{time}{rand:6}", "scrawlActionName": "uploadscrawl", "scrawlFieldName": "upfile", "scrawlPathFormat": "/uploads/images/{yyyy}/{mm}{dd}/{time}{rand:6}", "scrawlMaxSize": 2048000, "scrawlUrlPrefix": "", "scrawlInsertAlign": "none", "snapscreenActionName": "uploadimage", "snapscreenPathFormat": "/uploads/images/{yyyy}/{mm}{dd}/{time}{rand:6}", "snapscreenUrlPrefix": "", "snapscreenInsertAlign": "none", "catcherLocalDomain": ["127.0.0.1", "localhost", "img.baidu.com"], "catcherActionName": "catchimage", "catcherFieldName": "source", "catcherPathFormat": "/uploads/images/{yyyy}/{mm}{dd}/{time}{rand:6}", "catcherUrlPrefix": "", "catcherMaxSize": 2048000, "catcherAllowFiles": [".png", ".jpg", ".jpeg", ".gif", ".bmp"], "videoActionName": "uploadvideo", "videoFieldName": "upfile", "videoPathFormat": "/uploads/videos/{yyyy}/{mm}{dd}/{time}{rand:6}", "videoUrlPrefix": "", "videoMaxSize": 102400000, "videoAllowFiles": [".flv", ".swf", ".mkv", ".avi", ".rm", ".rmvb", ".mpeg", ".mpg", ".ogg", ".ogv", ".mov", ".wmv", ".mp4", ".webm", ".mp3", ".wav", ".mid"], "fileActionName": "uploadfile", "fileFieldName": "upfile", "filePathFormat": "/uploads/files/{yyyy}/{mm}{dd}/{time}{rand:6}", "fileUrlPrefix": "", "fileMaxSize": 51200000, "fileAllowFiles": [".png", ".jpg", ".jpeg", ".gif", ".bmp", ".flv", ".swf", ".mkv", ".avi", ".rm", ".rmvb", ".mpeg", ".mpg", ".ogg", ".ogv", ".mov", ".wmv", ".mp4", ".webm", ".mp3", ".wav", ".mid", ".rar", ".zip", ".tar", ".gz", ".7z", ".bz2", ".cab", ".iso", ".doc", ".docx", ".xls", ".xlsx", ".ppt", ".pptx", ".pdf", ".txt", ".md", ".xml"], "imageManagerActionName": "listimage", "imageManagerListPath": "/uploads/images/", "imageManagerListSize": 20, "imageManagerUrlPrefix": "", "imageManagerInsertAlign": "none", "imageManagerAllowFiles": [".png", ".jpg", ".jpeg", ".gif", ".bmp"], "fileManagerActionName": "listfile", "fileManagerListPath": "/uploads/files/", "fileManagerUrlPrefix": "", "fileManagerListSize": 20, "fileManagerAllowFiles": [".png", ".jpg", ".jpeg", ".gif", ".bmp", ".flv", ".swf", ".mkv", ".avi", ".rm", ".rmvb", ".mpeg", ".mpg", ".ogg", ".ogv", ".mov", ".wmv", ".mp4", ".webm", ".mp3", ".wav", ".mid", ".rar", ".zip", ".tar", ".gz", ".7z", ".bz2", ".cab", ".iso", ".doc", ".docx", ".xls", ".xlsx", ".ppt", ".pptx", ".pdf", ".txt", ".md", ".xml"]}