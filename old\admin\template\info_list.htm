{include file="header.htm"} 

<style>
.table-responsive {
    overflow-x: auto;
}
.info-actions {
    display: flex;
    flex-wrap: nowrap;
    justify-content: flex-end;
    gap: 5px;
    min-width: 120px;
}
.info-actions .btn {
    padding: 0.25rem 0.5rem;
    font-size: 0.875rem;
    white-space: nowrap;
    text-decoration: none !important;
}
.info-actions .btn-group {
    display: inline-block;
}
.dropdown-toggle::after {
    display: none !important;
}
.dropdown-item {
    text-decoration: none;
    padding: 0.25rem 1rem;
}
a, a:hover, a:focus, a:active {
    text-decoration: none !important;
}
.nav-link {
    text-decoration: none !important;
}
.nav-link.active {
    background-color: #3490dc;
    color: #fff;
}

/* 淡色按钮样式 */
.btn-light-primary {
    background-color: #e6f0ff;
    color: #1b68ff;
    border: 1px solid #cce0ff;
}
.btn-light-primary:hover {
    background-color: #d1e3ff;
    color: #0056b3;
}
.btn-light-warning {
    background-color: #fff8e6;
    color: #ffa500;
    border: 1px solid #ffe6b3;
}
.btn-light-warning:hover {
    background-color: #fff0d1;
    color: #cc8400;
}
.btn-light-danger {
    background-color: #ffe6e6;
    color: #ff3333;
    border: 1px solid #ffb3b3;
}
.btn-light-danger:hover {
    background-color: #ffd1d1;
    color: #cc0000;
}
.btn-light-info {
    background-color: #e6f7ff;
    color: #00aaff;
    border: 1px solid #b3e0ff;
}
.btn-light-info:hover {
    background-color: #d1f0ff;
    color: #0088cc;
}
.btn-light-success {
    background-color: #e6ffe6;
    color: #00aa00;
    border: 1px solid #b3ffb3;
}
.btn-light-success:hover {
    background-color: #d1ffd1;
    color: #008800;
}
.btn-light-secondary {
    background-color: #f0f0f0;
    color: #666666;
    border: 1px solid #dddddd;
}
.btn-light-secondary:hover {
    background-color: #e0e0e0;
    color: #444444;
}

/* 筛选标签样式 */
.filter-tag {
    display: inline-block;
    padding: 6px 10px;
    font-size: 12px;
    font-weight: normal;
    color: #fff;
    text-decoration: none;
    border-radius: 4px;
    margin-right: 5px;
    margin-bottom: 5px;
}
.filter-tag.active-all { background-color: #007bff; }
.filter-tag.inactive-all { background-color: #6c757d; }
.filter-tag.active-pending { background-color: #007bff; }
.filter-tag.inactive-pending { background-color: #6c757d; }
.filter-tag.active-online { background-color: #007bff; }
.filter-tag.inactive-online { background-color: #6c757d; }
.filter-tag.active-offline { background-color: #007bff; }
.filter-tag.inactive-offline { background-color: #6c757d; }
.filter-tag.active-deleted { background-color: #007bff; }
.filter-tag.inactive-deleted { background-color: #6c757d; }
.filter-tag.active-expired { background-color: #007bff; }
.filter-tag.inactive-expired { background-color: #6c757d; }
.filter-tag.active-top { background-color: #ff4d4f; }
.filter-tag.inactive-top { background-color: #6c757d; }
.filter-tag.active-home { background-color: #ff4d4f; }
.filter-tag.inactive-home { background-color: #6c757d; }
.filter-tag.active-category { background-color: #fa8c16; }
.filter-tag.inactive-category { background-color: #6c757d; }
.filter-tag.active-subcategory { background-color: #52c41a; }
.filter-tag.inactive-subcategory { background-color: #6c757d; }

/* 分页样式 */
.simple-pagination {
    display: flex;
    justify-content: center;
    align-items: center;
    flex-wrap: wrap;
    gap: 5px;
}
.pagination-btn {
    display: inline-block;
    padding: 5px 12px;
    background: #fff;
    border: 1px solid #ddd;
    color: #333;
    text-decoration: none;
    border-radius: 3px;
    transition: all 0.2s;
}
.pagination-btn:hover {
    background: #f8f9fa;
    border-color: #ccc;
}
.pagination-btn.active {
    background: #1b68ff;
    color: white;
    border-color: #1b68ff;
}
.pagination-btn.disabled {
    color: #aaa;
    background: #f8f8f8;
    cursor: not-allowed;
}

/* 固定表格列宽 */
.table {
    width: 100%;
    table-layout: fixed;
    white-space: nowrap;
}
.table th {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}
.table td {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}
/* 设置每列的固定宽度 */
.table .col-checkbox { width: 40px; }
.table .col-id { width: 60px; }
.table .col-title { width: 380px; }
.table .col-category { width: 120px; }
.table .col-top { width: 80px; }
.table .col-time { width: 120px; }
.table .col-created { width: 120px; }
.table .col-expire { width: 80px; }
.table .col-actions { width: 180px; }
</style>

<!-- 消息提示 -->
{if $message}
<div class="alert alert-success">{$message}</div>
{/if}
{if $error}
<div class="alert alert-danger">{$error}</div>
{/if}

<!-- 信息管理 -->
<div class="card mb-4">
    <div class="card-body">
        <!-- 快速筛选按钮 -->
        <div style="margin-bottom: 20px;">
            <div style="display: flex; flex-wrap: wrap;">
                <a href="info.php?status=1" class="filter-tag {if $status == 1 && $is_expired != 1 && empty($id_value) && empty($keyword) && empty($top_type)}active-all{else}inactive-all{/if}">正常信息</a>
                <a href="info.php?status=0" class="filter-tag {if isset($status) && $status == 0}active-pending{else}inactive-pending{/if}">待审核</a>
                <a href="info.php?status=2" class="filter-tag {if isset($status) && $status == 2}active-offline{else}inactive-offline{/if}">已下架</a>
                <a href="info.php?status=3" class="filter-tag {if isset($status) && $status == 3}active-deleted{else}inactive-deleted{/if}">用户删除</a>
                <a href="info.php?is_expired=1" class="filter-tag {if isset($is_expired) && $is_expired == 1}active-expired{else}inactive-expired{/if}">已过期</a>

                <!-- 置顶筛选 -->
                <a href="info.php?top_type=all_top" class="filter-tag {if $top_type == 'all_top'}active-top{else}inactive-top{/if}">全部置顶</a>
                <a href="info.php?top_type=home" class="filter-tag {if $top_type == 'home'}active-home{else}inactive-home{/if}">首页置顶</a>
                <a href="info.php?top_type=category" class="filter-tag {if $top_type == 'category'}active-category{else}inactive-category{/if}">分类置顶</a>
                <a href="info.php?top_type=subcategory" class="filter-tag {if $top_type == 'subcategory'}active-subcategory{else}inactive-subcategory{/if}">子分类置顶</a>

                {if !empty($id_value) || !empty($keyword)}
                <span class="filter-tag active-all">全局搜索: {if !empty($id_value)}ID:{$id_value}{elseif !empty($keyword)}关键词:{$keyword}{/if}</span>
                {/if}

                {if !empty($top_type)}
                <span class="filter-tag active-top">置顶筛选:
                    {if $top_type == 'all_top'}全部置顶
                    {elseif $top_type == 'home'}首页置顶
                    {elseif $top_type == 'category'}分类置顶
                    {elseif $top_type == 'subcategory'}子分类置顶
                    {/if}
                </span>
                {/if}
            </div>
        </div>
        
        <!-- 筛选功能 -->
        <div style="margin-bottom: 20px; padding-bottom: 15px; border-bottom: 1px solid #e9ecef;">
            <form action="info.php" method="get" style="display: flex; align-items: center; flex-wrap: wrap; gap: 15px;">

                <div style="display: flex; align-items: center; white-space: nowrap;">
                    <span style="margin-right: 5px; color: #333;">分类:</span>
                    <select name="parent_category_id" id="parentCategory" onchange="updateFilterSubCategories()" style="width: 150px; height: 32px; border: 1px solid #e9ecef; border-radius: 4px; padding: 0 8px;">
                        <option value="0">不限大分类</option>
                        {foreach $categories as $category}
                        <option value="{$category.id}" data-is-leaf="{if empty($category.children)}1{else}0{/if}">{$category.name}</option>
                        {/foreach}
                    </select>
                    <select name="category_id" id="subCategory" style="width: 150px; height: 32px; border: 1px solid #e9ecef; border-radius: 4px; padding: 0 8px; margin-left: 5px;">
                        <option value="0">不限小分类</option>
                    </select>
                    <input type="hidden" id="hiddenCategoryId" name="category_id" value="{$category_id}">
                </div>

                <div style="display: flex; align-items: center; white-space: nowrap;">
                    <span style="margin-right: 5px; color: #333;">ID:</span>
                    <input type="text" name="id" value="{$id_value}" placeholder="输入ID..." style="width: 100px; height: 32px; border: 1px solid #e9ecef; border-radius: 4px; padding: 0 8px;">
                </div>
                
                <div style="display: flex; align-items: center; white-space: nowrap;">
                    <span style="margin-right: 5px; color: #333;">关键词:</span>
                    <input type="text" name="keyword" value="{$keyword}" placeholder="搜索标题、内容..." style="width: 200px; height: 32px; border: 1px solid #e9ecef; border-radius: 4px; padding: 0 8px;">
                </div>

                <div style="display: flex; align-items: center; white-space: nowrap;">
                    <span style="margin-right: 5px; color: #333;">置顶:</span>
                    <select name="top_type" style="width: 120px; height: 32px; border: 1px solid #e9ecef; border-radius: 4px; padding: 0 8px;">
                        <option value="">不限</option>
                        <option value="all_top" {if $top_type == 'all_top'}selected{/if}>全部置顶</option>
                        <option value="home" {if $top_type == 'home'}selected{/if}>首页置顶</option>
                        <option value="category" {if $top_type == 'category'}selected{/if}>分类置顶</option>
                        <option value="subcategory" {if $top_type == 'subcategory'}selected{/if}>子分类置顶</option>
                    </select>
                </div>

                <div>
                    <button type="submit" class="btn btn-sm btn-light-primary" style="margin-right: 5px; height: 32px; line-height: 1; padding: 0 12px;">筛选</button>
                    <a href="info.php" class="btn btn-sm btn-light-secondary" style="height: 32px; line-height: 1; padding: 0 12px;">重置</a>
                </div>
                
                <div style="margin-left: auto;">
                    <a href="info.php?action=add" class="btn btn-sm btn-light-success" style="height: 32px; line-height: 1; padding: 0 12px;">添加信息</a>
                </div>
            </form>
        </div>
        
        <!-- 信息列表 -->
        <form id="postForm" action="" method="post">
            <div class="table-responsive">
                <table class="table table-vcenter table-bordered table-hover">
                    <thead>
                        <tr>
                            <th class="col-checkbox"></th>
                            <th class="col-id">ID</th>
                            <th class="col-title">标题</th>
                            <th class="col-category">分类</th>
                            <th class="col-top">置顶状态</th>
                            <th class="col-created">发布时间</th>
                            <th class="col-time">更新时间</th>
                            <th class="col-expire">过期时间</th>
                            <th class="col-actions" style="text-align: right;">操作</th>
                        </tr>
                    </thead>
                    <tbody>
                        {if !$posts}
                        <tr>
                            <td colspan="9" class="text-center">暂无信息数据</td>
                        </tr>
                        {else}
                        {foreach $posts as $post}
                        <tr>
                            <td>
                                <input type="checkbox" name="post_ids[]" class="post-checkbox" value="{$post.id}">
                            </td>
                            <td>{$post.id}</td>
                            <td title="{$post.title}" style="font-weight: 500;">
                                <a href="info.php?action=edit&id={$post.id}" target="_blank" style="color: #333; text-decoration: none; overflow: hidden; text-overflow: ellipsis; display: block;">
                                    {$post.title}
                                    {if $post.image_count > 0}
                                    <span style="display: inline-block; margin-left: 5px; padding: 1px 5px; font-size: 12px; background-color: #e6f7ff; color: #1890ff; border-radius: 3px; font-weight: normal;">图{$post.image_count}</span>
                                    {/if}
                                </a>
                            </td>
                            <td>{$post.category_name}</td>
                            <td>
                                {php}
                                $current_time = time();
                                $top_status = array();

                                // 检查首页置顶
                                if (!empty($post['is_top_home']) && $post['is_top_home'] == 1) {
                                    if (empty($post['top_home_expire']) || $post['top_home_expire'] > $current_time) {
                                        $top_status[] = '<span style="display: inline-block; margin: 1px; padding: 2px 6px; font-size: 11px; background-color: #ff4d4f; color: white; border-radius: 3px;">首页</span>';
                                    }
                                }

                                // 检查分类置顶
                                if (!empty($post['is_top_category']) && $post['is_top_category'] == 1) {
                                    if (empty($post['top_category_expire']) || $post['top_category_expire'] > $current_time) {
                                        $top_status[] = '<span style="display: inline-block; margin: 1px; padding: 2px 6px; font-size: 11px; background-color: #fa8c16; color: white; border-radius: 3px;">分类</span>';
                                    }
                                }

                                // 检查子分类置顶
                                if (!empty($post['is_top_subcategory']) && $post['is_top_subcategory'] == 1) {
                                    if (empty($post['top_subcategory_expire']) || $post['top_subcategory_expire'] > $current_time) {
                                        $top_status[] = '<span style="display: inline-block; margin: 1px; padding: 2px 6px; font-size: 11px; background-color: #52c41a; color: white; border-radius: 3px;">子分类</span>';
                                    }
                                }

                                if (empty($top_status)) {
                                    echo '<span style="color: #999;">--</span>';
                                } else {
                                    echo implode('', $top_status);
                                }
                                {/php}
                            </td>
                            <td>{$post.created_at|friendlyTime}</td>
                            <td>{$post.updated_at|friendlyTime}</td>
                            <td>
                                {php}
                                if (!empty($post['expired_at'])) {
                                    $days = getRemainingDaysInt($post['expired_at']);
                                    $guoqi = $days > 0 ? $days.'天' : '已过期';
                                } else {
                                    $guoqi = '长期';
                                }
                                {/php}
                                {$guoqi}
                            </td>
                            <td>
                                <div class="info-actions">
                                    <a href="info.php?action=edit&id={$post.id}" target="_blank" class="btn btn-sm btn-light-primary">编辑</a>
                                    {if $post.status == 0}
                                    <a href="info.php?action=toggle_status&id={$post.id}&to=1" class="btn btn-sm btn-light-success">通过</a>
                                    {elseif $post.status == 1}
                                    <a href="info.php?action=toggle_status&id={$post.id}&to=2" class="btn btn-sm btn-light-warning">下架</a>
                                    {elseif $post.status == 2}
                                    <a href="info.php?action=toggle_status&id={$post.id}&to=1" class="btn btn-sm btn-light-info">上架</a>
                                    {elseif $post.status == 3}
                                    <a href="info.php?action=toggle_status&id={$post.id}&to=0" class="btn btn-sm btn-light-secondary">恢复</a>
                                    {/if}
                                    <a href="info.php?action=delete&id={$post.id}" class="btn btn-sm btn-light-danger" onclick="return confirm('确定要删除这条信息吗？删除后无法恢复！')">删除</a>
                                    <a href="../{$post.category_pinyin}/{$post.id}.html" target="_blank" class="btn btn-sm btn-light-info">查看前台</a>
                                </div>
                            </td>
                        </tr>
                        {/foreach}
                        {/if}
                    </tbody>
                </table>
            </div>
            
            <!-- 分页 -->
            <div style="margin-top: 20px; display: flex; justify-content: space-between; align-items: center; flex-wrap: wrap;">
                <!-- 左侧全选和批量删除 -->
                <div style="margin-bottom: 15px;">
                    <label style="margin-right: 10px; display: inline-flex; align-items: center; cursor: pointer;">
                        <input type="checkbox" id="selectAll" style="margin-right: 5px;"> 全选
                    </label>
                    <button type="button" id="batchDeleteBtn" class="btn btn-sm btn-light-danger" style="height: 32px; line-height: 1; padding: 0 12px;">批量删除</button>
                </div>
                
                <!-- 分页 -->
                <div style="flex: 1; text-align: right;">
                    {if $pagination.total_pages > 1}
                    <div>
                        <div class="simple-pagination" style="justify-content: flex-end;">
                            {if $pagination.current_page > 1}
                            <a href="{$pagination.previous_link}" class="pagination-btn">上一页</a>
                            {else}
                            <span class="pagination-btn disabled">上一页</span>
                            {/if}
                            
                            {foreach $pagination.page_links as $page => $link}
                            <a href="{$link}" class="pagination-btn {if $page == $pagination.current_page}active{/if}">{$page}</a>
                            {/foreach}
                            
                            {if $pagination.current_page < $pagination.total_pages}
                            <a href="{$pagination.next_link}" class="pagination-btn">下一页</a>
                            {else}
                            <span class="pagination-btn disabled">下一页</span>
                            {/if}
                            
                            <span style="margin-left: 10px; color: #6c757d; font-size: 14px;">共 {$pagination.total_pages} 页</span>
                        </div>
                    </div>
                    {/if}
                </div>
            </div>
        </form>
    </div>
</div>

{include file="footer.htm"} 

<script>
document.addEventListener('DOMContentLoaded', function() {
    // 全选/取消全选
    var selectAll = document.getElementById('selectAll');
    var checkboxes = document.getElementsByClassName('post-checkbox');
    
    // 顶部全选按钮事件
    selectAll.addEventListener('change', function() {
        for (var i = 0; i < checkboxes.length; i++) {
            checkboxes[i].checked = selectAll.checked;
        }
    });
    
    // 当单个checkbox改变时，检查是否需要更新全选框状态
    for (var i = 0; i < checkboxes.length; i++) {
        checkboxes[i].addEventListener('change', function() {
            var allChecked = true;
            for (var j = 0; j < checkboxes.length; j++) {
                if (!checkboxes[j].checked) {
                    allChecked = false;
                    break;
                }
            }
            // 同步顶部全选按钮
            selectAll.checked = allChecked;
        });
    }
    
    // 批量删除
    var batchDeleteBtn = document.getElementById('batchDeleteBtn');
    var postForm = document.getElementById('postForm');
    
    function handleBatchDelete() {
        var checkedCount = 0;
        for (var i = 0; i < checkboxes.length; i++) {
            if (checkboxes[i].checked) {
                checkedCount++;
            }
        }
        
        if (checkedCount === 0) {
            alert('请至少选择一条信息进行删除');
            return;
        }
        
        if (confirm('确定要删除选中的 ' + checkedCount + ' 条信息吗？删除后无法恢复！')) {
            // 设置表单提交到批量删除处理页面
            postForm.action = '?action=batch_delete';
            postForm.submit();
        }
    }
    
    // 顶部批量删除按钮事件
    batchDeleteBtn.addEventListener('click', handleBatchDelete);

    // 分类联动功能
    var parentCategory = document.getElementById('parentCategory');
    var subCategory = document.getElementById('subCategory');
    var hiddenCategoryId = document.getElementById('hiddenCategoryId');

    // 使用新的分类树数据结构
    var categoryData = {
        {foreach $categories as $category}
        {$category.id}: {
            name: '{$category.name}',
            children: [
                {foreach $category.children as $child}
                {id: {$child.id}, name: '{$child.name}'},
                {/foreach}
            ]
        },
        {/foreach}
    };

    // 当前选中的分类ID
    var currentCategoryId = {if $category_id}{$category_id}{else}0{/if};

    function updateFilterSubCategories() {
        var parentId = parentCategory.value;
        subCategory.innerHTML = '<option value="0">不限小分类</option>';

        if (parentId != '0' && categoryData[parentId]) {
            var isLeaf = parentCategory.options[parentCategory.selectedIndex].getAttribute('data-is-leaf') === '1';

            if (isLeaf) {
                // 没有子分类，隐藏子分类选择框，直接使用父分类ID
                subCategory.style.opacity = '0.5';
                subCategory.disabled = true;
                hiddenCategoryId.value = parentId;
            } else {
                // 有子分类，显示子分类选择框
                subCategory.style.opacity = '1';
                subCategory.disabled = false;
                hiddenCategoryId.value = '0';

                categoryData[parentId].children.forEach(function(child) {
                    var option = document.createElement('option');
                    option.value = child.id;
                    option.text = child.name;
                    if (currentCategoryId == child.id) {
                        option.selected = true;
                    }
                    subCategory.appendChild(option);
                });
            }
        } else {
            // 未选择父分类
            subCategory.style.opacity = '0.5';
            subCategory.disabled = true;
            hiddenCategoryId.value = '0';
        }
    }

    // 子分类选择变化时更新隐藏字段
    subCategory.addEventListener('change', function() {
        if (!this.disabled && this.value != '0') {
            hiddenCategoryId.value = this.value;
        }
    });

    // 初始化分类选择状态
    if (currentCategoryId > 0) {
        // 查找父分类
        for (var parentId in categoryData) {
            if (parentId == currentCategoryId) {
                // 选中的是父分类
                parentCategory.value = parentId;
                break;
            }

            // 检查子分类
            for (var i = 0; i < categoryData[parentId].children.length; i++) {
                if (categoryData[parentId].children[i].id == currentCategoryId) {
                    parentCategory.value = parentId;
                    break;
                }
            }
        }
    }

    parentCategory.addEventListener('change', updateFilterSubCategories);
    updateFilterSubCategories(); // 初始化子分类
});
</script> 