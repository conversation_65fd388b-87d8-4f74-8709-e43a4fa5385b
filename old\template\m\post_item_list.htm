{if !empty($posts)}
    {foreach $posts as $post}
    <?php
    // 计算有效期
    if (!empty($post['expired_at'])) {
        $days = getRemainingDaysInt($post['expired_at']);
        $validity = $days > 0 ? $days.'天' : '已过期';
    } else {
        $validity = '长期';
    }
    ?>
    <div class="list-item {if !empty($post.is_top_category) || !empty($post.is_top_subcategory)}is-top{/if}">
        <div class="item-row">
            <div class="item-left">
                <div class="item-title">
                    <?php if ($validity == '已过期'): ?>
                    <a href="/{$category.pinyin}/{$post.id}.html" class="expired">{$post.title}</a>
                    <?php else: ?>
                    <a href="/{$category.pinyin}/{$post.id}.html">{$post.title}</a>
                    <?php endif; ?>
                    {if !empty($post.is_top_category) || !empty($post.is_top_subcategory)}
                    <span class="top-tag">顶</span>
                    {/if}
                </div>
            </div>
            <div class="item-right">
                <div class="item-time">
                    {$post.updated_at|friendlyTime}
                </div>
            </div>
        </div>
    </div>
    {/foreach}
{/if}