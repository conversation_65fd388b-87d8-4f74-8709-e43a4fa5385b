<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php if(null !== ($page_title ?? null)): ?><?php echo $page_title ?? ""; ?> - <?php endif; ?>分类信息网站后台管理</title>
    <link href="../static/font-awesome/css/all.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="static/css/admin_clean.css?v=<?php echo time(); ?>" rel="stylesheet">
    <link href="static/css/pagination.css" rel="stylesheet">
    <link href="../static/css/image-compress.css" rel="stylesheet">
</head>
<body>
    <div class="wrapper" id="wrapper">
        <!-- 侧边栏 -->
        <div class="sidebar" id="sidebar">
            <div class="sidebar-header">
                <a href="index.php" class="logo">
                    <i class="fas fa-cube"></i>
                    <span>管理系统</span>
                </a>
            </div>
            <!-- 侧边栏菜单 -->
<div class="menu-item <?php if($current_page == 'index'): ?>active<?php endif; ?>">
    <a href="index.php">
        <i class="fas fa-home"></i>
        <span>控制面板</span>
    </a>
</div>

<div class="menu-item <?php if($current_page == 'category'): ?>active<?php endif; ?>">
    <a href="category.php">
        <i class="fas fa-list"></i>
        <span>分类管理</span>
    </a>
</div>

<div class="menu-item <?php if($current_page == 'region'): ?>active<?php endif; ?>">
    <a href="region.php">
        <i class="fas fa-map-marker-alt"></i>
        <span>区域管理</span>
    </a>
</div>

<div class="menu-item <?php if($current_page == 'info'): ?>active<?php endif; ?>">
    <a href="info.php">
        <i class="fas fa-file-alt"></i>
        <span>信息管理</span>
    </a>
</div>

<div class="menu-item <?php if($current_page == 'news_category'): ?>active<?php endif; ?>">
    <a href="news_category.php">
        <i class="fas fa-newspaper"></i>
        <span>新闻栏目</span>
    </a>
</div>

<div class="menu-item <?php if($current_page == 'news'): ?>active<?php endif; ?>">
    <a href="news.php">
        <i class="fas fa-edit"></i>
        <span>新闻管理</span>
    </a>
</div>

<div class="menu-item <?php if($current_page == 'pages'): ?>active<?php endif; ?>">
    <a href="pages.php">
        <i class="fas fa-file-alt"></i>
        <span>单页管理</span>
    </a>
</div>

<div class="menu-item <?php if($current_page == 'links'): ?>active<?php endif; ?>">
    <a href="links.php">
        <i class="fas fa-link"></i>
        <span>友情链接</span>
    </a>
</div>

<div class="menu-item <?php if($current_page == 'report'): ?>active<?php endif; ?>">
    <a href="report.php">
        <i class="fas fa-flag"></i>
        <span>举报管理</span>
    </a>
</div>

<div class="menu-item <?php if($current_page == 'admin'): ?>active<?php endif; ?>">
    <a href="admin.php">
        <i class="fas fa-user-shield"></i>
        <span>管理员管理</span>
    </a>
</div>

<div class="menu-item <?php if($current_page == 'operation_logs'): ?>active<?php endif; ?>">
    <a href="operation_logs.php">
        <i class="fas fa-history"></i>
        <span>操作日志</span>
    </a>
</div>

<div class="menu-item <?php if($current_page == 'mobile_security'): ?>active<?php endif; ?>">
    <a href="mobile_security.php">
        <i class="fas fa-shield-alt"></i>
        <span>手机号安全</span>
    </a>
</div>

<div class="menu-item <?php if($current_page == 'setting'): ?>active<?php endif; ?>">
    <a href="setting.php">
        <i class="fas fa-cog"></i>
        <span>系统设置</span>
    </a>
</div>

<div class="menu-item <?php if($current_page == 'cache_manager'): ?>active<?php endif; ?>">
    <a href="cache_manager.php">
        <i class="fas fa-memory"></i>
        <span>缓存管理</span>
    </a>
</div>



<div class="menu-item <?php if($current_page == 'db_backup'): ?>active<?php endif; ?>">
    <a href="db_backup.php">
        <i class="fas fa-database"></i>
        <span>数据库备份</span>
    </a>
</div>
        </div>

        <!-- 顶部导航 -->
<div class="top-nav">
    <div class="nav-left">
        <div class="toggle-sidebar" id="toggle-sidebar">
            <i class="fas fa-bars"></i>
        </div>
        <div class="breadcrumb">
            <span class="admin-badge"><?php echo (isset($admin['username'])) ? $admin['username'] : ""; ?></span>
            <i class="fas fa-chevron-right"></i>
            <span>控制台</span>
            <?php if(null !== ($breadcrumb ?? null)): ?>
            <i class="fas fa-chevron-right"></i>
            <span><?php echo $breadcrumb ?? ""; ?></span>
            <?php endif; ?>
        </div>
    </div>
    <div class="nav-right">
        <div class="nav-item" id="clear-cache-btn" title="清理所有缓存">
            <i class="fas fa-trash-alt"></i>
        </div>
        <div class="nav-item" title="前台首页">
            <a href="../" target="_blank" style="color:inherit;text-decoration:none;">
                <i class="fas fa-home"></i>
            </a>
        </div>
        <div class="user-item">
            <div class="user-avatar"><i class="fas fa-user"></i></div>
            <span class="user-name"><?php echo (isset($admin['username'])) ? $admin['username'] : ""; ?></span>
            <a href="logout.php" class="logout-link" title="退出登录">
                <i class="fas fa-sign-out-alt"></i>
            </a>
        </div>
    </div>
</div>

<!-- 清理缓存功能的遮罩层和对话框 -->
<div id="cache-overlay" style="display:none; position:fixed; top:0; left:0; width:100%; height:100%; background:rgba(0,0,0,0.5); z-index:2000;"></div>
<div id="cache-modal" style="display:none; position:fixed; top:50%; left:50%; transform:translate(-50%,-50%); background:#fff; border-radius:8px; box-shadow:0 4px 20px rgba(0,0,0,0.2); width:300px; padding:20px; z-index:2001;">
    <div style="margin-bottom:15px; font-size:16px; font-weight:600;">确认清理缓存</div>
    <p style="margin-bottom:20px; font-size:14px; color:#666;">此操作将清理所有缓存，包括：</p>
    <ul style="margin-bottom:20px; padding-left:20px; font-size:14px; color:#666;">
        <li>页面缓存</li>
        <li>数据缓存</li>
        <li>模板编译文件</li>
    </ul>
    <div style="display:flex; justify-content:flex-end; gap:10px;">
        <button id="cancel-clear-cache" style="padding:8px 16px; border:1px solid #ddd; background:#fff; border-radius:4px; cursor:pointer;">取消</button>
        <button id="confirm-clear-cache" style="padding:8px 16px; border:none; background:#dc3545; color:#fff; border-radius:4px; cursor:pointer;">确认清理</button>
    </div>
</div>

<!-- 成功提示框 -->
<div id="success-toast" style="display:none; position:fixed; top:50%; left:50%; transform:translate(-50%,-50%); background:#28a745; color:#fff; padding:15px 25px; border-radius:6px; box-shadow:0 4px 12px rgba(0,0,0,0.15); z-index:2002; font-size:14px;">
    <i class="fas fa-check-circle" style="margin-right:8px;"></i>
    缓存清理成功！
</div>

<script>
    document.addEventListener('DOMContentLoaded', function() {
        const clearCacheBtn = document.getElementById('clear-cache-btn');
        const cacheOverlay = document.getElementById('cache-overlay');
        const cacheModal = document.getElementById('cache-modal');
        const cancelClearCache = document.getElementById('cancel-clear-cache');
        const confirmClearCache = document.getElementById('confirm-clear-cache');
        const successToast = document.getElementById('success-toast');
        
        if (clearCacheBtn && cacheOverlay && cacheModal) {
            clearCacheBtn.addEventListener('click', function() {
                cacheOverlay.style.display = 'block';
                cacheModal.style.display = 'block';
            });
            
            cancelClearCache.addEventListener('click', function() {
                cacheOverlay.style.display = 'none';
                cacheModal.style.display = 'none';
            });
            
            cacheOverlay.addEventListener('click', function() {
                cacheOverlay.style.display = 'none';
                cacheModal.style.display = 'none';
            });
            
            confirmClearCache.addEventListener('click', function() {
                // 发送清理缓存请求
                const xhr = new XMLHttpRequest();
                xhr.open('POST', 'cache_manager.php', true);
                xhr.setRequestHeader('Content-Type', 'application/x-www-form-urlencoded');
                
                confirmClearCache.innerHTML = '清理中...';
                confirmClearCache.disabled = true;
                
                xhr.onreadystatechange = function() {
                    if (xhr.readyState === 4) {
                        cacheOverlay.style.display = 'none';
                        cacheModal.style.display = 'none';
                        
                        if (xhr.status === 200) {
                            // 显示成功提示
                            successToast.style.display = 'block';
                            successToast.style.transform = 'translate(-50%, -50%) scale(0.8)';
                            
                            setTimeout(function() {
                                successToast.style.transform = 'translate(-50%, -50%) scale(1)';
                            }, 100);
                            
                            setTimeout(function() {
                                successToast.style.transform = 'translate(-50%, -50%) scale(0.8)';
                                setTimeout(function() {
                                    successToast.style.display = 'none';
                                    successToast.style.transform = 'translate(-50%, -50%) scale(1)';
                                }, 300);
                            }, 2000);
                        }
                        
                        confirmClearCache.disabled = false;
                        confirmClearCache.innerHTML = '确认清理';
                    }
                };
                
                xhr.send('action=clear_cache&type=all');
            });
        }
    });
</script>


        <!-- 主内容区 (开始) -->
        <div class="main-content">

<script>
    document.addEventListener('DOMContentLoaded', function() {
        // 侧边栏折叠功能
        const toggleSidebar = document.getElementById('toggle-sidebar');
        const sidebar = document.getElementById('sidebar');
        const wrapper = document.getElementById('wrapper');
        
        if (toggleSidebar && sidebar && wrapper) {
            toggleSidebar.addEventListener('click', function() {
                sidebar.classList.toggle('collapsed');
                wrapper.classList.toggle('collapsed');
                localStorage.setItem('sidebar_collapsed', sidebar.classList.contains('collapsed'));
            });
            
            // 恢复侧边栏状态
            const isCollapsed = localStorage.getItem('sidebar_collapsed') === 'true';
            if (isCollapsed) {
                sidebar.classList.add('collapsed');
                wrapper.classList.add('collapsed');
            }
        }
        
        // 设置当前页面的菜单项为激活状态
        const currentPath = window.location.pathname;
        const menuItems = document.querySelectorAll('.menu-item a');
        
        menuItems.forEach(function(item) {
            const href = item.getAttribute('href');
            if (href) {
                // 检查完整路径匹配
                if (currentPath.endsWith(href)) {
                    const menuItem = item.closest('.menu-item');
                    if (menuItem) {
                        menuItem.classList.add('active');
                    }
                    
                    foundActive = true;
                }
            }
        });
        
        // 如果没有找到完全匹配的，尝试部分匹配
        if (!foundActive) {
            const pathParts = currentPath.split('/');
            const filename = pathParts[pathParts.length - 1];
            
            if (filename) {
                menuItems.forEach(function(item) {
                    const href = item.getAttribute('href');
                    if (href && href.includes(filename.split('.')[0])) {
                        const menuItem = item.closest('.menu-item');
                        if (menuItem) {
                            menuItem.classList.add('active');
                        }
                    }
                });
            }
        }
        
        // 保存当前激活的菜单项
        menuItems.forEach(function(item) {
            item.addEventListener('click', function() {
                const href = this.getAttribute('href');
                if (href) {
                    localStorage.setItem('active_menu_item', href);
                }
            });
        });
    });

    // 处理URL参数中的错误和成功消息
    function handleUrlMessages() {
        const urlParams = new URLSearchParams(window.location.search);
        const message = urlParams.get('message');
        const error = urlParams.get('error');

        if (message) {
            showSuccessMessage(message);
            // 清除URL中的message参数
            clearUrlParameter('message');
        }

        if (error) {
            showErrorMessage(error);
            // 清除URL中的error参数
            clearUrlParameter('error');
        }
    }

    // 显示成功消息
    function showSuccessMessage(message) {
        const alertHtml = `
            <div class="alert alert-success alert-dismissible fade show" role="alert" style="position: fixed; top: 70px; right: 20px; z-index: 9999; min-width: 300px; box-shadow: 0 4px 12px rgba(0,0,0,0.15);">
                <i class="fas fa-check-circle me-2"></i>
                <span>${message}</span>
                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
            </div>
        `;
        document.body.insertAdjacentHTML('beforeend', alertHtml);

        // 5秒后自动消失
        setTimeout(() => {
            const alert = document.querySelector('.alert-success');
            if (alert) {
                alert.remove();
            }
        }, 5000);
    }

    // 显示错误消息
    function showErrorMessage(message) {
        const alertHtml = `
            <div class="alert alert-danger alert-dismissible fade show" role="alert" style="position: fixed; top: 70px; right: 20px; z-index: 9999; min-width: 300px; box-shadow: 0 4px 12px rgba(0,0,0,0.15);">
                <i class="fas fa-exclamation-circle me-2"></i>
                <span>${message}</span>
                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
            </div>
        `;
        document.body.insertAdjacentHTML('beforeend', alertHtml);

        // 8秒后自动消失（错误消息显示时间稍长）
        setTimeout(() => {
            const alert = document.querySelector('.alert-danger');
            if (alert) {
                alert.remove();
            }
        }, 8000);
    }

    // 清除URL参数
    function clearUrlParameter(param) {
        const url = new URL(window.location);
        url.searchParams.delete(param);
        window.history.replaceState({}, document.title, url.toString());
    }

    // 页面加载完成后处理URL消息
    document.addEventListener('DOMContentLoaded', function() {
        handleUrlMessages();
    });
</script>


<style>
    .table-responsive {
        overflow-x: auto;
    }
    .region-actions {
        display: flex;
        flex-wrap: nowrap;
        justify-content: flex-end;
        gap: 5px;
        min-width: 120px;
    }
    .region-actions .btn {
        padding: 0.25rem 0.5rem;
        font-size: 0.875rem;
        white-space: nowrap;
        text-decoration: none !important;
    }
    .region-actions .btn-group {
        display: inline-block;
    }
    .dropdown-toggle::after {
        display: none !important;
    }
    .dropdown-item {
        text-decoration: none;
        padding: 0.25rem 1rem;
    }
    a, a:hover, a:focus, a:active {
        text-decoration: none !important;
    }
    .nav-link {
        text-decoration: none !important;
    }
    .nav-link.active {
        background-color: #3490dc;
        color: #fff;
    }

    /* 淡色按钮样式 */
    .btn-light-primary {
        background-color: #e6f0ff;
        color: #1b68ff;
        border: 1px solid #cce0ff;
    }
    .btn-light-primary:hover {
        background-color: #d1e3ff;
        color: #0056b3;
    }
    .btn-light-warning {
        background-color: #fff8e6;
        color: #ffa500;
        border: 1px solid #ffe6b3;
    }
    .btn-light-warning:hover {
        background-color: #fff0d1;
        color: #cc8400;
    }
    .btn-light-danger {
        background-color: #ffe6e6;
        color: #ff3333;
        border: 1px solid #ffb3b3;
    }
    .btn-light-danger:hover {
        background-color: #ffd1d1;
        color: #cc0000;
    }
    .btn-light-info {
        background-color: #e6f7ff;
        color: #00aaff;
        border: 1px solid #b3e0ff;
    }
    .btn-light-info:hover {
        background-color: #d1f0ff;
        color: #0088cc;
    }
    .btn-light-success {
        background-color: #e6ffe6;
        color: #00aa00;
        border: 1px solid #b3ffb3;
    }
    .btn-light-success:hover {
        background-color: #d1ffd1;
        color: #008800;
    }
    .btn-light-secondary {
        background-color: #f0f0f0;
        color: #666666;
        border: 1px solid #dddddd;
    }
    .btn-light-secondary:hover {
        background-color: #e0e0e0;
        color: #444444;
    }

    /* 分页样式 */
    .simple-pagination {
        display: flex;
        justify-content: center;
        align-items: center;
        flex-wrap: wrap;
        gap: 5px;
    }
    .pagination-btn {
        display: inline-block;
        padding: 5px 12px;
        background: #fff;
        border: 1px solid #ddd;
        color: #333;
        text-decoration: none;
        border-radius: 3px;
        transition: all 0.2s;
    }
    .pagination-btn:hover {
        background: #f8f9fa;
        border-color: #ccc;
    }
    .pagination-btn.active {
        background: #1b68ff;
        color: white;
        border-color: #1b68ff;
    }
    .pagination-btn.disabled {
        color: #aaa;
        background: #f8f8f8;
        cursor: not-allowed;
    }

    /* 固定表格列宽 */
    .table {
        width: 100%;
        table-layout: auto;
        white-space: nowrap;
    }
    .table th {
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
    }
    .table td {
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
    }

    .search-form {
        display: flex;
        width: 300px;
    }
    .search-input {
        flex: 1;
        padding: 6px 12px;
        border: 1px solid #ced4da;
        border-radius: 4px 0 0 4px;
    }
    .search-btn {
        border-radius: 0 4px 4px 0;
    }


    .sort-input {
        width: 60px;
        text-align: center;
        padding: 2px 5px;
        border: 1px solid #ddd;
        border-radius: 3px;
    }
    
    .badge {
        display: inline-block;
        padding: 3px 6px;
        font-size: 12px;
        border-radius: 3px;
        color: #fff;
    }
    .badge-info {
        background-color: #17a2b8;
    }
    .badge-secondary {
        background-color: #6c757d;
    }
</style>

<!-- 消息提示 -->
<?php if(null !== ($message ?? null) && !empty($message)): ?>
<div class="alert alert-success">
    <i class="fa fa-check-circle"></i> <?php echo $message ?? ""; ?>
</div>
<?php endif; ?>

<?php if(null !== ($error ?? null) && !empty($error)): ?>
<div class="alert alert-danger">
    <i class="fa fa-exclamation-circle"></i> <?php echo $error ?? ""; ?>
</div>
<?php endif; ?>

<!-- 区域管理 -->
<div class="card mb-4">
    <div class="card-body">
        <!-- 头部导航 -->
        <div style="margin-bottom: 20px; padding-bottom: 15px; border-bottom: 1px solid #e9ecef;">
            <div style="display: flex; justify-content: space-between; align-items: center; flex-wrap: wrap; gap: 10px;">
                <div>
                    <?php if($parent_id > 0): ?>
                        <?php if($parent_region): ?>
                            <a href="region.php" class="btn btn-light-secondary">返回顶级区域</a>
                            <span style="margin-left: 10px;">当前位置：<?php echo (isset($parent_region['level_text'])) ? $parent_region['level_text'] : ""; ?> - <?php echo (isset($parent_region['name'])) ? $parent_region['name'] : ""; ?></span>
                        <?php else: ?>
                            <a href="region.php" class="btn btn-light-secondary">返回顶级区域</a>
                        <?php endif; ?>
                    <?php else: ?>
                        <h4 style="margin: 0; font-weight: 500;">顶级区域列表（省/直辖市）</h4>
                    <?php endif; ?>
                </div>
                <div>
                    <form class="search-form" action="region.php" method="get">
                        <?php if($parent_id > 0): ?>
                        <input type="hidden" name="parent_id" value="<?php echo $parent_id ?? ""; ?>">
                        <?php endif; ?>
                        <input type="text" name="keyword" class="search-input" placeholder="搜索区域名称" value="<?php echo $keyword ?? ""; ?>">
                        <button type="submit" class="btn btn-light-primary search-btn">搜索</button>
                    </form>
                </div>
            </div>
        </div>
        
        <!-- 操作按钮 -->
        <div style="margin-bottom: 20px; display: flex; justify-content: space-between; align-items: center;">
            <div>
                <?php if($parent_id > 0): ?>
                    <a href="region.php?action=add&parent_id=<?php echo $parent_id ?? ""; ?>" class="btn btn-light-primary">添加子区域</a>
                    <a href="region.php?action=batch&parent_id=<?php echo $parent_id ?? ""; ?>" class="btn btn-light-success" style="margin-left: 8px;">批量添加</a>
                <?php else: ?>
                    <a href="region.php?action=add" class="btn btn-light-primary">添加省份/直辖市</a>
                    <a href="region.php?action=batch" class="btn btn-light-success" style="margin-left: 8px;">批量添加</a>
                <?php endif; ?>
            </div>
            
            <button type="button" id="save-sort-btn" class="btn btn-light-info">保存排序</button>
        </div>
        
        <!-- 区域列表 -->
        <form id="list-form" action="region.php?action=batch_delete" method="post">
            <input type="hidden" name="return_parent_id" value="<?php echo $parent_id ?? ""; ?>">
            <div class="table-responsive">
                <table class="table table-hover" id="region-table">
                    <thead>
                        <tr>
                            <th width="40"><input type="checkbox" id="check-all"></th>
                            <th width="60">排序</th>
                            <th width="60">ID</th>
                            <th>名称</th>
                            <th width="100">拼音</th>
                            <th width="80">级别</th>
                            <th width="80">子区域</th>
                            <th width="180" style="text-align: right;">操作</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php if(null !== ($regions ?? null) && !empty($regions)): ?>
                            <?php if(null !== ($regions ?? null) && is_array($regions)): foreach($regions as $region): ?>
                                <tr id="item-<?php echo (isset($region['id'])) ? $region['id'] : ""; ?>" data-id="<?php echo (isset($region['id'])) ? $region['id'] : ""; ?>">
                                    <td><input type="checkbox" name="region_ids[]" value="<?php echo (isset($region['id'])) ? $region['id'] : ""; ?>" class="check-item"></td>
                                    <td>
                                        <input type="number" class="sort-input" name="sort[<?php echo (isset($region['id'])) ? $region['id'] : ""; ?>]" value="<?php echo (isset($region['sort_order'])) ? $region['sort_order'] : ""; ?>" min="0">
                                    </td>
                                    <td><?php echo (isset($region['id'])) ? $region['id'] : ""; ?></td>
                                    <td><?php echo (isset($region['name'])) ? $region['name'] : ""; ?></td>
                                    <td><?php echo (isset($region['pinyin'])) ? $region['pinyin'] : ""; ?></td>
                                    <td><?php echo (isset($region['level_text'])) ? $region['level_text'] : ""; ?></td>
                                    <td>
                                        <?php if($region['child_count'] > 0): ?>
                                            <a href="region.php?parent_id=<?php echo (isset($region['id'])) ? $region['id'] : ""; ?>" class="badge badge-info"><?php echo (isset($region['child_count'])) ? $region['child_count'] : ""; ?> 个</a>
                                        <?php else: ?>
                                            <span class="badge badge-secondary">0</span>
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <div class="region-actions">
                                            <?php if($region['child_count'] > 0): ?>
                                                <a href="region.php?parent_id=<?php echo (isset($region['id'])) ? $region['id'] : ""; ?>" class="btn btn-light-info">子区域</a>
                                            <?php else: ?>
                                                <a href="region.php?action=add&parent_id=<?php echo (isset($region['id'])) ? $region['id'] : ""; ?>" class="btn btn-light-success">添加子区域</a>
                                            <?php endif; ?>
                                            <a href="region.php?action=edit&id=<?php echo (isset($region['id'])) ? $region['id'] : ""; ?>" class="btn btn-light-primary">编辑</a>
                                            <a href="region.php?action=delete&id=<?php echo (isset($region['id'])) ? $region['id'] : ""; ?>&return_parent_id=<?php echo $parent_id ?? ""; ?>" class="btn btn-light-danger delete-btn">删除</a>
                                        </div>
                                    </td>
                                </tr>
                            <?php endforeach; endif; ?>
                        <?php else: ?>
                            <tr>
                                <td colspan="8" style="text-align: center;">没有找到区域记录</td>
                            </tr>
                        <?php endif; ?>
                    </tbody>
                </table>
            </div>
            
            <!-- 分页和批量操作 -->
            <div style="margin-top: 20px; display: flex; justify-content: space-between; align-items: center; flex-wrap: wrap;">
                <!-- 左侧全选和批量删除 -->
                <div style="margin-bottom: 15px;">
                    <label style="margin-right: 10px; display: inline-flex; align-items: center; cursor: pointer;">
                        <input type="checkbox" id="selectAll" style="margin-right: 5px;"> 全选
                    </label>
                    <button type="button" id="batch-delete-btn" class="btn btn-light-danger">批量删除</button>
                </div>
                
                <!-- 分页 -->
                <div style="flex: 1; text-align: right;">
                    <?php if(null !== ($pagination ?? null) && $pagination['total_pages'] > 1): ?>
                    <div>
                        <div class="simple-pagination" style="justify-content: flex-end;">
                            <?php if($pagination['current_page'] > 1): ?>
                            <a href="<?php echo (isset($pagination['first_link'])) ? $pagination['first_link'] : ""; ?>" class="pagination-btn">&laquo;</a>
                            <a href="<?php echo (isset($pagination['previous_link'])) ? $pagination['previous_link'] : ""; ?>" class="pagination-btn">&lsaquo;</a>
                            <?php else: ?>
                            <span class="pagination-btn disabled">&laquo;</span>
                            <span class="pagination-btn disabled">&lsaquo;</span>
                            <?php endif; ?>
                            
                            <?php if(null !== ($pagination ?? null) && is_array($pagination) && array_key_exists('page_links', $pagination) && $pagination['page_links']): ?>
                                <?php if(null !== ($pagination ?? null) && is_array($pagination['page_links'])): foreach($pagination['page_links'] as $page => $link): ?>
                                    <?php if($page == $pagination['current_page']): ?>
                                    <span class="pagination-btn active"><?php echo $page ?? ""; ?></span>
                                    <?php else: ?>
                                    <a href="<?php echo $link ?? ""; ?>" class="pagination-btn"><?php echo $page ?? ""; ?></a>
                                    <?php endif; ?>
                                <?php endforeach; endif; ?>
                            <?php endif; ?>
                            
                            <?php if($pagination['current_page'] < $pagination['total_pages']): ?>
                            <a href="<?php echo (isset($pagination['next_link'])) ? $pagination['next_link'] : ""; ?>" class="pagination-btn">&rsaquo;</a>
                            <a href="<?php echo (isset($pagination['last_link'])) ? $pagination['last_link'] : ""; ?>" class="pagination-btn">&raquo;</a>
                            <?php else: ?>
                            <span class="pagination-btn disabled">&rsaquo;</span>
                            <span class="pagination-btn disabled">&raquo;</span>
                            <?php endif; ?>
                            
                            <span style="margin-left: 10px; color: #6c757d; font-size: 14px;">共 <?php echo (isset($pagination['total_pages'])) ? $pagination['total_pages'] : ""; ?> 页</span>
                        </div>
                    </div>
                    <?php endif; ?>
                </div>
            </div>
        </form>
    </div>
</div>

        </div>
        <!-- 主内容区 (结束) -->
    </div>
    <!-- wrapper (结束) -->

    <!-- jQuery (必须在Bootstrap之前加载) -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <!-- Bootstrap JavaScript -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>



<script type="text/javascript">
// 简化版
window.onload = function() {
    console.log('页面加载完成');
    
    // 全选功能 - 头部复选框
    var checkAll = document.getElementById('check-all');
    if(checkAll) {
        checkAll.onclick = function() {
            var checked = this.checked;
            console.log('头部全选框点击:', checked);
            
            // 获取所有复选框
            var checkboxes = document.getElementsByClassName('check-item');
            for (var i = 0; i < checkboxes.length; i++) {
                checkboxes[i].checked = checked;
            }
            
            // 同步底部全选框
            var selectAll = document.getElementById('selectAll');
            if(selectAll) {
                selectAll.checked = checked;
            }
        };
    }
    
    // 底部全选框
    var selectAll = document.getElementById('selectAll');
    if(selectAll) {
        selectAll.onclick = function() {
            var checked = this.checked;
            console.log('底部全选框点击:', checked);
            
            // 获取所有复选框
            var checkboxes = document.getElementsByClassName('check-item');
            for (var i = 0; i < checkboxes.length; i++) {
                checkboxes[i].checked = checked;
            }
            
            // 同步顶部全选框
            var checkAll = document.getElementById('check-all');
            if(checkAll) {
                checkAll.checked = checked;
            }
        };
    }
    
    // 批量删除按钮
    var batchDeleteBtn = document.getElementById('batch-delete-btn');
    if(batchDeleteBtn) {
        batchDeleteBtn.onclick = function() {
            var checkboxes = document.getElementsByClassName('check-item');
            var checkedCount = 0;
            
            for (var i = 0; i < checkboxes.length; i++) {
                if (checkboxes[i].checked) {
                    checkedCount++;
                }
            }
            
            console.log('选中数量:', checkedCount);
            
            if (checkedCount === 0) {
                alert('请至少选择一个区域');
                return false;
            }
            
            if (confirm('确定要删除选中的' + checkedCount + '个区域吗？此操作不可恢复!')) {
                document.getElementById('list-form').submit();
            }
        };
    }
    
    // 保存排序按钮
    var saveSortBtn = document.getElementById('save-sort-btn');
    if(saveSortBtn) {
        saveSortBtn.onclick = function() {
            console.log('保存排序按钮点击');
            
            var sortData = [];
            var inputs = document.getElementsByClassName('sort-input');
            
            for (var i = 0; i < inputs.length; i++) {
                var input = inputs[i];
                var row = input.closest('tr');
                var id = row.getAttribute('data-id');
                var value = input.value;
                
                sortData.push({
                    id: parseInt(id),
                    sort: parseInt(value)
                });
            }
            
            // 生成JSON字符串
            var jsonString = JSON.stringify(sortData);
            console.log('排序数据JSON:', jsonString);
            
            // 创建请求数据对象
            var requestData = {
                sort_data: sortData,
                return_parent_id: <?php echo $parent_id ?? ""; ?>
            };
            
            // 使用XMLHttpRequest直接发送JSON
            var xhr = new XMLHttpRequest();
            xhr.open('POST', 'region.php?action=save_sort', true);
            xhr.setRequestHeader('Content-Type', 'application/json');
            
            // 处理响应
            xhr.onreadystatechange = function() {
                if (xhr.readyState === 4) {
                    if (xhr.status === 200) {
                        try {
                            var response = JSON.parse(xhr.responseText);
                            console.log('服务器响应:', response);
                            
                            if (response.success) {
                                alert('排序保存成功: ' + response.message);
                                // 刷新页面
                                window.location.reload();
                            } else {
                                alert('保存失败: ' + response.message);
                            }
                        } catch (e) {
                            console.error('解析响应失败:', e);
                            // 如果不是JSON响应，可能是重定向或HTML
                            window.location.reload();
                        }
                    } else {
                        alert('保存排序失败: 服务器错误 (' + xhr.status + ')');
                    }
                }
            };
            
            // 添加错误处理
            xhr.onerror = function() {
                console.error('请求错误');
                alert('网络错误，请稍后重试');
            };
            
            // 发送JSON数据
            xhr.send(JSON.stringify(requestData));
        };
    }
    

    


    // 单个删除功能
    var deleteButtons = document.getElementsByClassName('delete-btn');
    for (var i = 0; i < deleteButtons.length; i++) {
        deleteButtons[i].onclick = function(e) {
            e.preventDefault();
            var url = this.getAttribute('href');
            console.log('单项删除点击, URL:', url);
            
            if (confirm('确定要删除该区域吗？此操作不可恢复!')) {
                window.location.href = url;
            }
        };
    }
};
</script> 