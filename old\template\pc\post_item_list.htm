{if !empty($posts)}
    {foreach $posts as $post}
    {php}
    if (!empty($post['expired_at'])) {
        $days = getRemainingDaysInt($post['expired_at']);
        $guoqi = $days > 0 ? $days.'天' : '已过期';
    } else {
        $guoqi = '长期';
    }
    {/php}
    {php}
        // 检查是否显示置顶标记和选择图标
        $current_time = time();
        $show_top_tag = false;
        $icon_class = 'icon_title1.gif'; // 默认普通图标

        // 检查大分类置顶
        if (isset($post['is_top_category']) && $post['is_top_category'] == 1) {
            if (!isset($post['top_category_expire']) || $post['top_category_expire'] == 0 || $post['top_category_expire'] > $current_time) {
                $show_top_tag = true;
                $icon_class = 'icon_title2.gif'; // 置顶图标
            }
        }

        // 检查小分类置顶
        if (!$show_top_tag && isset($post['is_top_subcategory']) && $post['is_top_subcategory'] == 1) {
            if (!isset($post['top_subcategory_expire']) || $post['top_subcategory_expire'] == 0 || $post['top_subcategory_expire'] > $current_time) {
                $show_top_tag = true;
                $icon_class = 'icon_title2.gif'; // 置顶图标
            }
        }

        // 输出li标签和图标，为置顶信息添加CSS类
        $li_class = $show_top_tag ? ' class="top-item"' : '';
        echo '<li' . $li_class . ' style="background:url(/template/pc/images/' . $icon_class . ') left no-repeat; padding-left: 15px;">';
    {/php}
        <div class="info-title">
            {if $post.category_name}<a href="/{$post.category_pinyin}/" style="color: #999; text-decoration: none; margin-right: 5px;">[{$post.category_name}]</a>{/if}
            <a {if $guoqi=='已过期'} style="text-decoration: line-through;"{/if} target="_blank" href="/{$post.category_pinyin}/{$post.id}.html" {php}if ($show_top_tag) echo 'style="color: #EE3131;"';{/php}>{$post.title}</a>{php}if ($show_top_tag) echo '<span class="top-tag">顶</span>';{/php}
        </div>
        <div class="info-meta">
            <span class="area">{$post.region_name}</span>
            <span class="validity">{$guoqi}</span>
            <span class="time">{$post.updated_at|friendlyTime}</span>
        </div>
    {php}echo '</li>';{/php}
    {/foreach}
{/if} 