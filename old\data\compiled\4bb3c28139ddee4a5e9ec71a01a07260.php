<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="utf-8">
    <title><?php if($title): ?><?php echo $title ?? ""; ?> - <?php echo $site_name ?? ""; ?><?php else: ?><?php echo $site_name ?? ""; ?><?php endif; ?></title>
    <meta name="keywords" content="<?php if($meta_keywords): ?><?php echo $meta_keywords ?? ""; ?><?php elseif($site_keywords): ?><?php echo $site_keywords ?? ""; ?><?php else: ?>分类信息,免费发布,信息平台<?php endif; ?>" />
    <meta name="description" content="<?php if($meta_description): ?><?php echo $meta_description ?? ""; ?><?php elseif($site_description): ?><?php echo $site_description ?? ""; ?><?php else: ?>提供免费发布分类信息服务的网站<?php endif; ?>" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    
    <link rel="stylesheet" href="/template/pc/css/common.css">
    <link rel="stylesheet" href="/template/pc/css/index.css">
    <link rel="stylesheet" href="/static/css/content-responsive.css?v=<?php echo time(); ?>">
    <script type="text/javascript" src="/template/pc/js/m.js"></script>
    
    <style>
        /* 单页专用样式 - 左右分栏布局 */
        .page-wrapper {
            display: flex;
            background: #fff;
            border-radius: 5px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
            margin-bottom: 20px;
            min-height: 600px;
        }

        /* 左侧导航 */
        .page-sidebar {
            width: 200px;
            background: #f8f9fa;
            border-right: 1px solid #eee;
            border-radius: 5px 0 0 5px;
        }

        .sidebar-header {
            padding: 20px 15px;
            border-bottom: 1px solid #eee;
            background: #e9ecef;
            font-weight: 600;
            color: #495057;
            font-size: 16px;
        }

        .sidebar-menu {
            list-style: none;
            margin: 0;
            padding: 0;
        }

        .sidebar-menu li {
            border-bottom: 1px solid #eee;
        }

        .sidebar-menu li:last-child {
            border-bottom: none;
        }

        .sidebar-menu a {
            display: block;
            padding: 15px 20px;
            color: #666;
            text-decoration: none;
            transition: all 0.3s ease;
            font-size: 14px;
        }

        .sidebar-menu a:hover {
            background: #e9ecef;
            color: #333;
            padding-left: 25px;
        }

        .sidebar-menu a.active {
            background: #007bff;
            color: #fff;
            border-left: 4px solid #0056b3;
        }

        .sidebar-menu a.active:hover {
            background: #0056b3;
            color: #fff;
        }

        /* 右侧内容区 */
        .page-main {
            flex: 1;
            display: flex;
            flex-direction: column;
        }

        .page-header {
            padding: 20px 30px;
            border-bottom: 1px solid #eee;
            background: #f8f9fa;
        }

        .page-title {
            font-size: 24px;
            color: #333;
            margin: 0;
            font-weight: 600;
            line-height: 1.3;
        }

        .page-meta {
            margin-top: 8px;
            color: #666;
            font-size: 13px;
        }

        .page-content {
            flex: 1;
            padding: 30px;
            line-height: 1.8;
            font-size: 15px;
            color: #333;
            overflow-y: auto;
        }
        
        .page-content h1,
        .page-content h2,
        .page-content h3,
        .page-content h4,
        .page-content h5,
        .page-content h6 {
            color: #2c3e50;
            margin-top: 25px;
            margin-bottom: 12px;
            font-weight: 600;
        }

        .page-content h1 {
            font-size: 20px;
            border-bottom: 2px solid #007bff;
            padding-bottom: 8px;
        }

        .page-content h2 {
            font-size: 18px;
            border-left: 4px solid #007bff;
            padding-left: 12px;
        }

        .page-content h3 {
            font-size: 16px;
        }

        .page-content p {
            margin-bottom: 12px;
            line-height: 1.7;
        }

        /* 只对没有内联text-align样式的段落应用justify */
        .page-content p:not([style*="text-align"]) {
            text-align: justify;
        }
        
        .page-content ul,
        .page-content ol {
            margin: 15px 0;
            padding-left: 30px;
        }
        
        .page-content li {
            margin-bottom: 8px;
        }
        
        .page-content img {
            max-width: 100%;
            height: auto;
            border-radius: 5px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            margin: 15px 0;
        }
        
        .page-content blockquote {
            background: #f8f9fa;
            border-left: 4px solid #3498db;
            margin: 20px 0;
            padding: 15px 20px;
            font-style: italic;
            color: #666;
        }
        
        .page-content table {
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
            border: 1px solid #ddd;
        }
        
        .page-content table th,
        .page-content table td {
            padding: 12px;
            text-align: left;
            border-bottom: 1px solid #ddd;
        }
        
        .page-content table th {
            background-color: #f8f9fa;
            font-weight: 600;
            color: #333;
        }
        
        .page-content table tr:hover {
            background-color: #f5f5f5;
        }
        
        .page-content code {
            background: #f4f4f4;
            padding: 2px 6px;
            border-radius: 3px;
            font-family: 'Courier New', monospace;
            font-size: 14px;
            color: #e74c3c;
        }
        
        .page-content pre {
            background: #f4f4f4;
            padding: 15px;
            border-radius: 5px;
            overflow-x: auto;
            margin: 15px 0;
        }
        
        .page-content pre code {
            background: none;
            padding: 0;
            color: #333;
        }
        


        /* 响应式设计 */
        @media (max-width: 768px) {
            .page-wrapper {
                flex-direction: column;
            }

            .page-sidebar {
                width: 100%;
                border-right: none;
                border-bottom: 1px solid #eee;
                border-radius: 5px 5px 0 0;
            }

            .sidebar-menu {
                display: flex;
                overflow-x: auto;
            }

            .sidebar-menu li {
                border-bottom: none;
                border-right: 1px solid #eee;
                white-space: nowrap;
            }

            .sidebar-menu li:last-child {
                border-right: none;
            }

            .sidebar-menu a {
                padding: 12px 15px;
            }

            .page-header,
            .page-content,
            .page-actions {
                padding: 15px;
            }

            .page-title {
                font-size: 20px;
            }

            .page-content {
                font-size: 14px;
            }

            .page-content h1 {
                font-size: 18px;
            }

            .page-content h2 {
                font-size: 16px;
            }

            .page-content h3 {
                font-size: 15px;
            }
        }
    </style>
</head>
<body>
        <!-- 顶部 -->
	<div class="yui-top  yui-1200">
		<div class="yui-top-center">
			<div class="yui-top-left yui-left">
				<a href="https://www.botou.net/">1网站首页</a>
				<a href="#">移动版</a>
				<a href="#">微信公众号</a>
				<a href="#">快速发布</a>
			</div>

			<div class="yui-top-right yui-right yui-text-right">
				<a href="#">登录</a><a href="#">注册</a><div class="yui-top-dropdown">
					<span class="yui-top-dropdown-btn">会员中心</span>
					<ul class="yui-top-dropdown-menu">
						<li><a href="#">我的信息</a></li>
						<li><a href="#">我的收藏</a></li>
						<li><a href="#">账号设置</a></li>
					</ul>
				</div><div class="yui-top-dropdown">
					<span class="yui-top-dropdown-btn">商家中心</span>
					<ul class="yui-top-dropdown-menu">
						<li><a href="#">商家入驻</a></li>
						<li><a href="#">商家管理</a></li>
						<li><a href="#">营销推广</a></li>
					</ul>
				</div><div class="yui-top-dropdown">
					<span class="yui-top-dropdown-btn">网站导航</span>
					<ul class="yui-top-dropdown-menu">
						<li><a href="#">关于我们</a></li>
						<li><a href="#">联系我们</a></li>
						<li><a href="#">使用帮助</a></li>
					</ul>
				</div>
			</div>
		</div>
	</div>
        <!-- 页面切换导航 -->
        <!-- <div class="page-switch-nav">
            <div class="yui-1200">
                <a href="index.htm" class="active">首页</a>
                <a href="list.htm">列表页</a>
                <a href="view.htm">详情页</a>
            </div>
        </div> -->
	<!-- header-->
	<div class="yui-header yui-1200">

		<div class="yui-t yui-c-box">
			<div class="yui-logo">
				<a href="https://www.botou.net/"><img src="/template/pc/images/logo.png" alt="泊头生活网" srcset=""></a>
			</div>
			<div class="yui-cimg"></div>
			<!--form select -->
			<div class="yui-form">
				<div class="yui-select">
					<!-- <div class="mod_select">
						<div class="select_box">
							<span class="select_txt">信息</span>
							<span class="select-icon"></span>
							<ul class="option">
								<li>信息</li>
								<li>帖子</li>

							</ul>
						</div>
					</div> -->
					<form action="/search.php" method="get" id="header-search-form">

						<input type="hidden" name="show" value="title" />
						<input type="hidden" name="tempid" value="1" />
						<input type="hidden" name="tbname" value="info">
						<input type="text" name="keyword"  class="import" placeholder="请输入关键字" id="header-search-input">
						<input type="submit" class="btn-search" id="header-search-btn" value="搜   索">
					</form>
				</div>
				<div class="yui-select-bottom-text"></div>
			</div>
			<div class="yui-fabu" style="float:right;">
				<button onClick="location.href='/post.php'"><a href="/post.php" target="_blank">免费发布信息</a></button>
			</div>
			<!-- form end -->
		</div>
	</div>
	<div class="yui-clear"></div>
	<div class="yui-nav mt20  yui-1200">
		<ul>
			<li <?php if(!null !== ($current_page ?? null) || $current_page == 'index'): ?>class='nav-cur'<?php endif; ?>><a href="/">首页</a></li>
			<?php 
			// 直接从数据库获取分类数据
			$categories = getCategories();
			
			// 筛选一级分类并排序
			$topCategories = array();
			foreach ($categories as $cat) {
				if ($cat['parent_id'] == 0 && $cat['status'] == 1) {
					$topCategories[] = $cat;
				}
			}
			
			// 按排序值升序排列
			usort($topCategories, function($a, $b) {
				if ($a['sort_order'] == $b['sort_order']) {
					return $a['id'] - $b['id']; // 如果排序值相同，按ID升序
				}
				return $a['sort_order'] - $b['sort_order']; // 按排序值升序
			});
			
			// 输出导航菜单
			foreach ($topCategories as $cat) {
				echo '<li><a href="/'.$cat['pinyin'].'/">'.$cat['name'].'</a></li>';
			}
			 ?>
			<li <?php if(null !== ($current_page ?? null) && $current_page == 'news'): ?>class='nav-cur'<?php endif; ?>><a href="/news.php">新闻中心</a></li>
		</ul>
	</div>

	<script>
	// Header搜索加载状态管理 - 使用多种方式确保兼容性
	(function() {
		function initHeaderSearch() {
			var headerSearchForm = document.getElementById('header-search-form');
			if (headerSearchForm) {
				headerSearchForm.addEventListener('submit', function(e) {
					var input = document.getElementById('header-search-input');
					var keyword = input ? input.value.trim() : '';

					if (keyword) {
						showHeaderSearchLoading();
					}
				});
			}
		}

		function showHeaderSearchLoading() {
			var searchBtn = document.getElementById('header-search-btn');

			if (searchBtn) {
				searchBtn.value = '搜索中...';
				searchBtn.disabled = true;
				searchBtn.style.backgroundColor = '#6c757d';
				searchBtn.style.cursor = 'not-allowed';

				// 添加调试信息
				console.log('Header搜索加载状态已激活');
			}
		}

		function hideHeaderSearchLoading() {
			var searchBtn = document.getElementById('header-search-btn');

			if (searchBtn) {
				searchBtn.value = '搜   索';
				searchBtn.disabled = false;
				searchBtn.style.backgroundColor = '#3092d5';
				searchBtn.style.cursor = 'pointer';
			}
		}

		// 多种初始化方式确保兼容性
		if (document.readyState === 'loading') {
			document.addEventListener('DOMContentLoaded', initHeaderSearch);
		} else {
			initHeaderSearch();
		}

		// 如果有jQuery，也用jQuery方式绑定
		if (typeof $ !== 'undefined') {
			$(document).ready(function() {
				$('#header-search-form').on('submit', function(e) {
					var keyword = $('#header-search-input').val().trim();
					if (keyword) {
						showHeaderSearchLoading();
					}
				});
			});
		}

		// 暴露函数到全局作用域，方便调试
		window.showHeaderSearchLoading = showHeaderSearchLoading;
		window.hideHeaderSearchLoading = hideHeaderSearchLoading;
	})();
	</script>

	<?php if($site_analytics): ?>
	<!-- 网站统计代码 -->
	<?php echo $site_analytics ?? ""; ?>
	<?php endif; ?>

    
    <!-- 广告栏 -->
    <div class="mo-box yui-1200">
        <a href="https://www.botou.net/aboutus/tuiguang.html" target="_blank" title="泊头生活网广告招商">
            <img src="/template/pc/images/2022112309104176804697_1240_90.png" alt="广告位">
        </a>
    </div>
    
    <div class="yui-clear"></div>
    
    <!-- 主内容区域 -->
    <div class="yui-content yui-1200">
        <div class="page-wrapper">
            <!-- 左侧导航 -->
            <div class="page-sidebar">
                <div class="sidebar-header">网站介绍</div>
                <ul class="sidebar-menu">
                    <?php if(null !== ($nav_pages ?? null) && is_array($nav_pages)): foreach($nav_pages as $nav_page): ?>
                    <li>
                        <a href="<?php echo (isset($nav_page['url'])) ? $nav_page['url'] : ""; ?>"<?php if($nav_page['is_current']): ?> class="active"<?php endif; ?>>
                            <?php echo (isset($nav_page['title'])) ? $nav_page['title'] : ""; ?>
                        </a>
                    </li>
                    <?php endforeach; endif; ?>
                </ul>
            </div>

            <!-- 右侧主内容 -->
            <div class="page-main">
                <!-- 页面头部 -->
                <div class="page-header">
                    <h1 class="page-title"><?php echo $title ?? ""; ?></h1>
                    <div class="page-meta">
                        <span><i class="fas fa-clock"></i> 更新时间：<?php echo date('Y-m-d H:i', $page['updated_at']); ?></span>
                    </div>
                </div>

                <!-- 页面内容 -->
                <div class="page-content">
                    <?php echo $content; ?>
                </div>
            </div>
        </div>
    </div>
    
    <div class="yui-footer">
    <div class="yui-1200">
        <div class="footer-content bg-white">
            <!-- 友情链接区域 -->
          
            <p class="footer-nav">
                <a href="https://www.botou.net/" title="泊头生活网">网站首页</a>
                <a href="https://www.botou.net/aboutus/tuiguang.html" target="_blank">广告服务</a>
                <a href="https://www.botou.net/aboutus/shenmin.html" target="_blank">法律声明</a>
                <a href="https://www.botou.net/aboutus/about.html" target="_blank">网站介绍</a>
                <a href="https://www.botou.net/aboutus/contactus.html" target="_blank">联系我们</a>
                <a href="https://www.botou.net/aboutus/job.html" target="_blank">招聘信息</a>
            </p>
            <p class="footer-disclaimer">2本站信息均由网民发表,不代表本网站立场,如侵犯了您的权利请致电投诉</p>
            <p class="footer-disclaimer">客服电话： &nbsp; 客服邮箱：<font><EMAIL></font> <a href="http://cyberpolice.mps.gov.cn/wfjb/" target="_blank" rel="nofollow">网络违法犯罪举报网站</a></p>
            <p class="footer-copyright"><?php if($site_copyright): ?><?php echo $site_copyright ?? ""; ?><?php else: ?>Copyright © 2024 分类信息网站 All Rights Reserved<?php endif; ?></p>
            <?php if($site_icp): ?><p class="footer-copyright"><a href="https://beian.miit.gov.cn/" target="_blank" id="footericp" rel="nofollow"><?php echo $site_icp ?? ""; ?></a></p><?php endif; ?>
        </div>
    </div>
</div>
    
    <script>
    // 页面加载完成后的处理
    document.addEventListener('DOMContentLoaded', function() {
        // 为外部链接添加新窗口打开
        const links = document.querySelectorAll('.page-content a[href^="http"]');
        links.forEach(function(link) {
            if (!link.hostname || link.hostname !== window.location.hostname) {
                link.target = '_blank';
                link.rel = 'noopener noreferrer';
            }
        });
        
        // 图片点击放大功能
        const images = document.querySelectorAll('.page-content img');
        images.forEach(function(img) {
            img.style.cursor = 'pointer';
            img.addEventListener('click', function() {
                const overlay = document.createElement('div');
                overlay.style.cssText = `
                    position: fixed;
                    top: 0;
                    left: 0;
                    width: 100%;
                    height: 100%;
                    background: rgba(0,0,0,0.8);
                    display: flex;
                    justify-content: center;
                    align-items: center;
                    z-index: 9999;
                    cursor: pointer;
                `;
                
                const enlargedImg = document.createElement('img');
                enlargedImg.src = this.src;
                enlargedImg.style.cssText = `
                    max-width: 90%;
                    max-height: 90%;
                    border-radius: 5px;
                    box-shadow: 0 4px 20px rgba(0,0,0,0.5);
                `;
                
                overlay.appendChild(enlargedImg);
                document.body.appendChild(overlay);
                
                overlay.addEventListener('click', function() {
                    document.body.removeChild(overlay);
                });
            });
        });
    });
    </script>
</body>
</html>
