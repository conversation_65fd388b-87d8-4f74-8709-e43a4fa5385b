// 站点访问控制
(function() {
    // 检查是否是本地访问
    function isLocalAccess() {
        return window.location.hostname === 'localhost' || 
               window.location.hostname === '127.0.0.1' ||
               window.location.hostname.startsWith('192.168.') ||
               window.location.hostname.startsWith('10.') ||
               window.location.hostname.includes('.local');
    }

    // 如果是本地访问，直接返回，不做任何限制
    if (isLocalAccess()) {
        return;
    }

    // 创建维护页面遮罩
    function createMaintenanceOverlay() {
        const overlay = document.createElement('div');
        overlay.style.cssText = `
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: #fff;
            z-index: 999999;
            display: flex;
            justify-content: center;
            align-items: center;
            flex-direction: column;
        `;

        const maintenanceImage = document.createElement('img');
        maintenanceImage.src = '/template/pc/images/maintenance.png';  // 确保此维护图片存在
        maintenanceImage.style.cssText = `
            max-width: 300px;
            margin-bottom: 30px;
        `;

        const message = document.createElement('div');
        message.style.cssText = `
            font-size: 24px;
            color: #333;
            text-align: center;
            margin-bottom: 20px;
        `;
        message.innerHTML = '网站维护中<br>Site Under Maintenance';

        const subMessage = document.createElement('div');
        subMessage.style.cssText = `
            font-size: 16px;
            color: #666;
            text-align: center;
            max-width: 600px;
            padding: 0 20px;
        `;
        subMessage.innerHTML = '我们正在进行系统升级和维护，给您带来不便深表歉意。<br>请稍后再试。';

        overlay.appendChild(maintenanceImage);
        overlay.appendChild(message);
        overlay.appendChild(subMessage);
        document.body.appendChild(overlay);
        document.body.style.overflow = 'hidden';

        // 阻止右键和F12
        document.addEventListener('contextmenu', e => e.preventDefault());
        document.addEventListener('keydown', e => {
            if (e.keyCode === 123) e.preventDefault(); // F12
            if (e.ctrlKey && e.shiftKey && e.keyCode === 73) e.preventDefault(); // Ctrl+Shift+I
            if (e.ctrlKey && e.shiftKey && e.keyCode === 74) e.preventDefault(); // Ctrl+Shift+J
            if (e.ctrlKey && e.keyCode === 85) e.preventDefault(); // Ctrl+U
        });
    }

    // 检查IP地址
    fetch('https://ipapi.co/json/')
        .then(response => response.json())
        .then(data => {
            // 检查是否是备案检查IP（这里列出一些常见的检查来源地）
            const restrictedLocations = [
                'beijing',        // 北京
                'shanghai',       // 上海
                'guangzhou',      // 广州
                'shenzhen',      // 深圳
                'hangzhou'        // 杭州
            ];
            
            const city = (data.city || '').toLowerCase();
            const region = (data.region || '').toLowerCase();
            
            // 如果IP来自限制地区，显示维护页面
            if (restrictedLocations.some(location => 
                city.includes(location) || region.includes(location))) {
                createMaintenanceOverlay();
            }
        })
        .catch(error => {
            console.error('IP check failed:', error);
            // 如果IP检查失败，为安全起见也显示维护页面
            createMaintenanceOverlay();
        });
})();