# 分类信息网站开发文档

## 项目概述
本项目是一个多端适配的分类信息网站系统，采用模块化开发模式，支持信息发布、管理、搜索、置顶等核心功能。系统设计轻量高效，适应各类分类信息场景需求。

## 系统环境要求
- PHP: 7.3 ~ 8.2
- MySQL: 5.7 ~ 8.0
- Web服务器: Apache/Nginx
- 运行内存: 建议2GB以上
- 存储空间: 根据业务量，建议10GB以上

## 技术架构
- **开发模式**: 面向过程，模块化开发
- **模板引擎**: 自主开发的轻量级模板引擎，支持变量输出、条件判断、循环等基本功能
- **缓存系统**: 支持页面缓存和数据缓存，可配置缓存时间和清理策略
- **图片处理**: 支持图片上传、压缩、缩略图生成和水印添加
- **短信系统**: 集成阿里云短信服务，支持验证码发送和防刷机制

## 多端适配
| 端口 | 域名 | 模板目录 | 技术特点 | 说明 |
|------|------|----------|---------|------|
| PC端 | www.fenlei.com | template/pc/ | 完整功能界面 | 默认访问端 |
| 手机端 | m.fenlei.com | template/m/ | 触屏优化，响应式 | 自动识别跳转 |
| 微信端 | wx.fenlei.com | template/wx/ | 微信API集成 | 微信专属界面 |
| APP端 | app.fenlei.com | template/app/ | JSON接口 | APP接口支持 |

## 目录结构
```
/
├── index.php           - 网站首页入口
├── category.php        - 分类列表页
├── view.php           - 信息详情页
├── post.php           - 信息发布页
├── manage.php         - 信息管理页
├── search.php         - 搜索功能
├── report.php         - 举报功能
├── top.php            - 置顶购买
├── ajax.php           - AJAX请求处理
├── .htaccess          - URL重写规则
├── admin/             - 后台管理目录
├── api/               - API接口目录
├── config/            - 配置文件目录
│   ├── config.php         - 主配置文件
│   ├── config.db.php      - 数据库配置
│   └── theme_settings.php - 主题设置
├── include/           - 核心功能文件
│   ├── common.inc.php      - 公共初始化
│   ├── mysql.class.php     - 数据库操作类
│   ├── global.fun.php      - 全局函数库
│   ├── template.class.php  - 模板引擎类
│   ├── image.fun.php       - 图片处理函数
│   ├── captcha.class.php   - 验证码类
│   ├── aliyun_sms.class.php- 阿里云短信
│   ├── sms.fun.php         - 短信功能
│   ├── info.fun.php        - 信息处理函数
│   ├── pagecache.class.php - 页面缓存类
│   └── phpcache.class.php  - PHP缓存类
├── template/          - 模板文件目录
│   ├── pc/            - PC端模板
│   ├── m/             - 手机端模板
│   ├── wx/            - 微信端模板
│   └── app/           - APP端模板
├── data/              - 数据存储目录
│   ├── cache/         - 缓存文件
│   └── compiled/      - 编译后的模板
├── uploads/           - 上传文件目录（按年份分类）
│   ├── images/        - 图片存储
│   │   ├── 2025/      - 2025年图片
│   │   │   ├── 0720/  - 07月20日
│   │   │   └── ...    - 其他日期
│   │   └── ...        - 其他年份
│   ├── videos/        - 视频存储（按年份分类）
│   └── files/         - 文件存储（按年份分类）
├── static/            - 静态资源目录
│   ├── css/           - 样式文件
│   ├── js/            - JavaScript文件
│   └── images/        - 系统图片
└── member/            - 会员相关功能
```

## 核心功能模块详解

### 1. 信息发布功能
- **字段自定义**: 支持文本、下拉、多选、图片等多种字段类型
- **图片处理**: 多图上传，自动压缩和缩略图生成
- **位置服务**: 支持地图选点和自动定位
- **验证机制**: 手机验证码、IP限制、防刷措施
- **分类属性**: 不同分类可配置不同表单字段和验证规则
- **内容审核**: 支持关键词过滤、敏感图片识别

### 2. 信息管理功能
- **密码验证**: 发布时设置管理密码，管理时验证
- **信息修改**: 支持标题、内容、图片等修改
- **信息删除**: 支持彻底删除和软删除
- **信息刷新**: 更新发布时间，提升排名
- **状态管理**: 发布、待审核、已审核、已过期、已删除等状态流转
- **操作日志**: 记录所有修改操作历史

### 3. 置顶功能
- **分级置顶**: 支持首页置顶、分类置顶、区域置顶
- **时间设置**: 按小时、天、周、月计费
- **显示效果**: 置顶信息样式特殊标记
- **套餐管理**: 多种置顶组合方案
- **在线支付**: 支持微信支付、支付宝等
- **自动过期**: 到期自动取消置顶状态

### 4. 图片处理系统
- **多图上传**: 支持批量上传，AJAX无刷新
- **图片压缩**: 自动调整大小，节省空间
- **缩略图生成**: 不同场景生成不同尺寸缩略图
- **水印添加**: 支持文字水印和图片水印
- **图片鉴别**: 接入第三方API进行鉴黄处理

### 5. 短信系统
- **验证码发送**: 随机生成6位数字验证码
- **模板管理**: 支持自定义短信模板
- **发送记录**: 记录所有短信发送日志
- **防刷机制**: 同一手机号60秒内限制发送1次
- **黑名单管理**: 异常号码自动加入黑名单

### 6. 缓存系统
- **页面缓存**: 整页静态化，减轻服务器压力
- **数据缓存**: 频繁查询数据的缓存处理
- **缓存策略**: 支持定时、手动、修改触发等清理方式
- **缓存监控**: 提供缓存命中率和使用统计

## 数据库设计详解

### 主要数据表结构

#### 1. 信息主表(posts)
```sql
CREATE TABLE `posts` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `title` varchar(255) NOT NULL COMMENT '信息标题',
  `content` text COMMENT '详细内容',
  `category_id` int(11) NOT NULL COMMENT '分类ID',
  `region_id` int(11) NOT NULL COMMENT '区域ID',
  `contact_name` varchar(50) DEFAULT NULL COMMENT '联系人',
  `contact_phone` varchar(20) DEFAULT NULL COMMENT '联系电话',
  `contact_wx` varchar(50) DEFAULT NULL COMMENT '微信号',
  `manage_pwd` varchar(32) DEFAULT NULL COMMENT '管理密码',
  `views` int(11) DEFAULT '0' COMMENT '浏览次数',
  `top_level` tinyint(1) DEFAULT '0' COMMENT '置顶级别',
  `top_expire_time` int(11) DEFAULT NULL COMMENT '置顶到期时间',
  `status` tinyint(1) DEFAULT '1' COMMENT '状态:0删除,1正常,2待审核',
  `create_time` int(11) DEFAULT NULL COMMENT '创建时间',
  `update_time` int(11) DEFAULT NULL COMMENT '更新时间',
  `ip` varchar(15) DEFAULT NULL COMMENT '发布IP',
  PRIMARY KEY (`id`),
  KEY `idx_category` (`category_id`),
  KEY `idx_region` (`region_id`),
  KEY `idx_status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='信息主表';
```

#### 2. 分类表(categories)
```sql
CREATE TABLE `categories` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `parent_id` int(11) DEFAULT '0' COMMENT '父分类ID',
  `name` varchar(50) NOT NULL COMMENT '分类名称',
  `icon` varchar(255) DEFAULT NULL COMMENT '分类图标',
  `sort_order` int(11) DEFAULT '0' COMMENT '排序',
  `seo_title` varchar(255) DEFAULT NULL COMMENT 'SEO标题',
  `seo_keywords` varchar(255) DEFAULT NULL COMMENT 'SEO关键词',
  `seo_description` varchar(255) DEFAULT NULL COMMENT 'SEO描述',
  `status` tinyint(1) DEFAULT '1' COMMENT '状态:0禁用,1启用',
  PRIMARY KEY (`id`),
  KEY `idx_parent` (`parent_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='分类表';
```

#### 3. 自定义字段表(fields)
```sql
CREATE TABLE `fields` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `category_id` int(11) NOT NULL COMMENT '分类ID',
  `name` varchar(50) NOT NULL COMMENT '字段名称',
  `field_key` varchar(50) NOT NULL COMMENT '字段键名',
  `type` varchar(20) NOT NULL COMMENT '字段类型',
  `options` text COMMENT '选项值(用于下拉、多选)',
  `required` tinyint(1) DEFAULT '0' COMMENT '是否必填',
  `sort_order` int(11) DEFAULT '0' COMMENT '排序',
  `status` tinyint(1) DEFAULT '1' COMMENT '状态',
  PRIMARY KEY (`id`),
  KEY `idx_category` (`category_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='自定义字段表';
```

#### 4. 字段值表(field_values)
```sql
CREATE TABLE `field_values` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `post_id` int(11) NOT NULL COMMENT '信息ID',
  `field_id` int(11) NOT NULL COMMENT '字段ID',
  `value` text COMMENT '字段值',
  PRIMARY KEY (`id`),
  KEY `idx_post` (`post_id`),
  KEY `idx_field` (`field_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='字段值表';
```

#### 5. 附件表(attachments)
```sql
CREATE TABLE `attachments` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `post_id` int(11) NOT NULL COMMENT '信息ID',
  `file_path` varchar(255) NOT NULL COMMENT '文件路径',
  `thumb_path` varchar(255) DEFAULT NULL COMMENT '缩略图路径',
  `file_size` int(11) DEFAULT NULL COMMENT '文件大小',
  `file_type` varchar(50) DEFAULT NULL COMMENT '文件类型',
  `upload_time` int(11) DEFAULT NULL COMMENT '上传时间',
  `sort_order` int(11) DEFAULT '0' COMMENT '排序',
  PRIMARY KEY (`id`),
  KEY `idx_post` (`post_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='附件表';
```

## 前端开发指南

### 设计规范
- **色彩系统**
  - 主色调: #1b68ff（蓝色）
  - 成功色: #3ad29f（绿色）
  - 警告色: #eea303（黄色）
  - 危险色: #f82f58（红色）
  - 文本色: #001a4e（深蓝）
  - 次要文本: #6c757d（灰色）

- **间距规范**
  - 基础间距: 8px
  - 常用间距: 8px, 16px, 24px, 32px

- **字体规范**
  - 默认字体: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto
  - 默认大小: 14px
  - 标题大小: 18px, 16px
  - 小文本: 12px

### 组件系统
后台管理系统已包含多种组件，开发中可直接使用：

1. **布局组件**
   - 侧边栏导航（支持折叠）
   - 顶部导航栏（带面包屑）
   - 内容区布局（响应式）

2. **表单组件**
   - 输入框（支持验证）
   - 下拉选择框
   - 单选/复选框
   - 开关按钮
   - 文件上传组件
   - 富文本编辑器

3. **数据展示**
   - 表格组件（支持排序和过滤）
   - 统计卡片
   - 标签
   - 进度条
   - 警告框

4. **交互组件**
   - 按钮（多种样式）
   - 选项卡
   - 模态框
   - 提示框

## 后端开发指南

### 核心类/函数用法

#### 数据库操作类 (mysql.class.php)
```php
// 初始化数据库连接
$db = new MySQL();

// 查询多条记录
$posts = $db->query("SELECT * FROM posts WHERE status = ? ORDER BY id DESC LIMIT 10", [1]);

// 查询单条记录
$post = $db->getOne("SELECT * FROM posts WHERE id = ?", [$id]);

// 插入记录
$data = [
    "title" => "测试标题",
    "content" => "测试内容",
    "category_id" => 1,
    "region_id" => 2,
    "create_time" => time()
];
$id = $db->insert("posts", $data);

// 更新记录
$db->update("posts", ["title" => "新标题"], "id = ?", [$id]);

// 删除记录
$db->delete("posts", "id = ?", [$id]);

// 事务处理
$db->beginTransaction();
try {
    $db->insert("posts", $data);
    $db->update("categories", ["count" => $db->raw("count + 1")], "id = ?", [1]);
    $db->commit();
} catch (Exception $e) {
    $db->rollback();
}
```

#### 模板引擎 (template.class.php)
```php
// 初始化模板引擎
$tpl = new Template();

// 设置模板目录
$tpl->setTemplateDir("template/pc/");

// 分配变量
$tpl->assign("title", "网站标题");
$tpl->assign("keywords", "SEO关键词");
$tpl->assign("description", "SEO描述");
$tpl->assign("posts", $posts_array);

// 显示模板
$tpl->display("index.htm");

// 获取编译后的内容
$content = $tpl->fetch("index.htm");

// 缓存模板
$tpl->setCaching(true);
$tpl->setCacheTTL(3600); // 缓存1小时
$tpl->display("index.htm");
```

#### 图片处理函数 (image.fun.php)
```php
// 生成缩略图
$thumb_path = create_thumb($original_path, 300, 200);

// 添加水印
add_watermark($image_path, "text", "网站水印");
add_watermark($image_path, "image", "static/images/watermark.png");

// 图片内容检测（鉴黄）
$is_safe = check_image_content($image_path);

// 图片压缩
compress_image($original_path, $target_path, 80);

// 批量处理图片
batch_process_images($files_array, 800, 600, true, "text", "水印");
```

#### 短信功能 (sms.fun.php)
```php
// 生成随机验证码
$code = generate_sms_code(6);

// 发送验证码短信
$result = send_verify_code($phone, $code);

// 验证短信验证码
$is_valid = verify_sms_code($phone, $input_code);

// 发送通知短信
$result = send_notice_sms($phone, 'post_approved', [
    'title' => '您发布的信息已审核通过',
    'time' => date('Y-m-d H:i:s')
]);

// 检查发送频率限制
$can_send = check_sms_limit($phone);
```

#### 缓存操作 (phpcache.class.php)
```php
// 初始化缓存
$cache = new PHPCache();

// 设置缓存
$cache->set("home_posts", $posts, 3600); // 缓存1小时

// 获取缓存
$data = $cache->get("home_posts");
if ($data === false) {
    // 缓存不存在或已过期，重新获取数据
    $data = get_fresh_data();
    $cache->set("home_posts", $data, 3600);
}

// 删除缓存
$cache->delete("home_posts");

// 清空所有缓存
$cache->clear();

// 缓存标签功能
$cache->setTag("post_1", ["posts", "category_1"]);
$cache->clearByTag("category_1"); // 清除所有标记为category_1的缓存
```

### API开发规范

1. **接口请求格式**
```
/api/[模块]/[操作]?param1=value1&param2=value2
```

2. **返回数据格式**
```json
{
    "code": 0,           // 0成功，非0为错误码
    "msg": "success",    // 成功或错误信息
    "data": {            // 返回的数据
        // 具体数据
    }
}
```

3. **错误码规范**
- 0: 成功
- 1001-1999: 用户相关错误
- 2001-2999: 信息发布相关错误
- 3001-3999: 支付相关错误
- 9001-9999: 系统相关错误

4. **API安全措施**
- 时间戳验证
- 签名机制
- 接口限流
- CORS设置

## 性能优化策略

1. **数据库优化**
- 合理设计索引
- 分表策略（按时间、地区分表）
- 定期数据清理
- SQL查询优化

2. **缓存优化**
- 多级缓存策略
- 热点数据预加载
- 缓存更新策略
- 缓存穿透防护

3. **前端优化**
- 静态资源合并压缩
- 图片懒加载
- CDN加速
- 浏览器缓存策略

4. **服务器优化**
- OPCache启用
- PHP-FPM调优
- Nginx参数优化
- 静态文件分离

## 部署说明

### 环境配置
1. **Web服务器配置**
   - **Apache**:
     ```apache
     <VirtualHost *:80>
         ServerName www.fenlei.com
         DocumentRoot /path/to/project
         <Directory "/path/to/project">
             Options FollowSymLinks
             AllowOverride All
             Require all granted
         </Directory>
     </VirtualHost>
     ```
   - **Nginx**:
     ```nginx
     server {
         listen 80;
         server_name www.fenlei.com;
         root /path/to/project;
         
         location / {
             index index.php index.html;
             try_files $uri $uri/ /index.php?$query_string;
         }
         
         location ~ \.php$ {
             fastcgi_pass 127.0.0.1:9000;
             fastcgi_index index.php;
             fastcgi_param SCRIPT_FILENAME $document_root$fastcgi_script_name;
             include fastcgi_params;
         }
     }
     ```

2. **PHP配置优化**
   ```ini
   memory_limit = 256M
   upload_max_filesize = 20M
   post_max_size = 20M
   max_execution_time = 30
   date.timezone = Asia/Shanghai
   opcache.enable = 1
   opcache.memory_consumption = 128
   ```

3. **目录权限设置**
   ```bash
   chmod -R 755 /path/to/project
   chmod -R 777 /path/to/project/upload
   chmod -R 777 /path/to/project/data
   ```

### 安装步骤
1. 创建数据库
2. 导入SQL文件
3. 修改数据库配置文件 `config/config.db.php`
4. 设置网站基本信息
5. 配置URL重写规则
6. 测试各端访问

## 常见问题与解决方案

1. **图片上传失败**
   - 检查目录权限
   - 检查PHP上传配置
   - 检查磁盘空间

2. **数据库连接错误**
   - 验证数据库配置
   - 检查MySQL服务状态
   - 确认用户权限设置

3. **缓存不生效**
   - 检查缓存目录权限
   - 验证缓存配置
   - 清理缓存后重试

4. **短信发送失败**
   - 检查阿里云账号配置
   - 验证签名和模板状态
   - 查看短信发送日志

## 技术支持与更新

- **官方文档**: http://www.fenlei.com/doc
- **技术支持**: <EMAIL>
- **更新日志**: 查看CHANGELOG.md文件
- **问题报告**: 在项目GitHub上提交Issue
- **更新日期**: 2024年3月