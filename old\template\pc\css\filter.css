/* 分类与区域筛选美化 - 紧凑版 */
.filter-container {
  background-color: #fff;
  border-radius: 6px;
  margin-bottom: 10px;
  overflow: hidden;
  width: 100%;
  box-sizing: border-box;
}

.filter-section {
  padding: 15px 12px;
  display: flex;
  border-bottom: 1px solid #f5f5f5;
  position: relative;
}

.filter-section:last-child {
  border-bottom: none;
}

.filter-label {
  width: 50px;
  font-size: 14px;
  color: #333;
  font-weight: 600;
  line-height: 20px;
  padding-top: 0;
  flex-shrink: 0;
  display: flex;
  align-items: center;
}

.filter-options {
  flex: 1;
  display: flex;
  flex-wrap: wrap;
  gap: 2px;
  margin: 0;
  display: flex;
  align-items: center;
}

.filter-item {
  display: inline-block;
  padding: 2px 8px;
  font-size: 16px;
  color: #555;
  border-radius: 3px;
  text-decoration: none;

  line-height: 1.2;
  position: relative;

  margin-bottom: 2px;
  margin-right: 2px;
}

.filter-item:hover {
  color: #3092d5;
  background-color: rgba(196, 40, 77, 0.05);
}

.filter-item.active {
  background-color: #3092d5;
  color: #fff;
}

.filter-item span {
  margin-left: 2px;
  font-size: 13px;
  opacity: 0.85;
}

.filter-item.active span {
  color: rgba(255,255,255,0.9);
}

.filter-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

/* 紧凑布局 */
.filter-combined-row {
  display: flex;
  border-bottom: 1px solid #f5f5f5;
}

.compact-section {
  flex: 1;
  border-bottom: none;
  border-right: 1px solid #f5f5f5;
}

.compact-section:last-child {
  border-right: none;
}

/* 清除浮动 */
.clearfix:after {
  content: "";
  display: table;
  clear: both;
}

/* 保持右侧列表宽度一致 */
.yui-index-info-list {
  display: flex;
  flex-wrap: nowrap;
}

.yui-index-info-list-left {
  flex: 1;
  min-width: 0;
}

.yui-index-info-list-right {
  width: 300px;
  flex-shrink: 0;
}

/* 响应式调整 */
@media screen and (max-width: 768px) {
  .filter-section {
    flex-direction: column;
  }
  
  .filter-label {
    width: 100%;
    margin-bottom: 5px;
  }
  
  .filter-options {
    width: 100%;
  }
  
  .filter-combined-row {
    flex-direction: column;
  }
  
  .compact-section {
    border-right: none;
    border-bottom: 1px solid #f5f5f5;
  }
  
  .yui-index-info-list {
    flex-direction: column;
  }
  
  .yui-index-info-list-right {
    width: 100%;
    margin-left: 0;
    margin-top: 20px;
  }
}