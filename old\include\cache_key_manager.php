<?php
/**
 * 缓存键名管理器
 * 统一管理缓存键名生成策略，提高缓存命中率和管理效率
 * 兼容PHP 7.3-8.2
 */

if (!defined('IN_BTMPS')) {
    exit('Access Denied');
}

class CacheKeyManager {
    
    // 缓存键名前缀定义
    const PREFIX_CATEGORY = 'cat';
    const PREFIX_REGION = 'reg';
    const PREFIX_POST = 'post';
    const PREFIX_LIST = 'list';
    const PREFIX_INDEX = 'idx';
    const PREFIX_SEARCH = 'srch';
    const PREFIX_NEWS = 'news';
    const PREFIX_USER = 'user';
    const PREFIX_API = 'api';
    const PREFIX_TEMP = 'tmp';
    
    // 分隔符
    const SEPARATOR = '_';
    
    /**
     * 生成分类相关缓存键名
     */
    public static function categoryKey($type, $params = []) {
        switch ($type) {
            case 'all':
                return self::PREFIX_CATEGORY . self::SEPARATOR . 'all';
                
            case 'tree':
                return self::PREFIX_CATEGORY . self::SEPARATOR . 'tree';
                
            case 'info':
                $catId = isset($params['id']) ? $params['id'] : 0;
                return self::PREFIX_CATEGORY . self::SEPARATOR . 'info' . self::SEPARATOR . $catId;
                
            case 'posts':
                $catId = isset($params['cat_id']) ? $params['cat_id'] : 0;
                $page = isset($params['page']) ? $params['page'] : 1;
                $limit = isset($params['limit']) ? $params['limit'] : 30;
                $areaId = isset($params['area_id']) ? $params['area_id'] : 0;
                $template = isset($params['template']) ? $params['template'] : 'pc';
                $sort = isset($params['sort']) ? $params['sort'] : 'default';
                
                return self::PREFIX_CATEGORY . self::SEPARATOR . 'posts' . self::SEPARATOR . 
                       $catId . self::SEPARATOR . $page . self::SEPARATOR . $limit . self::SEPARATOR . 
                       $areaId . self::SEPARATOR . $template . self::SEPARATOR . $sort;
                
            case 'count':
                $catId = isset($params['cat_id']) ? $params['cat_id'] : 0;
                $subCatId = isset($params['sub_cat_id']) ? $params['sub_cat_id'] : 0;
                $areaId = isset($params['area_id']) ? $params['area_id'] : 0;
                $isProvince = isset($params['is_province']) ? ($params['is_province'] ? 1 : 0) : 0;
                
                return self::PREFIX_CATEGORY . self::SEPARATOR . 'count' . self::SEPARATOR . 
                       $catId . self::SEPARATOR . $subCatId . self::SEPARATOR . $areaId . self::SEPARATOR . $isProvince;
                
            default:
                return self::PREFIX_CATEGORY . self::SEPARATOR . $type;
        }
    }
    
    /**
     * 生成区域相关缓存键名
     */
    public static function regionKey($type, $params = []) {
        switch ($type) {
            case 'all':
                return self::PREFIX_REGION . self::SEPARATOR . 'all';
                
            case 'tree':
                return self::PREFIX_REGION . self::SEPARATOR . 'tree';
                
            case 'info':
                $regionId = isset($params['id']) ? $params['id'] : 0;
                return self::PREFIX_REGION . self::SEPARATOR . 'info' . self::SEPARATOR . $regionId;
                
            case 'children':
                $parentId = isset($params['parent_id']) ? $params['parent_id'] : 0;
                return self::PREFIX_REGION . self::SEPARATOR . 'children' . self::SEPARATOR . $parentId;
                
            default:
                return self::PREFIX_REGION . self::SEPARATOR . $type;
        }
    }
    
    /**
     * 生成信息详情相关缓存键名
     */
    public static function postKey($type, $params = []) {
        switch ($type) {
            case 'detail':
                $postId = isset($params['id']) ? $params['id'] : 0;
                $template = isset($params['template']) ? $params['template'] : 'pc';
                return self::PREFIX_POST . self::SEPARATOR . 'detail' . self::SEPARATOR . $postId . self::SEPARATOR . $template;
                
            case 'images':
                $postId = isset($params['id']) ? $params['id'] : 0;
                return self::PREFIX_POST . self::SEPARATOR . 'images' . self::SEPARATOR . $postId;
                
            case 'related':
                $postId = isset($params['id']) ? $params['id'] : 0;
                $catId = isset($params['cat_id']) ? $params['cat_id'] : 0;
                $limit = isset($params['limit']) ? $params['limit'] : 10;
                return self::PREFIX_POST . self::SEPARATOR . 'related' . self::SEPARATOR . 
                       $postId . self::SEPARATOR . $catId . self::SEPARATOR . $limit;
                
            case 'hot':
                $catId = isset($params['cat_id']) ? $params['cat_id'] : 0;
                $limit = isset($params['limit']) ? $params['limit'] : 10;
                return self::PREFIX_POST . self::SEPARATOR . 'hot' . self::SEPARATOR . $catId . self::SEPARATOR . $limit;
                
            case 'top':
                $limit = isset($params['limit']) ? $params['limit'] : 10;
                return self::PREFIX_POST . self::SEPARATOR . 'top' . self::SEPARATOR . $limit;
                
            case 'latest':
                $limit = isset($params['limit']) ? $params['limit'] : 10;
                return self::PREFIX_POST . self::SEPARATOR . 'latest' . self::SEPARATOR . $limit;
                
            default:
                return self::PREFIX_POST . self::SEPARATOR . $type;
        }
    }
    
    /**
     * 生成列表页相关缓存键名
     */
    public static function listKey($type, $params = []) {
        switch ($type) {
            case 'category':
                $template = isset($params['template']) ? $params['template'] : 'pc';
                $catId = isset($params['cat_id']) ? $params['cat_id'] : 0;
                $page = isset($params['page']) ? $params['page'] : 1;
                $limit = isset($params['limit']) ? $params['limit'] : 30;
                $areaId = isset($params['area_id']) ? $params['area_id'] : 0;
                $sort = isset($params['sort']) ? $params['sort'] : 'default';
                
                return self::PREFIX_LIST . self::SEPARATOR . 'cat' . self::SEPARATOR . 
                       $template . self::SEPARATOR . $catId . self::SEPARATOR . $page . self::SEPARATOR . 
                       $limit . self::SEPARATOR . $areaId . self::SEPARATOR . $sort;
                
            case 'search':
                $keyword = isset($params['keyword']) ? md5($params['keyword']) : '';
                $catId = isset($params['cat_id']) ? $params['cat_id'] : 0;
                $areaId = isset($params['area_id']) ? $params['area_id'] : 0;
                $page = isset($params['page']) ? $params['page'] : 1;
                $limit = isset($params['limit']) ? $params['limit'] : 30;
                
                return self::PREFIX_SEARCH . self::SEPARATOR . 'result' . self::SEPARATOR . 
                       $keyword . self::SEPARATOR . $catId . self::SEPARATOR . $areaId . self::SEPARATOR . 
                       $page . self::SEPARATOR . $limit;
                
            default:
                return self::PREFIX_LIST . self::SEPARATOR . $type;
        }
    }
    
    /**
     * 生成首页相关缓存键名
     */
    public static function indexKey($type, $params = []) {
        switch ($type) {
            case 'page':
                $template = isset($params['template']) ? $params['template'] : 'pc';
                return self::PREFIX_INDEX . self::SEPARATOR . 'page' . self::SEPARATOR . $template;
                
            case 'navigation':
                return self::PREFIX_INDEX . self::SEPARATOR . 'nav';
                
            case 'stats':
                return self::PREFIX_INDEX . self::SEPARATOR . 'stats';
                
            default:
                return self::PREFIX_INDEX . self::SEPARATOR . $type;
        }
    }
    
    /**
     * 生成新闻相关缓存键名
     */
    public static function newsKey($type, $params = []) {
        switch ($type) {
            case 'home':
                $page = isset($params['page']) ? $params['page'] : 1;
                $limit = isset($params['limit']) ? $params['limit'] : 10;
                $recommend = isset($params['recommend']) ? ($params['recommend'] ? 1 : 0) : 0;
                $top = isset($params['top']) ? ($params['top'] ? 1 : 0) : 0;
                
                return self::PREFIX_NEWS . self::SEPARATOR . 'home' . self::SEPARATOR . 
                       'p' . $page . self::SEPARATOR . $limit . self::SEPARATOR . 
                       'rec' . $recommend . self::SEPARATOR . 'top' . $top;
                
            case 'detail':
                $newsId = isset($params['id']) ? $params['id'] : 0;
                return self::PREFIX_NEWS . self::SEPARATOR . 'detail' . self::SEPARATOR . $newsId;
                
            case 'category':
                $catId = isset($params['cat_id']) ? $params['cat_id'] : 0;
                $page = isset($params['page']) ? $params['page'] : 1;
                $limit = isset($params['limit']) ? $params['limit'] : 10;
                
                return self::PREFIX_NEWS . self::SEPARATOR . 'cat' . self::SEPARATOR . 
                       $catId . self::SEPARATOR . $page . self::SEPARATOR . $limit;
                
            case 'hot':
                $limit = isset($params['limit']) ? $params['limit'] : 10;
                return self::PREFIX_NEWS . self::SEPARATOR . 'hot' . self::SEPARATOR . $limit;
                
            default:
                return self::PREFIX_NEWS . self::SEPARATOR . $type;
        }
    }
    
    /**
     * 生成API相关缓存键名
     */
    public static function apiKey($type, $params = []) {
        switch ($type) {
            case 'response':
                $endpoint = isset($params['endpoint']) ? $params['endpoint'] : '';
                $hash = isset($params['params_hash']) ? $params['params_hash'] : '';
                return self::PREFIX_API . self::SEPARATOR . 'resp' . self::SEPARATOR . $endpoint . self::SEPARATOR . $hash;
                
            default:
                return self::PREFIX_API . self::SEPARATOR . $type;
        }
    }
    
    /**
     * 生成临时缓存键名
     */
    public static function tempKey($type, $params = []) {
        $identifier = isset($params['id']) ? $params['id'] : uniqid();
        return self::PREFIX_TEMP . self::SEPARATOR . $type . self::SEPARATOR . $identifier;
    }
    
    /**
     * 解析缓存键名，获取类型和参数信息
     */
    public static function parseKey($key) {
        $parts = explode(self::SEPARATOR, $key);
        
        if (empty($parts)) {
            return ['type' => 'unknown', 'prefix' => '', 'params' => []];
        }
        
        $prefix = $parts[0];
        $type = isset($parts[1]) ? $parts[1] : '';
        $params = array_slice($parts, 2);
        
        return [
            'type' => $type,
            'prefix' => $prefix,
            'params' => $params,
            'full_key' => $key
        ];
    }
    
    /**
     * 根据键名前缀获取缓存级别
     */
    public static function getCacheLevelByPrefix($prefix) {
        switch ($prefix) {
            case self::PREFIX_CATEGORY:
            case self::PREFIX_REGION:
                return FileCache::CACHE_LEVEL_PERMANENT;
                
            case self::PREFIX_INDEX:
                return FileCache::CACHE_LEVEL_LONG;
                
            case self::PREFIX_SEARCH:
                return FileCache::CACHE_LEVEL_SHORT;
                
            case self::PREFIX_API:
            case self::PREFIX_TEMP:
                return FileCache::CACHE_LEVEL_TEMP;
                
            default:
                return FileCache::CACHE_LEVEL_MEDIUM;
        }
    }
    
    /**
     * 批量生成相关缓存键名（用于批量清理）
     */
    public static function getRelatedKeys($mainKey, $type = 'all') {
        $parsed = self::parseKey($mainKey);
        $relatedKeys = [$mainKey];
        
        // 根据主键类型生成相关键名
        switch ($parsed['prefix']) {
            case self::PREFIX_CATEGORY:
                if ($type === 'all' || $type === 'posts') {
                    // 添加分类相关的帖子缓存键名
                    $catId = isset($parsed['params'][0]) ? $parsed['params'][0] : 0;
                    if ($catId > 0) {
                        $relatedKeys[] = self::categoryKey('posts', ['cat_id' => $catId]);
                        $relatedKeys[] = self::categoryKey('count', ['cat_id' => $catId]);
                    }
                }
                break;
                
            case self::PREFIX_POST:
                if ($type === 'all' || $type === 'category') {
                    // 添加相关分类缓存键名
                    $postId = isset($parsed['params'][0]) ? $parsed['params'][0] : 0;
                    // 这里需要根据实际情况获取帖子的分类ID
                }
                break;
        }
        
        return array_unique($relatedKeys);
    }
}

/**
 * 缓存键名生成辅助函数
 */
function cache_key_category($type, $params = []) {
    return CacheKeyManager::categoryKey($type, $params);
}

function cache_key_region($type, $params = []) {
    return CacheKeyManager::regionKey($type, $params);
}

function cache_key_post($type, $params = []) {
    return CacheKeyManager::postKey($type, $params);
}

function cache_key_list($type, $params = []) {
    return CacheKeyManager::listKey($type, $params);
}

function cache_key_index($type, $params = []) {
    return CacheKeyManager::indexKey($type, $params);
}

function cache_key_news($type, $params = []) {
    return CacheKeyManager::newsKey($type, $params);
}

function cache_key_api($type, $params = []) {
    return CacheKeyManager::apiKey($type, $params);
}

function cache_key_temp($type, $params = []) {
    return CacheKeyManager::tempKey($type, $params);
}
