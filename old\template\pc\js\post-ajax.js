/**
 * 信息发布页面Ajax提交功能
 */
$(document).ready(function() {
    console.log("post-ajax.js 加载成功");
    
    // 监听提交按钮点击事件
    $("#submit-btn").on("click", function(e) {
        e.preventDefault(); // 阻止默认行为
        console.log("提交按钮被点击");
        
        // 获取表单元素
        var form = $("#post-form");
        
        // 手动触发表单验证
        if (!form.valid()) {
            console.log("表单验证失败");
            return false; // 如果验证失败，停止执行
        }
        
        console.log("表单验证通过，准备发送Ajax请求");
        
        // 显示加载遮罩 - 使用css方法设置display为flex
        $("#loading-overlay").css("display", "flex");
        
        // 获取表单数据并添加ajax标记
        var formData = new FormData(form[0]);
        formData.append('ajax', '1'); // 添加Ajax标记
        
        // 记录上传的表单数据
        console.log("表单action URL:", form.attr('action'));
        
        // 发送Ajax请求
        $.ajax({
            url: form.attr('action'),
            type: 'POST',
            data: formData,
            processData: false,
            contentType: false,
            complete: function(xhr, status) {
                console.log("请求完成，状态:", status);
                console.log("响应类型:", xhr.getResponseHeader("Content-Type"));
                console.log("响应文本:", xhr.responseText);
            },
            success: function(response) {
                // 隐藏加载遮罩
                $("#loading-overlay").css("display", "none");
                console.log("请求成功，收到响应:", response);
                
                // 尝试解析响应（如果它还不是对象）
                var responseData = response;
                if (typeof response === 'string') {
                    try {
                        responseData = JSON.parse(response);
                        console.log("解析JSON后的响应:", responseData);
                    } catch (e) {
                        console.error("无法解析响应为JSON:", e);
                        
                        // 如果响应中包含成功信息，视为成功
                        if (response.indexOf('成功') > -1) {
                            $("#success-overlay").css("display", "flex");
                            form[0].reset();
                            $("#image-previews").empty();
                            return;
                        } else {
                            alert("提交失败: 服务器返回非JSON格式数据");
                            return;
                        }
                    }
                }
                
                // 处理JSON响应
                if (responseData && responseData.success) {
                    // 显示成功提示
                    $("#success-overlay").css("display", "flex");
                    // 清空表单
                    form[0].reset();
                    // 清空图片预览
                    $("#image-previews").empty();
                } else {
                    // 显示错误信息
                    var message = responseData && responseData.message ? responseData.message : '提交失败，请稍后再试';
                    alert(message);
                }
            },
            error: function(xhr, status, error) {
                // 隐藏加载遮罩
                $("#loading-overlay").css("display", "none");
                console.error("请求失败:", status, error);
                console.log("状态码:", xhr.status);
                console.log("错误响应:", xhr.responseText);
                
                // 尝试解析错误响应
                var errorMessage = '提交失败，请稍后再试 (错误: ' + error + ')';
                try {
                    if (xhr.responseText) {
                        var response = JSON.parse(xhr.responseText);
                        if (response.message) {
                            errorMessage = response.message;
                        }
                    }
                } catch (e) {
                    console.error('解析错误响应失败:', e);
                    errorMessage += ' (无法解析响应)';
                }
                
                alert(errorMessage);
            }
        });
        
        return false; // 阻止表单默认提交
    });
    
    // 监听表单的原生提交事件，确保它被拦截
    $("#post-form").on("submit", function(e) {
        e.preventDefault();
        console.log("表单提交被拦截");
        $("#submit-btn").click(); // 触发提交按钮的点击事件
        return false;
    });
    
    // 确保成功提示框的关闭按钮正常工作
    $("#success-btn").on("click", function() {
        $("#success-overlay").css("display", "none");
        // 使用纯前端跳转，不产生额外请求
        window.location.href = '/';
    });
});