<?php if(null !== ($posts ?? null) && !empty($posts)): ?>
    <?php if(null !== ($posts ?? null) && is_array($posts)): foreach($posts as $post): ?>
    <?php 
    if (!empty($post['expired_at'])) {
        $days = getRemainingDaysInt($post['expired_at']);
        $guoqi = $days > 0 ? $days.'天' : '已过期';
    } else {
        $guoqi = '长期';
    }
     ?>
    <?php 
        // 检查是否显示置顶标记和选择图标
        $current_time = time();
        $show_top_tag = false;
        $icon_class = 'icon_title1.gif'; // 默认普通图标

        // 检查大分类置顶
        if (isset($post['is_top_category']) && $post['is_top_category'] == 1) {
            if (!isset($post['top_category_expire']) || $post['top_category_expire'] == 0 || $post['top_category_expire'] > $current_time) {
                $show_top_tag = true;
                $icon_class = 'icon_title2.gif'; // 置顶图标
            }
        }

        // 检查小分类置顶
        if (!$show_top_tag && isset($post['is_top_subcategory']) && $post['is_top_subcategory'] == 1) {
            if (!isset($post['top_subcategory_expire']) || $post['top_subcategory_expire'] == 0 || $post['top_subcategory_expire'] > $current_time) {
                $show_top_tag = true;
                $icon_class = 'icon_title2.gif'; // 置顶图标
            }
        }

        // 输出li标签和图标，为置顶信息添加CSS类
        $li_class = $show_top_tag ? ' class="top-item"' : '';
        echo '<li' . $li_class . ' style="background:url(/template/pc/images/' . $icon_class . ') left no-repeat; padding-left: 15px;">';
     ?>
        <div class="info-title">
            <?php if($post['category_name']): ?><a href="/<?php echo (isset($post['category_pinyin'])) ? $post['category_pinyin'] : ""; ?>/" style="color: #999; text-decoration: none; margin-right: 5px;">[<?php echo (isset($post['category_name'])) ? $post['category_name'] : ""; ?>]</a><?php endif; ?>
            <a <?php if($guoqi=='已过期'): ?> style="text-decoration: line-through;"<?php endif; ?> target="_blank" href="/<?php echo (isset($post['category_pinyin'])) ? $post['category_pinyin'] : ""; ?>/<?php echo (isset($post['id'])) ? $post['id'] : ""; ?>.html" <?php if ($show_top_tag) echo 'style="color: #EE3131;"'; ?>><?php echo (isset($post['title'])) ? $post['title'] : ""; ?></a><?php if ($show_top_tag) echo '<span class="top-tag">顶</span>'; ?>
        </div>
        <div class="info-meta">
            <span class="area"><?php echo (isset($post['region_name'])) ? $post['region_name'] : ""; ?></span>
            <span class="validity"><?php echo $guoqi ?? ""; ?></span>
            <span class="time"><?php echo null !== ((null !== ($post ?? null)) ? ($post['updated_at']) : null) ? friendlyTime((null !== ($post ?? null)) ? ($post['updated_at']) : null) : ""; ?></span>
        </div>
    <?php echo '</li>'; ?>
    <?php endforeach; endif; ?>
<?php endif; ?> 