<?php
/**
 * 单页显示页面
 * 支持伪静态URL访问
 */
define('IN_BTMPS', true);
require_once('include/common.inc.php');

// 获取页面路径参数
$path = isset($_GET['path']) ? trim($_GET['path']) : '';

if (empty($path)) {
    header("HTTP/1.0 404 Not Found");
    include('template/pc/404.htm');
    exit;
}

// 从数据库查询页面信息
$sql = "SELECT * FROM pages WHERE path = ? AND status = 1";
$result = $db->query($sql, array($path));

if (!$result || $db->num_rows($result) == 0) {
    header("HTTP/1.0 404 Not Found");
    include('template/pc/404.htm');
    exit;
}

$page = $db->fetch_array($result);



// 获取导航菜单数据
$nav_pages = array();
$nav_sql = "SELECT title, path, url FROM pages WHERE status = 1 ORDER BY sort_order ASC, id ASC";
$nav_result = $db->query($nav_sql);
if ($nav_result && $db->num_rows($nav_result) > 0) {
    while ($nav_page = $db->fetch_array($nav_result)) {
        $nav_page['is_current'] = ($nav_page['path'] == $page['path']);
        $nav_pages[] = $nav_page;
    }
}

// 设置模板变量
$tpl->assign('page', $page);
$tpl->assign('title', $page['title']);
$tpl->assign('content', $page['content']);
$tpl->assign('meta_keywords', $page['meta_keywords']);
$tpl->assign('meta_description', $page['meta_description']);
$tpl->assign('nav_pages', $nav_pages);
$tpl->assign('current_page', 'page');

// 设置SEO信息
$tpl->assign('site_title', $page['title']);
$tpl->assign('site_keywords', $page['meta_keywords'] ?: '单页,信息页');
$tpl->assign('site_description', $page['meta_description'] ?: $page['title']);

// 显示模板（系统会自动根据设备类型选择模板目录）
$tpl->display('page.htm');
?>
