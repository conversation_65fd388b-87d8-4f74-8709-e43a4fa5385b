<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>弹出层提示功能测试</title>
    <link rel="stylesheet" href="css/view.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css">
    <style>
        body { margin: 0; padding: 20px; font-family: Arial, sans-serif; background: #f5f5f5; }
        .test-container { max-width: 800px; margin: 0 auto; background: #fff; padding: 30px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .test-title { text-align: center; margin-bottom: 30px; color: #333; }
        .test-description { background: #f8f9fa; padding: 20px; border-radius: 5px; margin-bottom: 30px; border-left: 4px solid #2e8ded; }
        .test-buttons { display: flex; flex-wrap: wrap; gap: 15px; justify-content: center; margin-bottom: 30px; }
        .test-btn { background: #2e8ded; color: #fff; border: none; padding: 12px 24px; border-radius: 5px; cursor: pointer; font-size: 14px; transition: background-color 0.3s; }
        .test-btn:hover { background: #1e7bd7; }
        .test-btn.error { background: #ff4757; }
        .test-btn.error:hover { background: #ff3742; }
        .test-btn.success { background: #2ed573; }
        .test-btn.success:hover { background: #26d065; }
        .test-section { margin-bottom: 30px; }
        .test-section h3 { color: #2e8ded; margin-bottom: 15px; }
        .code-example { background: #f8f9fa; padding: 15px; border-radius: 5px; font-family: 'Courier New', monospace; font-size: 13px; border: 1px solid #e9ecef; }
    </style>
</head>
<body>
    <div class="test-container">
        <h1 class="test-title">PC端弹出层提示功能测试</h1>
        
        <div class="test-description">
            <h3>功能说明</h3>
            <p><strong>问题：</strong>原来的错误提示会跳转到另外一个页面显示，用户体验不佳。</p>
            <p><strong>解决方案：</strong>使用弹出层在当前页面显示提示信息，包括加载状态、错误提示和成功提示。</p>
            <p><strong>应用场景：</strong>主要用于刷新操作的频率限制提示，如"操作过于频繁，距离上次刷新不足1小时，请等待40分钟后再试"。</p>
        </div>

        <div class="test-section">
            <h3>弹出层测试</h3>
            <div class="test-buttons">
                <button class="test-btn" onclick="testLoadingModal()">测试加载提示</button>
                <button class="test-btn error" onclick="testErrorModal()">测试错误提示</button>
                <button class="test-btn success" onclick="testSuccessModal()">测试成功提示</button>
                <button class="test-btn" onclick="testRefreshError()">模拟刷新频率限制</button>
            </div>
        </div>

        <div class="test-section">
            <h3>使用方法</h3>
            <div class="code-example">
// 显示加载提示
showLoadingModal('正在刷新信息...');

// 显示错误提示
showErrorModal('操作过于频繁，距离上次刷新不足1小时，请等待40分钟后再试');

// 显示成功提示（带回调）
showSuccessModal('信息刷新成功', () => {
    window.location.reload();
});

// 隐藏弹出层
hideLoadingModal();
hideErrorModal();
hideSuccessModal();
            </div>
        </div>

        <div class="test-section">
            <h3>AJAX请求示例</h3>
            <div class="code-example">
fetch('/manage.php?id=123&action=refresh&ajax=1', {
    method: 'GET',
    headers: {
        'X-Requested-With': 'XMLHttpRequest'
    }
})
.then(response => response.json())
.then(data => {
    if (data.success) {
        showSuccessModal(data.message);
    } else {
        showErrorModal(data.message);
    }
});
            </div>
        </div>
    </div>

    <!-- 操作提示弹出层 -->
    <!-- 加载提示层 -->
    <div id="loading-modal" class="modal-overlay" style="display: none;">
        <div class="modal-content loading-modal">
            <div class="loading-spinner"></div>
            <div class="loading-text">正在处理...</div>
        </div>
    </div>

    <!-- 错误提示层 -->
    <div id="error-modal" class="modal-overlay" style="display: none;">
        <div class="modal-content error-modal">
            <div class="modal-icon error-icon">
                <i class="fas fa-times-circle"></i>
            </div>
            <div class="modal-title">操作失败</div>
            <div class="modal-message" id="error-message"></div>
            <div class="modal-buttons">
                <button class="modal-btn primary" onclick="hideErrorModal()">确定</button>
            </div>
        </div>
    </div>

    <!-- 成功提示层 -->
    <div id="success-modal" class="modal-overlay" style="display: none;">
        <div class="modal-content success-modal">
            <div class="modal-icon success-icon">
                <i class="fas fa-check-circle"></i>
            </div>
            <div class="modal-title">操作成功</div>
            <div class="modal-message" id="success-message"></div>
            <div class="modal-buttons">
                <button class="modal-btn primary" onclick="hideSuccessModal()">确定</button>
            </div>
        </div>
    </div>

    <script>
        // 弹出层控制函数
        function showLoadingModal(message = '正在处理...') {
            document.getElementById('loading-modal').style.display = 'flex';
            document.querySelector('.loading-text').textContent = message;
        }
        
        function hideLoadingModal() {
            document.getElementById('loading-modal').style.display = 'none';
        }
        
        function showErrorModal(message) {
            document.getElementById('error-message').textContent = message;
            document.getElementById('error-modal').style.display = 'flex';
        }
        
        function hideErrorModal() {
            document.getElementById('error-modal').style.display = 'none';
        }
        
        function showSuccessModal(message, callback = null) {
            document.getElementById('success-message').textContent = message;
            document.getElementById('success-modal').style.display = 'flex';
            
            // 如果有回调函数，保存起来
            if (callback) {
                window.successModalCallback = callback;
            }
        }
        
        function hideSuccessModal() {
            document.getElementById('success-modal').style.display = 'none';
            
            // 执行回调函数
            if (window.successModalCallback) {
                window.successModalCallback();
                window.successModalCallback = null;
            }
        }
        
        // 点击遮罩层关闭弹出层
        document.addEventListener('click', function(e) {
            if (e.target.classList.contains('modal-overlay')) {
                if (e.target.id === 'error-modal') {
                    hideErrorModal();
                } else if (e.target.id === 'success-modal') {
                    hideSuccessModal();
                }
            }
        });

        // 测试函数
        function testLoadingModal() {
            showLoadingModal('正在刷新信息...');
            setTimeout(() => {
                hideLoadingModal();
            }, 3000);
        }

        function testErrorModal() {
            showErrorModal('操作过于频繁，距离上次刷新不足1小时，请等待40分钟后再试');
        }

        function testSuccessModal() {
            showSuccessModal('信息刷新成功', () => {
                alert('回调函数执行了！');
            });
        }

        function testRefreshError() {
            showLoadingModal('正在刷新信息...');
            setTimeout(() => {
                hideLoadingModal();
                showErrorModal('操作过于频繁，距离上次刷新不足1小时，请等待40分钟后再试');
            }, 1500);
        }
    </script>
</body>
</html>
