<?php
define('IN_BTMPS', true);

// 开始计时
$__startTime = microtime(true);

// 设置错误报告级别
error_reporting(E_ALL);
ini_set('display_errors', 'On');

// 设置时区
date_default_timezone_set('Asia/Shanghai');

// 定义常量
define('ROOT_PATH', str_replace('\\', '/', dirname(dirname(__FILE__))).'/');
define('INCLUDE_PATH', ROOT_PATH.'include/');
define('TEMPLATE_PATH', ROOT_PATH.'template/');
define('DATA_PATH', ROOT_PATH.'data/');
define('UPLOAD_PATH', ROOT_PATH.'uploads/');
define('COMPILED_PATH', DATA_PATH.'compiled/');

// 确保编译目录存在
if (!is_dir(COMPILED_PATH)) {
    mkdir(COMPILED_PATH, 0777, true);
}

// 启动会话
session_start();

// 设置默认字符集
header('Content-Type: text/html; charset=utf-8');

// 加载数据库配置
require_once(ROOT_PATH.'config/config.db.php');

// 加载系统配置
require_once(ROOT_PATH.'config/config.inc.php');

// 加载模板引擎类
require_once(INCLUDE_PATH.'template.class.php');

// 初始化模板引擎
$tpl = new Template();

// 加载数据库类
require_once(INCLUDE_PATH.'mysql.class.php');

// 初始化数据库连接
$db = new MySQL($config);

// 加载必要的函数库（避免重复定义常量）
if (!function_exists('cache_set')) {
    require_once(INCLUDE_PATH.'cache.class.php');
}
if (!function_exists('getCachedCategories')) {
    require_once(INCLUDE_PATH.'global.fun.php');
}
if (!function_exists('getTopPosts')) {
    require_once(INCLUDE_PATH.'info.fun.php');
}

// 初始化全局变量（info.fun.php中的函数需要这些变量）
if (!isset($GLOBALS['cached_categories'])) {
    $GLOBALS['cached_categories'] = getCachedCategories();
}
if (!isset($GLOBALS['cached_regions'])) {
    $GLOBALS['cached_regions'] = getCachedRegions();
}

// 检查管理员权限
if (!isset($_SESSION['admin']) || $_SESSION['admin']['is_login'] !== true) {
    header('Location: login.php');
    exit;
}

// 处理AJAX清理缓存请求
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action']) && $_POST['action'] === 'clear_cache') {
    $type = isset($_POST['type']) ? $_POST['type'] : 'all';

    try {
        switch ($type) {
            case 'all':
                $count = clearAllCache();
                $response = array('success' => true, 'message' => "成功清理了所有缓存，共 {$count} 个文件");
                break;
            case 'news':
                $count = news_cache_clear_all();
                $response = array('success' => true, 'message' => "成功清理了新闻缓存，共 {$count} 个文件");
                break;
            case 'news_home':
                $count = news_cache_clear_home();
                $response = array('success' => true, 'message' => "成功清理了新闻首页缓存，共 {$count} 个文件");
                break;
            case 'news_list':
                $count = news_cache_clear_list();
                $response = array('success' => true, 'message' => "成功清理了新闻列表缓存，共 {$count} 个文件");
                break;
            case 'news_detail':
                $count = news_cache_clear_detail();
                $response = array('success' => true, 'message' => "成功清理了新闻详情缓存，共 {$count} 个文件");
                break;
            default:
                $count = clearAllCache();
                $response = array('success' => true, 'message' => "成功清理了所有缓存，共 {$count} 个文件");
                break;
        }
    } catch (Exception $e) {
        $response = array('success' => false, 'message' => '清理缓存失败：' . $e->getMessage());
    }

    header('Content-Type: application/json');
    echo json_encode($response);
    exit;
}

// 设置当前页面标识
$current_page = 'cache_manager';

$action = isset($_GET['action']) ? $_GET['action'] : 'view';
$message = '';

switch ($action) {
    case 'clear_all':
        $count = clearAllCache();
        $message = "成功清理了所有缓存，共 {$count} 个文件";
        break;



    case 'clear_posts':
        // 清理帖子相关缓存
        $cacheDir = $GLOBALS['file_cache']->getCacheDir();
        $files = glob($cacheDir . '*.cache');
        $count = 0;

        foreach ($files as $file) {
            $filename = basename($file);
            if (strpos($filename, md5('category_posts_')) === 0) {
                if (@unlink($file)) {
                    $count++;
                }
            }
        }

        $message = "成功清理了 {$count} 个帖子缓存文件";
        break;



    case 'precompile_templates':
        // 预编译所有模板
        $result = precompileAllTemplates();
        $message = $result['message'];
        break;

    case 'clear_template_cache':
        // 清理模板缓存
        $result = clearTemplateCache();
        $message = $result['message'];
        break;

    case 'clean_expired_templates':
        // 清理过期模板缓存
        $result = cleanExpiredTemplateCache();
        $message = $result['message'];
        break;

    case 'clear_detail_cache':
        // 清理详情缓存
        $result = clearDetailCache();
        $message = $result['message'];
        break;

    case 'clear_search_cache':
        // 清理搜索缓存
        $result = clearSearchCache();
        $message = $result['message'];
        break;

    case 'clear_index_cache':
        // 清理首页缓存
        $result = clearIndexCache();
        $message = $result['message'];
        break;

    case 'clear_list_cache':
        // 清理列表缓存
        $result = clearListCache();
        $message = $result['message'];
        break;

    case 'warmup_cache':
        // 一键预热缓存
        $result = warmupCache();
        $message = $result['message'];
        break;

    case 'view_stats':
        // 显示详细统计信息
        break;

    default:
        $message = '';
        break;
}

// 获取缓存信息
$cacheInfo = $GLOBALS['file_cache']->getInfo();

// 安全地检查缓存状态，不自动创建缓存
function checkCacheFileExists($key) {
    return $GLOBALS['file_cache']->exists($key);
}

// 获取模板缓存统计信息
$templateCacheStats = getTemplateCacheStats();

// 获取分类缓存统计信息
$cacheTypeStats = getCacheTypeStats();

$cacheStats = [
    'total_files' => $cacheInfo['count'],
    'total_size' => $cacheInfo['total_size'],
    'total_size_formatted' => $cacheInfo['total_size_formatted'],
    'template_cache' => $templateCacheStats,
    'cache_types' => $cacheTypeStats,
];

// 获取管理员信息
$admin = $_SESSION['admin'];

// 设置模板目录
$tpl->setTemplateDir(dirname(__FILE__) . '/template/');

// 分配模板变量
$tpl->assign('page_title', '缓存管理');
$tpl->assign('breadcrumb', '缓存管理');
$tpl->assign('current_page', $current_page);
$tpl->assign('admin', $admin);
$tpl->assign('message', $message);
$tpl->assign('cacheInfo', $cacheInfo);
$tpl->assign('cacheStats', $cacheStats);

// 显示模板
$tpl->display('cache_manager.htm');

/**
 * 预编译所有模板
 */
function precompileAllTemplates() {
    try {
        $template_dirs = array(
            'PC模板' => TEMPLATE_PATH . 'pc/',
            '手机模板' => TEMPLATE_PATH . 'm/',
        );

        $total_compiled = 0;
        $total_errors = 0;
        $error_messages = array();

        foreach ($template_dirs as $name => $dir) {
            if (!is_dir($dir)) {
                continue;
            }

            $temp_tpl = new Template();
            $temp_tpl->setTemplateDir($dir);
            $result = $temp_tpl->precompileTemplates($dir);

            $total_compiled += $result['count'];
            $total_errors += count($result['errors']);

            if (!empty($result['errors'])) {
                $error_messages = array_merge($error_messages, $result['errors']);
            }
        }

        if ($total_errors == 0) {
            return array('message' => "预编译完成！成功编译 {$total_compiled} 个模板文件。");
        } else {
            $error_text = implode('<br>', array_slice($error_messages, 0, 5));
            if (count($error_messages) > 5) {
                $error_text .= '<br>...还有 ' . (count($error_messages) - 5) . ' 个错误';
            }
            return array('message' => "预编译完成！成功编译 {$total_compiled} 个文件，{$total_errors} 个错误：<br>{$error_text}");
        }
    } catch (Exception $e) {
        return array('message' => '预编译失败：' . $e->getMessage());
    }
}

/**
 * 清理模板缓存
 */
function clearTemplateCache() {
    try {
        $cache_dir = DATA_PATH . 'compiled/';
        $count = 0;

        if (is_dir($cache_dir)) {
            $files = glob($cache_dir . '*.php');
            foreach ($files as $file) {
                if (@unlink($file)) {
                    $count++;
                }
            }
        }

        return array('message' => "缓存清理完成！删除了 {$count} 个模板缓存文件。");
    } catch (Exception $e) {
        return array('message' => '缓存清理失败：' . $e->getMessage());
    }
}

/**
 * 清理过期模板缓存
 */
function cleanExpiredTemplateCache() {
    try {
        $temp_tpl = new Template();
        $count = $temp_tpl->cleanExpiredCache();

        return array('message' => "过期缓存清理完成！删除了 {$count} 个过期文件。");
    } catch (Exception $e) {
        return array('message' => '过期缓存清理失败：' . $e->getMessage());
    }
}

/**
 * 清理详情缓存
 */
function clearDetailCache() {
    try {
        $cache_dir = DATA_PATH . 'cache/';
        $count = 0;

        if (is_dir($cache_dir)) {
            // 查找所有以 detail_ 开头的缓存文件（包括PC版、手机版等）
            $files = glob($cache_dir . 'detail_*.cache');
            foreach ($files as $file) {
                if (@unlink($file)) {
                    $count++;
                }
            }
        }

        return array('message' => "详情缓存清理完成！删除了 {$count} 个缓存文件（包括PC版、手机版等）。");
    } catch (Exception $e) {
        return array('message' => '详情缓存清理失败：' . $e->getMessage());
    }
}

/**
 * 清理搜索缓存
 */
function clearSearchCache() {
    try {
        $cache_dir = DATA_PATH . 'cache/';
        $count = 0;

        if (is_dir($cache_dir)) {
            // 查找所有以 search_ 开头的缓存文件（包括PC版、手机版等）
            $files = glob($cache_dir . 'search_*.cache');
            foreach ($files as $file) {
                if (@unlink($file)) {
                    $count++;
                }
            }
        }

        return array('message' => "搜索缓存清理完成！删除了 {$count} 个缓存文件（包括PC版、手机版等）。");
    } catch (Exception $e) {
        return array('message' => '搜索缓存清理失败：' . $e->getMessage());
    }
}

/**
 * 获取模板缓存统计信息
 */
function getTemplateCacheStats() {
    $cache_dir = DATA_PATH . 'compiled/';
    $stats = array(
        'total_files' => 0,
        'total_size' => 0,
        'total_size_formatted' => '0 B',
        'oldest_time' => 0,
        'newest_time' => 0,
        'cache_dir' => $cache_dir
    );

    if (!is_dir($cache_dir)) {
        return $stats;
    }

    $files = glob($cache_dir . '*.php');
    $stats['total_files'] = count($files);

    if ($stats['total_files'] > 0) {
        $oldest_time = time();
        $newest_time = 0;

        foreach ($files as $file) {
            $size = filesize($file);
            $time = filemtime($file);

            $stats['total_size'] += $size;
            $oldest_time = min($oldest_time, $time);
            $newest_time = max($newest_time, $time);
        }

        $stats['oldest_time'] = $oldest_time;
        $stats['newest_time'] = $newest_time;
        $stats['total_size_formatted'] = formatBytes($stats['total_size']);
    }

    return $stats;
}

/**
 * 获取不同类型缓存的统计信息
 */
function getCacheTypeStats() {
    $cache_dir = DATA_PATH . 'cache/';
    $stats = array(
        'category' => array('count' => 0, 'size' => 0),
        'region' => array('count' => 0, 'size' => 0),
        'detail' => array('count' => 0, 'size' => 0),
        'list' => array('count' => 0, 'size' => 0),
        'search' => array('count' => 0, 'size' => 0),
        'index' => array('count' => 0, 'size' => 0),
        'user' => array('count' => 0, 'size' => 0),
        'stats' => array('count' => 0, 'size' => 0),
        'misc' => array('count' => 0, 'size' => 0),
    );

    if (!is_dir($cache_dir)) {
        return $stats;
    }

    $files = glob($cache_dir . '*.cache');

    foreach ($files as $file) {
        $filename = basename($file, '.cache');
        $size = filesize($file);

        // 根据缓存文件名的实际格式进行分类
        // 搜索缓存：search_pc_xxx, search_m_xxx 等
        if (strpos($filename, 'search_') === 0) {
            $stats['search']['count']++;
            $stats['search']['size'] += $size;
        }
        // 首页缓存：index_page_pc, index_page_m 等
        elseif (strpos($filename, 'index_page_') === 0) {
            $stats['index']['count']++;
            $stats['index']['size'] += $size;
        }
        // 详情缓存：detail_xxx_pc, detail_xxx_m 等
        elseif (strpos($filename, 'detail_') === 0) {
            $stats['detail']['count']++;
            $stats['detail']['size'] += $size;
        }
        // 统计缓存：category_posts_count_xxx 等
        elseif (strpos($filename, 'category_posts_count_') === 0) {
            $stats['stats']['count']++;
            $stats['stats']['size'] += $size;
        }
        // 列表缓存：category_list_xxx, category_posts_v2_xxx 等
        elseif (strpos($filename, 'category_list_') === 0 || strpos($filename, 'category_posts_v2_') === 0) {
            $stats['list']['count']++;
            $stats['list']['size'] += $size;
        }
        // 分类缓存：category_all 等基础分类缓存
        elseif (strpos($filename, 'category_all') === 0) {
            $stats['category']['count']++;
            $stats['category']['size'] += $size;
        }
        // 区域缓存：region_all 等
        elseif (strpos($filename, 'region_') === 0) {
            $stats['region']['count']++;
            $stats['region']['size'] += $size;
        }
        // 用户缓存：user_xxx 等
        elseif (strpos($filename, 'user_') === 0) {
            $stats['user']['count']++;
            $stats['user']['size'] += $size;
        }
        // 其他缓存
        else {
            $stats['misc']['count']++;
            $stats['misc']['size'] += $size;
        }
    }

    // 格式化大小
    foreach ($stats as $type => &$data) {
        $data['size_formatted'] = formatBytes($data['size']);
    }

    return $stats;
}

/**
 * 清理首页缓存
 */
function clearIndexCache() {
    try {
        $cache_dir = DATA_PATH . 'cache/';
        $count = 0;

        if (is_dir($cache_dir)) {
            // 查找所有以 index_page_ 开头的缓存文件
            $files = glob($cache_dir . 'index_page_*.cache');
            foreach ($files as $file) {
                if (@unlink($file)) {
                    $count++;
                }
            }
        }

        return array('message' => "首页缓存清理完成！删除了 {$count} 个缓存文件。");
    } catch (Exception $e) {
        return array('message' => '首页缓存清理失败：' . $e->getMessage());
    }
}

/**
 * 清理列表缓存
 */
function clearListCache() {
    try {
        $cache_dir = DATA_PATH . 'cache/';
        $count = 0;

        if (is_dir($cache_dir)) {
            // 查找所有列表相关的缓存文件
            $patterns = array(
                'category_list_*.cache',
                'category_posts_v2_*.cache'
            );

            foreach ($patterns as $pattern) {
                $files = glob($cache_dir . $pattern);
                foreach ($files as $file) {
                    if (@unlink($file)) {
                        $count++;
                    }
                }
            }
        }

        return array('message' => "列表缓存清理完成！删除了 {$count} 个缓存文件。");
    } catch (Exception $e) {
        return array('message' => '列表缓存清理失败：' . $e->getMessage());
    }
}

/**
 * 一键预热缓存
 */
function warmupCache() {
    try {
        $warmed_count = 0;
        $error_count = 0;
        $errors = array();

        // 1. 预热首页缓存
        try {
            $result = warmupHomePage();
            $warmed_count += $result['success'];
            if ($result['error']) {
                $errors[] = $result['error'];
                $error_count++;
            }
        } catch (Exception $e) {
            $errors[] = "首页缓存预热失败：" . $e->getMessage();
            $error_count++;
        }

        // 2. 预热栏目列表页缓存
        try {
            $result = warmupCategoryPages();
            $warmed_count += $result['success'];
            if ($result['error']) {
                $errors[] = $result['error'];
                $error_count++;
            }
        } catch (Exception $e) {
            $errors[] = "栏目列表页缓存预热失败：" . $e->getMessage();
            $error_count++;
        }

        // 3. 预热热门详情页缓存
        try {
            $result = warmupPopularDetailPages();
            $warmed_count += $result['success'];
            if ($result['error']) {
                $errors[] = $result['error'];
                $error_count++;
            }
        } catch (Exception $e) {
            $errors[] = "热门详情页缓存预热失败：" . $e->getMessage();
            $error_count++;
        }

        // 4. 清理过期缓存
        try {
            $result = cleanExpiredCache();
            $warmed_count += $result['cleaned'];
            if ($result['error']) {
                $errors[] = $result['error'];
                $error_count++;
            }
        } catch (Exception $e) {
            $errors[] = "清理过期缓存失败：" . $e->getMessage();
            $error_count++;
        }

        // 5. 预热模板缓存
        try {
            $template_result = precompileAllTemplates();
            if (strpos($template_result['message'], '成功') !== false) {
                $warmed_count += 3; // 模板预编译算作3个单位
            }
        } catch (Exception $e) {
            $errors[] = "模板缓存预热失败：" . $e->getMessage();
            $error_count++;
        }

        // 生成结果消息
        if ($error_count == 0) {
            return array('message' => "缓存预热完成！成功预热了 {$warmed_count} 个缓存项目，网站访问速度已优化。");
        } else {
            $error_text = implode('<br>', array_slice($errors, 0, 3));
            if (count($errors) > 3) {
                $error_text .= '<br>...还有 ' . (count($errors) - 3) . ' 个错误';
            }
            return array('message' => "缓存预热完成！成功预热了 {$warmed_count} 个项目，{$error_count} 个错误：<br>{$error_text}");
        }

    } catch (Exception $e) {
        return array('message' => '缓存预热失败：' . $e->getMessage());
    }
}

/**
 * 获取可用的模板目录
 */
function getAvailableTemplates() {
    $template_base_dir = dirname(__FILE__) . '/../template/';
    $available_templates = array();

    // 检查各个模板目录是否存在
    $possible_templates = array('pc', 'm', 'wx', 'app');

    foreach ($possible_templates as $template) {
        $template_dir = $template_base_dir . $template;
        if (is_dir($template_dir)) {
            $available_templates[] = $template;
        }
    }

    // 如果没有找到任何模板目录，至少返回pc作为默认
    if (empty($available_templates)) {
        $available_templates[] = 'pc';
    }

    return $available_templates;
}

/**
 * 预热首页缓存
 */
function warmupHomePage() {
    global $config;

    try {
        $success_count = 0;

        // 获取实际存在的模板目录
        $templates = getAvailableTemplates();

        foreach ($templates as $template) {
            $cache_key = "index_page_{$template}";

            // 删除现有缓存，强制重新生成
            cache_delete($cache_key);

            // 模拟首页数据获取
            $indexSize = isset($config['index_size']) ? intval($config['index_size']) : 10;
            $cache_index_time = isset($config['cache_index']) ? intval($config['cache_index']) : 3600;

            // 获取首页数据
            $categories = isset($GLOBALS['cached_categories']) ? $GLOBALS['cached_categories'] : getCachedCategories();
            $topPosts = function_exists('getTopPosts') ? getTopPosts(10) : array();
            $normalPosts = function_exists('getNormalPosts') ? getNormalPosts($indexSize) : array();

            // 缓存数据
            $cache_data = array(
                'categories' => $categories,
                'topPosts' => $topPosts,
                'normalPosts' => $normalPosts,
                'generated_at' => time()
            );

            $result = cache_set($cache_key, $cache_data, $cache_index_time);
            if ($result) {
                $success_count++;
            }
        }

        return array('success' => $success_count, 'error' => null);

    } catch (Exception $e) {
        return array('success' => 0, 'error' => "首页缓存预热失败: " . $e->getMessage());
    }
}

/**
 * 预热栏目列表页缓存
 */
function warmupCategoryPages() {
    global $config;

    try {
        $success_count = 0;

        $categories = isset($GLOBALS['cached_categories']) ? $GLOBALS['cached_categories'] : getCachedCategories();
        $templates = getAvailableTemplates();
        $cache_list_time = isset($config['cache_list']) ? intval($config['cache_list']) : 1800;
        $perPage = isset($config['list_page_size']) ? intval($config['list_page_size']) : 20;

        // 预热所有栏目（一级和二级）的第一页
        foreach ($categories as $category) {
            foreach ($templates as $template) {
                $cache_key = "category_list_{$template}_{$category['id']}_1_{$perPage}_0";

                // 删除现有缓存
                cache_delete($cache_key);

                // 获取栏目数据（这会触发额外的缓存生成）
                $posts = getCategoryPostsDirectly($category['id'], 1, $perPage, 0, 0, false);
                $totalPosts = getCachedCategoryPostsCount($category['id'], 0, 0, false);

                // 缓存数据
                $cache_data = array(
                    'posts' => $posts,
                    'totalPosts' => $totalPosts,
                    'generated_at' => time()
                );

                $result = cache_set($cache_key, $cache_data, $cache_list_time);
                if ($result) {
                    $success_count++;
                }
            }
        }

        return array('success' => $success_count, 'error' => null);

    } catch (Exception $e) {
        return array('success' => 0, 'error' => "栏目列表页缓存预热失败: " . $e->getMessage());
    }
}

/**
 * 预热热门详情页缓存
 */
function warmupPopularDetailPages() {
    global $config, $db;

    try {
        $success_count = 0;
        $templates = getAvailableTemplates();
        $cache_detail_time = isset($config['cache_detail']) ? intval($config['cache_detail']) : 7200;

        // 获取最近7天的热门信息（按浏览量排序）
        $sql = "SELECT id, category_id FROM posts WHERE status = 1 AND expired_at > ? AND created_at > ? ORDER BY view_count DESC LIMIT 20";
        $recent_time = time() - (7 * 24 * 3600); // 7天前
        $popular_posts = $db->fetchAll($sql, array(time(), $recent_time));

        foreach ($popular_posts as $post) {
            foreach ($templates as $template) {
                $cache_key = "detail_{$post['id']}_{$template}";

                // 删除现有缓存，强制重新生成
                cache_delete($cache_key);

                // 获取详情数据（这会触发额外的缓存生成）
                $detail = getPostDetail($post['id']);
                if ($detail) {
                    $images = getCachedPostImages($post['id']);
                    $relatedPosts = getCachedRelatedPosts($post['category_id'], $post['id'], 5);
                    $categoryInfo = getCategoryInfo($post['category_id']);

                    // 缓存数据（不包含浏览次数，因为需要实时显示）
                    $cache_data = array(
                        'post' => $detail,
                        'images' => $images,
                        'related_posts' => $relatedPosts,
                        'category_info' => $categoryInfo,
                        'cached_at' => time()
                    );

                    $result = cache_set($cache_key, $cache_data, $cache_detail_time);
                    if ($result) {
                        $success_count++;
                    }
                }
            }
        }

        return array('success' => $success_count, 'error' => null);

    } catch (Exception $e) {
        return array('success' => 0, 'error' => "热门详情页缓存预热失败: " . $e->getMessage());
    }
}

/**
 * 清理过期缓存
 */
function cleanExpiredCache() {
    try {
        $cache_dir = DATA_PATH . 'cache/';
        $cleaned = 0;
        $expire_time = time() - (24 * 3600); // 24小时前

        if (is_dir($cache_dir)) {
            $files = glob($cache_dir . '*.cache');
            foreach ($files as $file) {
                $filename = basename($file);

                // 跳过基础数据缓存（地区、栏目等）
                if (strpos($filename, 'region_') === 0 ||
                    strpos($filename, 'category_all') === 0) {
                    continue;
                }

                // 清理过期文件
                if (filemtime($file) < $expire_time) {
                    if (@unlink($file)) {
                        $cleaned++;
                    }
                }
            }
        }

        return array('cleaned' => $cleaned, 'error' => null);

    } catch (Exception $e) {
        return array('cleaned' => 0, 'error' => "清理过期缓存失败: " . $e->getMessage());
    }
}

/**
 * 格式化字节数
 */
function formatBytes($bytes) {
    $units = array('B', 'KB', 'MB', 'GB');
    $bytes = max($bytes, 0);
    $pow = floor(($bytes ? log($bytes) : 0) / log(1024));
    $pow = min($pow, count($units) - 1);

    $bytes /= pow(1024, $pow);

    return round($bytes, 2) . ' ' . $units[$pow];
}
