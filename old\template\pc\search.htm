<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title><?php 
        if (empty($keyword)) {
            echo '信息搜索';
        } else {
            echo '搜索：' . $keyword;
        }
        echo ' - ' . $site_name; 
    ?></title>
    <meta name="keywords" content="<?php echo $site_keywords; ?>">
    <meta name="description" content="<?php echo $site_description; ?>">
    <link rel="stylesheet" href="/template/pc/css/common.css">
    <link rel="stylesheet" href="/template/pc/css/category.css">
    <link rel="stylesheet" href="/template/pc/css/filter.css">
    <script type="text/javascript" src="/template/pc/js/m.js"></script>
    <style>
        /* 搜索加载状态样式 */
        .search-form {
            position: relative;
        }

        .search-btn {
            position: relative;
            min-width: 80px;
            transition: all 0.3s ease;
        }

        .search-btn:disabled {
            background: #6c757d !important;
            border-color: #6c757d !important;
            cursor: not-allowed;
        }

        .search-btn .btn-loading {
            display: none;
        }

        .search-btn.loading .btn-text {
            display: none;
        }

        .search-btn.loading .btn-loading {
            display: inline-block;
        }

        /* 搜索加载遮罩 */
        .search-loading-overlay {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(255, 255, 255, 0.9);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 1000;
            border-radius: 8px;
        }

        .loading-content {
            text-align: center;
            color: #007bff;
            font-size: 16px;
        }

        .loading-content i {
            font-size: 20px;
            margin-right: 8px;
        }

        /* 搜索结果区域加载状态 */
        .search-results-loading {
            text-align: center;
            padding: 40px 20px;
            color: #007bff;
            font-size: 16px;
        }

        .search-results-loading i {
            font-size: 24px;
            margin-bottom: 10px;
            display: block;
        }

        /* 搜索结果信息样式 */
        .search-result-info {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 4px;
            margin-bottom: 15px;
        }

        .result-summary {
            color: #495057;
            font-size: 14px;
            line-height: 1.5;
        }

        .result-summary i {
            color: #007bff;
            margin-right: 5px;
        }

        .result-summary strong {
            color: #007bff;
        }

        /* 搜索页面使用与信息列表相同的分页样式 */
        .pagination {
            margin: 30px 0;
            display: flex;
            justify-content: center;
            align-items: center;
            gap: 4px;
        }

        .pagination a, .pagination span {
            display: inline-flex;
            align-items: center;
            justify-content: center;
            min-width: 26px;
            height: 26px;
            font-size: 13px;
            border: 1px solid #e0e0e0;
            background-color: #fff;
            color: #333;
            text-decoration: none;
            border-radius: 3px;
            transition: all 0.3s ease;
        }

        .pagination a:hover {
            background-color: #f5f5f5;
            border-color: #d0d0d0;
            color: #c4284d;
        }

        .pagination .current {
            background-color: #c4284d;
            color: #fff;
            border-color: #c4284d;
            font-weight: 500;
        }

        .pagination .disabled {
            color: #999;
            cursor: not-allowed;
            background-color: #f5f5f5;
            border-color: #e0e0e0;
        }

        .pagination .nav-btn {
            padding: 0 12px;
            min-width: 60px;
        }
    </style>
</head>
<body>
    <!-- 顶部 -->
    {include file="header.htm"}
    
    <div class="yui-clear"></div>

    <!-- 主内容区域 -->
    <div class="yui-1200">
        <!-- 面包屑导航 -->
        <div class="breadcrumb-container">
            <div class="breadcrumb">
                <a href="/">首页</a>
                <span class="separator">></span>
                <span>信息搜索</span>
                <?php if (!empty($keyword)): ?>
                <span class="separator">></span>
                <span><?php echo $keyword; ?></span>
                <?php endif; ?>
            </div>
        </div>
    </div>

    <div class="yui-content yui-1200">
        <div class="content-wrap">
            <!-- 左侧搜索区域 -->
            <div class="yui-index-info-list">
                <div class="yui-index-info-list-left">
                    <div class="yui-list-tabs">
                        <div class="tab-header">
                            <ul>
                                <li class="active">搜索结果</li>
                            </ul>
                            <?php if (!empty($keyword)): ?>
                            <span class="index-list-post yui-right" style="color: #666; font-size: 14px;">
                                <i class="fas fa-search" style="margin-right: 5px;"></i>
                                搜索关键词：<strong style="color: #c4284d;"><?php echo htmlspecialchars($keyword); ?></strong>
                                <?php if (!empty($search_results)): ?>
                                    ，共找到 <strong style="color: #c4284d;"><?php echo $total_count; ?></strong> 条相关信息
                                <?php endif; ?>
                            </span>
                            <?php endif; ?>
                        </div>

                        <!-- 搜索错误提示 -->
                        <?php if (!empty($search_error)): ?>
                        <div class="search-error-message pd10" style="background: #fff3cd; border: 1px solid #ffeaa7; color: #856404; padding: 10px; margin-bottom: 10px; border-radius: 4px;">
                            <i class="fas fa-exclamation-triangle" style="margin-right: 5px;"></i>
                            <span id="search-error-text"><?php echo $search_error; ?></span>
                            <span id="countdown-timer" style="font-weight: bold;"></span>
                        </div>
                        <?php endif; ?>

                        <!-- 无搜索关键词时显示搜索表单 -->
                        <?php if (empty($keyword)): ?>
                        <div class="search-form pd10">
                            <form action="/search.php" method="get" id="search-form">
                                <div class="search-box">
                                    <input type="text" name="keyword" class="search-input" placeholder="请输入要搜索的关键词" required id="search-input">
                                    <button type="submit" class="search-btn" id="search-btn">
                                        <span class="btn-text">搜索</span>
                                        <span class="btn-loading" style="display: none;">
                                            <i class="fas fa-spinner fa-spin"></i> 搜索中...
                                        </span>
                                    </button>
                                </div>
                                <!-- 搜索加载提示 -->
                                <div class="search-loading-overlay" id="search-loading" style="display: none;">
                                    <div class="loading-content">
                                        <i class="fas fa-spinner fa-spin"></i>
                                        <span>正在搜索，请稍候...</span>
                                    </div>
                                </div>
                            </form>

                            <!-- 搜索须知 -->
                            <div class="search-notice">
                                <div class="notice-title">搜索信息须知：</div>
                                <div class="notice-content">
                                    <p>1. 本搜索为简易搜索，搜索范围为信息标题和信息内容，不支持分词联想，请输入单个精确的关键词进行检索</p>
                                    <p>2. 搜索结果较为粗略，如要查找更多有用信息，请切换到<a href="/">网站首页</a>和相应栏目浏览</p>
                                </div>
                            </div>
                        </div>
                        <?php endif; ?>

                        <!-- 搜索结果 -->
                        <?php if (!empty($keyword)): ?>
                            <?php if (!empty($search_results)): ?>
                                <div class="tab-content">
                                    <div class="tab-pane active">
                                        <div class="list-header">
                                            <div class="list-title">信息主题</div>
                                            <div class="list-meta">
                                                <span>地区</span>
                                                <span>有效期</span>
                                                <span>发布时间</span>
                                            </div>
                                        </div>
                                        <ul class="info-list">
                                            <?php foreach ($search_results as $result): ?>
                                                <?php
                                                if (!empty($result['expired_at'])) {
                                                    $days = getRemainingDaysInt($result['expired_at']);
                                                    $guoqi = $days > 0 ? '<span style="color: red; font-weight: bold;">'.$days.'</span> 天' : '已过期';
                                                } else {
                                                    $guoqi = '长期';
                                                }
                                                ?>
                                                <li>
                                                    <div class="info-title">
                                                        <?php if (!empty($result['category_name'])): ?><a href="/<?php echo $result['category_pinyin']; ?>/" style="color: #999; text-decoration: none; margin-right: 5px;">[<?php echo $result['category_name']; ?>]</a><?php endif; ?>
                                                        <a <?php if ($guoqi=='已过期') echo 'style="text-decoration: line-through; color: #999;"'; ?> href="/<?php echo $result['category_pinyin']; ?>/<?php echo $result['id']; ?>.html"><?php echo $result['title_highlight']; ?></a>
                                                        <?php
                                                        // 检查是否显示置顶标记（需要检查过期时间）
                                                        $current_time = time();
                                                        $show_top_tag = false;

                                                        // 检查大分类置顶
                                                        if (isset($result['is_top_category']) && $result['is_top_category'] == 1) {
                                                            if (!isset($result['top_category_expire']) || $result['top_category_expire'] == 0 || $result['top_category_expire'] > $current_time) {
                                                                $show_top_tag = true;
                                                            }
                                                        }

                                                        // 检查小分类置顶（搜索页面显示所有置顶类型）
                                                        if (!$show_top_tag && isset($result['is_top_subcategory']) && $result['is_top_subcategory'] == 1) {
                                                            if (!isset($result['top_subcategory_expire']) || $result['top_subcategory_expire'] == 0 || $result['top_subcategory_expire'] > $current_time) {
                                                                $show_top_tag = true;
                                                            }
                                                        }

                                                        if ($show_top_tag) {
                                                            echo '<span class="top-tag">顶</span>';
                                                        }
                                                        ?>
                                                    </div>
                                                    <div class="info-meta">
                                                        <span class="area"><?php echo $result['region_name']; ?></span>
                                                        <span class="validity"><?php echo $guoqi; ?></span>
                                                        <span class="time"><?php echo friendlyTime($result['created_at']); ?></span>
                                                    </div>
                                                </li>
                                            <?php endforeach; ?>
                                        </ul>
                                    </div>
                                </div>

                                <?php if ($total_pages > 1): ?>
                                    <div class="pagination">
                                        <?php if ($current_page > 1): ?>
                                            <a href="/search.php?keyword=<?php echo urlencode($keyword); ?>&page=1" class="nav-btn">首页</a>
                                            <a href="/search.php?keyword=<?php echo urlencode($keyword); ?>&page=<?php echo $current_page - 1; ?>" class="nav-btn">上一页</a>
                                        <?php endif; ?>

                                        <?php
                                        $startPage = max(1, $current_page - 2);
                                        $endPage = min($total_pages, $startPage + 4);
                                        if ($endPage - $startPage < 4) {
                                            $startPage = max(1, $endPage - 4);
                                        }

                                        for ($i = $startPage; $i <= $endPage; $i++):
                                        ?>
                                            <?php if ($i == $current_page): ?>
                                                <span class="current"><?php echo $i; ?></span>
                                            <?php else: ?>
                                                <a href="/search.php?keyword=<?php echo urlencode($keyword); ?>&page=<?php echo $i; ?>"><?php echo $i; ?></a>
                                            <?php endif; ?>
                                        <?php endfor; ?>

                                        <?php if ($current_page < $total_pages): ?>
                                            <a href="/search.php?keyword=<?php echo urlencode($keyword); ?>&page=<?php echo $current_page + 1; ?>" class="nav-btn">下一页</a>
                                            <a href="/search.php?keyword=<?php echo urlencode($keyword); ?>&page=<?php echo $total_pages; ?>" class="nav-btn">末页</a>
                                        <?php endif; ?>
                                    </div>

                                    <!-- 快速跳转 -->
                                    <?php if ($total_pages > 10): ?>
                                    <div class="quick-jump" style="text-align: center; margin-top: 10px;">
                                        <span style="color: #666; font-size: 12px; margin-right: 8px;">跳转到：</span>
                                        <input type="number" id="jump-page" min="1" max="<?php echo $total_pages; ?>"
                                               style="width: 50px; height: 24px; padding: 2px 6px; border: 1px solid #e0e0e0; border-radius: 3px; text-align: center; font-size: 12px;"
                                               placeholder="<?php echo $current_page; ?>">
                                        <button onclick="jumpToPage()"
                                                style="margin-left: 6px; padding: 2px 8px; height: 24px; background: #c4284d; color: white; border: none; border-radius: 3px; cursor: pointer; font-size: 12px;">
                                            跳转
                                        </button>
                                    </div>
                                    <?php endif; ?>
                                <?php endif; ?>

                            <?php else: ?>
                                <div class="no-info-message">
                                    <i class="no-info-icon"></i>
                                    <p>没有找到与 <strong>"<?php echo $keyword; ?>"</strong> 相关的信息</p>
                                    <p>建议：</p>
                                    <ul>
                                        <li>请检查您的拼写</li>
                                        <li>尝试使用更常见的关键词</li>
                                        <li>尝试使用更短的关键词</li>
                                    </ul>
                                </div>
                            <?php endif; ?>
                        <?php else: ?>
                            <div class="no-info-message">
                                <i class="no-info-icon"></i>
                                <p>请在上方搜索框中输入关键词进行搜索</p>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>

                <!-- 右侧栏 -->
                <div class="yui-index-info-list-right">
                    <div class="bbs-hot">
                        <div class="yui-h-title">
                            <h3>公益广告</h3><span></span>
                        </div>
                        <div class="yui-img-list">
                            <a href=""><img src="/template/pc/images/16382506823782.png" /></a>
                        </div>
                    </div>
                    <div class="bbs-hot">
                        <div class="yui-h-title">
                            <h3>最新信息</h3><span><a href="/">更多</a></span>
                        </div>
                        <div class="yui-small-list">
                            <ul>
                                {loop $latest_posts $post}
                                <li><a href="/{$post.category_pinyin}/{$post.id}.html" target="_blank">{$post.title}</a></li>
                                {/loop}
                            </ul>
                        </div>
                    </div>
                    
                    <div class="bbs-hot">
                        <div class="yui-h-title">
                            <h3>便民服务</h3><span></span>
                        </div>
                        <div class="yui-small-list">
                            <ul>
                                <li><a href="#" target="_blank">泊头市公交线路</a></li>
                                <li><a href="#" target="_blank">泊头天气预报</a></li>
                                <li><a href="#" target="_blank">泊头医院电话</a></li>
                                <li><a href="#" target="_blank">泊头学校信息</a></li>
                                <li><a href="#" target="_blank">泊头市政服务</a></li>
                                <li><a href="#" target="_blank">泊头求职招聘</a></li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 底部 -->
    {include file="footer.htm"}

    <style>
        .search-box {
            display: flex;
            margin-bottom: 10px;
        }
        .search-input {
            flex: 1;
            padding: 6px 10px;
            border: 1px solid #ddd;
            border-radius: 3px 0 0 3px;
            font-size: 13px;
            outline: none;
        }
        .search-btn {
            padding: 6px 15px;
            background-color: #ff6600;
            color: #fff;
            border: none;
            border-radius: 0 3px 3px 0;
            cursor: pointer;
            font-size: 13px;
        }
        .search-notice {
            background-color: #fff9e3;
            border: 1px solid #ffe7bc;
            border-radius: 3px;
            padding: 8px 10px;
            margin: 0 10px 10px;
            font-size: 12px;
            color: #666;
        }
        .notice-title {
            color: #ff6600;
            margin-bottom: 5px;
            font-weight: bold;
        }
        .notice-content p {
            margin-bottom: 3px;
            line-height: 1.5;
        }
        .notice-content a {
            color: #ff6600;
            text-decoration: none;
        }
        .notice-content a:hover {
            text-decoration: underline;
        }
        .no-info-message {
            text-align: center;
            padding: 30px 0;
        }
        .no-info-message p {
            margin-bottom: 10px;
        }
        .no-info-message ul {
            display: inline-block;
            text-align: left;
            color: #666;
        }
        .pd10 {
            padding: 10px;
        }
        .highlight {
            background-color: #fff3cd;
            color: #856404;
            font-weight: bold;
            padding: 2px 4px;
            border-radius: 2px;
        }

        /* 搜索间隔提示样式 */
        .search-error-message {
            animation: fadeIn 0.3s ease-in;
        }

        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(-10px); }
            to { opacity: 1; transform: translateY(0); }
        }

        .search-btn:disabled {
            background-color: #ccc;
            cursor: not-allowed;
            opacity: 0.6;
        }
    </style>

    <script>
    // 搜索间隔管理
    var SearchInterval = {
        interval: <?php echo isset($config['search_interval']) ? intval($config['search_interval']) : 5; ?>, // 搜索间隔（秒）
        lastSearchTime: 0,
        countdownTimer: null,

        // 初始化
        init: function() {
            this.lastSearchTime = this.getLastSearchTime();
            this.bindEvents();
            this.checkSearchInterval();
        },

        // 绑定事件
        bindEvents: function() {
            var self = this;
            var searchForm = document.getElementById('search-form');
            var searchBtn = document.getElementById('search-btn');

            if (searchForm) {
                searchForm.addEventListener('submit', function(e) {
                    if (!self.canSearch()) {
                        e.preventDefault();
                        self.showIntervalMessage();
                        return false;
                    }

                    // 显示加载状态
                    self.showLoadingState();

                    // 记录搜索时间
                    self.setLastSearchTime();
                });
            }
        },

        // 显示加载状态
        showLoadingState: function() {
            var searchBtn = document.getElementById('search-btn');
            var searchLoading = document.getElementById('search-loading');

            if (searchBtn) {
                searchBtn.classList.add('loading');
                searchBtn.disabled = true;
            }

            if (searchLoading) {
                searchLoading.style.display = 'flex';
            }
        },

        // 隐藏加载状态
        hideLoadingState: function() {
            var searchBtn = document.getElementById('search-btn');
            var searchLoading = document.getElementById('search-loading');

            if (searchBtn) {
                searchBtn.classList.remove('loading');
                searchBtn.disabled = false;
            }

            if (searchLoading) {
                searchLoading.style.display = 'none';
            }
        },

        // 检查是否可以搜索
        canSearch: function() {
            var now = Math.floor(Date.now() / 1000);
            var timeDiff = now - this.lastSearchTime;
            return timeDiff >= this.interval;
        },

        // 获取剩余等待时间
        getRemainingTime: function() {
            var now = Math.floor(Date.now() / 1000);
            var timeDiff = now - this.lastSearchTime;
            return Math.max(0, this.interval - timeDiff);
        },

        // 显示间隔提示消息
        showIntervalMessage: function() {
            var remainingTime = this.getRemainingTime();
            if (remainingTime > 0) {
                this.showErrorMessage('搜索过于频繁，请等待 ' + remainingTime + ' 秒后再试');
                this.startCountdown(remainingTime);
            }
        },

        // 显示错误消息
        showErrorMessage: function(message) {
            // 创建或更新错误消息元素
            var errorDiv = document.querySelector('.search-error-message');
            if (!errorDiv) {
                errorDiv = document.createElement('div');
                errorDiv.className = 'search-error-message';
                errorDiv.style.cssText = 'background: #fff3cd; border: 1px solid #ffeaa7; color: #856404; padding: 10px; margin-bottom: 10px; border-radius: 4px;';

                var searchForm = document.querySelector('.search-form');
                var form = document.getElementById('search-form');
                searchForm.insertBefore(errorDiv, form);
            }

            errorDiv.innerHTML = '<i class="fas fa-exclamation-triangle" style="margin-right: 5px;"></i>' +
                                '<span id="search-error-text">' + message + '</span>' +
                                '<span id="countdown-timer" style="font-weight: bold;"></span>';
            errorDiv.style.display = 'block';
        },

        // 隐藏错误消息
        hideErrorMessage: function() {
            var errorDiv = document.querySelector('.search-error-message');
            if (errorDiv) {
                errorDiv.style.display = 'none';
            }
        },

        // 开始倒计时
        startCountdown: function(seconds) {
            var self = this;
            var countdownElement = document.getElementById('countdown-timer');
            var searchBtn = document.getElementById('search-btn');

            // 禁用搜索按钮
            if (searchBtn) {
                searchBtn.disabled = true;
            }

            // 清除之前的计时器
            if (this.countdownTimer) {
                clearInterval(this.countdownTimer);
            }

            // 更新倒计时显示
            var updateCountdown = function() {
                if (seconds > 0) {
                    if (countdownElement) {
                        countdownElement.textContent = '（' + seconds + '秒后可再次搜索）';
                    }
                    seconds--;
                } else {
                    // 倒计时结束
                    clearInterval(self.countdownTimer);
                    self.countdownTimer = null;

                    // 启用搜索按钮
                    if (searchBtn) {
                        searchBtn.disabled = false;
                    }

                    // 隐藏错误消息
                    self.hideErrorMessage();
                }
            };

            // 立即执行一次
            updateCountdown();

            // 每秒更新
            this.countdownTimer = setInterval(updateCountdown, 1000);
        },

        // 检查搜索间隔
        checkSearchInterval: function() {
            var remainingTime = this.getRemainingTime();
            if (remainingTime > 0) {
                this.showIntervalMessage();
            }
        },

        // 获取上次搜索时间
        getLastSearchTime: function() {
            try {
                return parseInt(localStorage.getItem('lastSearchTime') || '0');
            } catch (e) {
                return 0;
            }
        },

        // 设置上次搜索时间
        setLastSearchTime: function() {
            try {
                var now = Math.floor(Date.now() / 1000);
                localStorage.setItem('lastSearchTime', now.toString());
                this.lastSearchTime = now;
            } catch (e) {
                // localStorage 不可用时的降级处理
                this.lastSearchTime = Math.floor(Date.now() / 1000);
            }
        }
    };

    // 页面加载完成后初始化
    document.addEventListener('DOMContentLoaded', function() {
        SearchInterval.init();
    });

    // 如果页面已经有搜索错误，启动倒计时
    <?php if (!empty($search_error) && preg_match('/请等待 (\d+) 秒/', $search_error, $matches)): ?>
    document.addEventListener('DOMContentLoaded', function() {
        SearchInterval.startCountdown(<?php echo $matches[1]; ?>);
    });
    <?php endif; ?>

    // 分页优化功能
    var PaginationOptimizer = {
        init: function() {
            this.bindPaginationEvents();
            this.preloadNextPage();
        },

        // 绑定分页点击事件
        bindPaginationEvents: function() {
            var self = this;
            var paginationLinks = document.querySelectorAll('.pagination a');

            paginationLinks.forEach(function(link) {
                // 添加点击加载状态
                link.addEventListener('click', function(e) {
                    // 分页点击不受搜索间隔限制
                    self.showPaginationLoading(this);
                });

                // 鼠标悬停预加载
                link.addEventListener('mouseenter', function() {
                    self.preloadPage(this.href);
                });
            });
        },

        // 显示分页加载状态
        showPaginationLoading: function(link) {
            link.style.opacity = '0.6';
        },

        // 预加载下一页
        preloadNextPage: function() {
            var currentPage = <?php echo $current_page; ?>;
            var totalPages = <?php echo $total_pages; ?>;
            var keyword = '<?php echo addslashes($keyword); ?>';

            if (currentPage < totalPages) {
                var nextPageUrl = '/search.php?keyword=' + encodeURIComponent(keyword) + '&page=' + (currentPage + 1);
                this.preloadPage(nextPageUrl);
            }
        },

        // 预加载页面
        preloadPage: function(url) {
            // 创建隐藏的link标签进行预加载
            var link = document.createElement('link');
            link.rel = 'prefetch';
            link.href = url;
            document.head.appendChild(link);

            // 5秒后移除预加载链接
            setTimeout(function() {
                if (link.parentNode) {
                    link.parentNode.removeChild(link);
                }
            }, 5000);
        }
    };

    // 键盘导航功能
    var KeyboardNavigation = {
        init: function() {
            var self = this;
            document.addEventListener('keydown', function(e) {
                // 只在没有输入框聚焦时启用键盘导航
                if (document.activeElement.tagName === 'INPUT') return;

                var currentPage = <?php echo $current_page; ?>;
                var totalPages = <?php echo $total_pages; ?>;
                var keyword = '<?php echo addslashes($keyword); ?>';

                switch(e.key) {
                    case 'ArrowLeft':
                    case 'h':
                        if (currentPage > 1) {
                            e.preventDefault();
                            window.location.href = '/search.php?keyword=' + encodeURIComponent(keyword) + '&page=' + (currentPage - 1);
                        }
                        break;
                    case 'ArrowRight':
                    case 'l':
                        if (currentPage < totalPages) {
                            e.preventDefault();
                            window.location.href = '/search.php?keyword=' + encodeURIComponent(keyword) + '&page=' + (currentPage + 1);
                        }
                        break;
                    case 'Home':
                        if (currentPage > 1) {
                            e.preventDefault();
                            window.location.href = '/search.php?keyword=' + encodeURIComponent(keyword) + '&page=1';
                        }
                        break;
                    case 'End':
                        if (currentPage < totalPages) {
                            e.preventDefault();
                            window.location.href = '/search.php?keyword=' + encodeURIComponent(keyword) + '&page=' + totalPages;
                        }
                        break;
                }
            });
        }
    };

    // 快速跳转功能
    function jumpToPage() {
        var jumpPage = document.getElementById('jump-page');
        var page = parseInt(jumpPage.value);
        var totalPages = <?php echo $total_pages; ?>;
        var keyword = '<?php echo addslashes($keyword); ?>';

        if (page && page >= 1 && page <= totalPages) {
            window.location.href = '/search.php?keyword=' + encodeURIComponent(keyword) + '&page=' + page;
        } else {
            alert('请输入有效的页码（1-' + totalPages + '）');
            jumpPage.focus();
        }
    }

    // 页面加载完成后初始化所有优化功能
    document.addEventListener('DOMContentLoaded', function() {
        if (document.querySelector('.pagination')) {
            PaginationOptimizer.init();
            KeyboardNavigation.init();

            // 快速跳转输入框回车支持
            var jumpPageInput = document.getElementById('jump-page');
            if (jumpPageInput) {
                jumpPageInput.addEventListener('keypress', function(e) {
                    if (e.key === 'Enter') {
                        jumpToPage();
                    }
                });
            }

            // 显示简洁的键盘导航提示
            if (<?php echo $total_pages; ?> > 3) {
                var keyboardHint = document.createElement('div');
                keyboardHint.innerHTML = '💡 使用 ← → 键快速翻页';
                keyboardHint.style.cssText = `
                    position: fixed;
                    bottom: 20px;
                    right: 20px;
                    background: rgba(196,40,77,0.9);
                    color: white;
                    padding: 8px 12px;
                    border-radius: 3px;
                    font-size: 12px;
                    z-index: 1000;
                    transition: opacity 0.3s ease;
                `;
                document.body.appendChild(keyboardHint);

                setTimeout(function() {
                    keyboardHint.style.opacity = '0';
                    setTimeout(function() {
                        if (keyboardHint.parentNode) {
                            keyboardHint.parentNode.removeChild(keyboardHint);
                        }
                    }, 300);
                }, 2000);
            }
        }
    });
    </script>
</body>
</html>