/* 移动端发布页样式 */

/* 发布表单与内容区 */
.post-form {
    background-color: #fff;
    padding: 15px;
    margin-bottom: 10px;
}

.form-title {
    font-size: 17px;
    font-weight: 600;
    margin-bottom: 15px;
    padding-bottom: 10px;
    border-bottom: 1px solid #f5f5f5;
    color: #333;
    font-family: var(--font-family);
}

/* 表单样式 */
.form-panel {
    background-color: #fff;
    margin-bottom: 8px;
    border-radius: 0;
    padding: 0;
    box-shadow: none;
    overflow: hidden;
}

.form-group {
    padding: 10px 10px;
    display: flex;
    align-items: center;
    border-bottom: 1px solid #eeeeee;
}

.form-group:last-child {
    border-bottom: none;
}

.form-group-last {
    margin-bottom: 5px;
}

.form-group-noborder {
    border-bottom: none;
}

.form-label {
    width: 90px;
    flex-shrink: 0;
    font-weight: 500;
    font-size: 15px;
    color: #333;
    margin-bottom: 0;
    font-family: var(--font-family);
    display: flex;
    align-items: center;
}

.form-right {
    flex: 1;
    margin-left: 8px;
    display: flex;
    align-items: center;
    justify-content: flex-end;
    flex-wrap: nowrap;
}

.form-control {
    width: 100%;
    padding: 8px 0;
    border: none;
    border-radius: 0;
    font-size: 15px;
    background-color: transparent;
    text-align: left;
}

.form-control::placeholder {
    color: #cccccc;
    text-align: left;
}

.form-select {
    height: 36px;
    padding-right: 20px;
    padding-left: 0;
    text-overflow: ellipsis;
    white-space: nowrap;
    overflow: hidden;
    -webkit-appearance: none;
    -moz-appearance: none;
    appearance: none;
    background-image: none;
    background-repeat: no-repeat;
    background-position: right 0 center;
    background-size: 12px 6px;
    font-size: 15px;
    text-align: left;
}

.form-arrow:after {
    content: '';
}

.form-arrow .form-right:after {
    content: '\f054';
    font-family: 'Font Awesome 5 Free';
    font-weight: 900;
    font-size: 14px;
    color: #ccc;
    margin-left: 5px;
}

.required .form-label:before {
    content: "";
}

.required .form-label:after {
    content: "*";
    color: #ff3b30;
    font-size: 16px;
    margin-left: 4px;
}

.form-vertical.required .form-label:after {
    margin-left: 4px;
}

.form-vertical.required .form-label:before {
    content: "";
    margin-right: 0;
}

/* 文本框右对齐 */
input.form-control, select.form-control {
    text-align: left;
}

/* 调整多行文本样式 */
.form-textarea {
    height: 90px;
    resize: vertical;
    text-align: left;
    padding: 8px;
    border: 1px solid #eee;
    border-radius: 4px;
}

.form-hint {
    font-size: 13px;
    color: #999;
    margin-top: 2px;
    display: block;
    text-align: left;
}

.form-error {
    color: #ff3b30;
    font-size: 13px;
    margin-top: 6px;
    display: block;
    text-align: left;
}

/* 图片上传相关 */
.image-upload {
    margin-top: 2px;
    width: 100%;
    text-align: left;
}

.hidden-input {
    display: none;
}

/* 图片上传 */
.upload-section {
    margin-bottom: 20px;
}

.upload-title {
    font-size: 16px;
    color: #333;
    margin-bottom: 10px;
    font-weight: 500;
}

.upload-desc {
    font-size: 13px;
    color: #999;
    margin-bottom: 10px;
}

.upload-preview {
    display: flex;
    flex-wrap: wrap;
    margin-bottom: 10px;
}

.preview-item {
    width: 80px;
    height: 80px;
    margin-right: 10px;
    margin-bottom: 10px;
    position: relative;
    border: 1px dashed #ddd;
    border-radius: 4px;
    overflow: hidden;
}

.preview-item img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.preview-remove {
    position: absolute;
    top: 0;
    right: 0;
    width: 20px;
    height: 20px;
    background-color: rgba(0,0,0,0.5);
    color: #fff;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 12px;
    cursor: pointer;
}

.upload-btn {
    display: inline-flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    width: 75px;
    height: 75px;
    color: #999;
    border: 1px dashed #ddd;
    border-radius: 3px;
    cursor: pointer;
    font-size: 22px;
    margin-bottom: 5px;
    margin-right: 8px;
    background-color: #fafafa;
}

.upload-btn:active {
    border-color: var(--primary-color, #ff6600);
    color: var(--primary-color, #ff6600);
}

.upload-title {
    font-size: 16px;
    color: #333;
    margin-bottom: 12px;
    font-weight: 500;
}

.upload-desc {
    font-size: 13px;
    color: #999;
    margin-bottom: 12px;
}

.image-hint {
    display: inline-block;
    vertical-align: middle;
}

.image-previews {
    display: flex;
    flex-wrap: wrap;
    margin-top: 10px;
}

.image-item {
    width: 80px;
    height: 80px;
    margin-right: 8px;
    margin-bottom: 8px;
    position: relative;
}

.image-item img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    border-radius: 4px;
}

.remove-image {
    position: absolute;
    top: -8px;
    right: -8px;
    width: 20px;
    height: 20px;
    background-color: #ff3b30;
    color: #fff;
    border-radius: 50%;
    text-align: center;
    line-height: 20px;
    cursor: pointer;
    font-size: 12px;
}

/* 按钮样式 */
.btn-group {
    display: flex;
    justify-content: space-between;
    margin: 10px 0;
    padding: 0 12px;
}

.btn {
    flex: 1;
    margin: 0 5px;
    height: 45px;
    border: none;
    border-radius: 4px;
    font-size: 16px;
    font-weight: bold;
    cursor: pointer;
}

.btn-primary {
    background-color: var(--primary-color, #ff6600);
    color: #fff;
}

.submit-container {
    border-bottom: none;
    padding: 0;
    margin-top: 12px;
}

.submit-group {
    padding: 0;
    width: calc(100% - 20px);
    margin: 15px 10px 20px;
    position: static;
    z-index: 1;
}

.submit-button {
    border-radius: 4px;
    height: 48px;
    font-size: 16px;
    font-weight: 500;
    background-color: var(--primary-color, #ff9800);
    color: #fff;
    border: none;
    width: 100%;
    margin: 0;
}

.submit-button i {
    margin-right: 5px;
}

/* 微信复选框样式 */
.weixin-checkbox {
    display: flex;
    align-items: center;
    margin: 0;
    font-size: 14px;
    color: #666;
}

.weixin-checkbox input {
    margin-right: 4px;
    width: 16px;
    height: 16px;
}

.btn-default {
    background-color: #f0f0f0;
    color: #333;
}

/* 表单相关其他元素 */
.verification-input {
    display: flex;
}

.verification-input input {
    flex: 1;
    margin-right: 10px;
}

.verification-input .send-code {
    width: 100px;
    background-color: var(--primary-color, #ff6600);
    color: #fff;
    border: none;
    border-radius: 4px;
    cursor: pointer;
}

.verification-input .send-code:disabled {
    background-color: #f0f0f0;
    color: #999;
}

/* 复选框和单选框 */
.checkbox-inline {
    display: flex;
    align-items: center;
    margin-bottom: 8px;
}

.checkbox-inline input {
    margin-right: 5px;
}

.checkbox {
    display: flex;
    align-items: center;
}

.radio-group, .checkbox-group {
    display: flex;
    flex-wrap: wrap;
    width: 100%;
    margin-top: 3px;
}

.radio-item, .checkbox-item {
    margin-right: 12px;
    margin-bottom: 8px;
    display: flex;
    align-items: center;
}

.radio-item input, .checkbox-item input {
    margin-right: 6px;
    width: 18px;
    height: 18px;
}

/* 错误和提示消息 */
.error-message {
    background-color: #ffebee;
    padding: 10px 15px;
    margin-bottom: 10px;
    color: #d32f2f;
    border-left: 3px solid #f44336;
}

.error-message i {
    margin-right: 5px;
}

.form-notice {
    margin-bottom: 15px;
    padding: 10px;
    background-color: #fff9e3;
    border: 1px solid #ffe5bc;
    border-radius: 4px;
    color: #885000;
    font-size: 12px;
}

.form-notice h4 {
    margin-bottom: 6px;
    font-weight: bold;
    color: var(--primary-color, #ff6600);
    font-size: 14px;
}

/* 栏目选择样式 */
.category-selected {
    display: flex;
    align-items: center;
    justify-content: space-between;
    min-height: 36px;
}

.category-name {
    font-size: 15px;
    color: #333;
    margin-right: 5px;
}

.reselect-category {
    color: #999;
    font-size: 13px;
    text-decoration: none;
}

/* 发布须知 */
.posting-notice {
    margin-bottom: 0;
    padding: 8px 10px;
    background-color: #fff9f0;
    border-radius: 0;
    border: none;
}

.notice-title {
    margin-bottom: 4px;
    font-weight: 500;
    color: var(--primary-color, #ff6600);
    font-size: 15px;
}

.notice-title i {
    margin-right: 6px;
}

.notice-content {
    color: #666;
    font-size: 13px;
    line-height: 1.5;
}

.agreement-link {
    color: var(--primary-color, #ff6600);
    text-decoration: underline;
}

/* 分类选择页样式 */
.tip-panel {
    background-color: #fff8e1;
    padding: 12px 15px;
    margin: 0;
    font-size: 14px;
    color: #ff8f00;
    line-height: 1.5;
    border-bottom: 1px solid #ffe0b2;
}

.category-container {
    display: flex;
    height: calc(100vh - 113px);
    overflow: hidden;
}

.left-menu {
    width: 35%;
    background-color: #f8f9fa;
    overflow-y: auto;
    border-right: 1px solid #e0e0e0;
    height: 100%;
}

.right-content {
    width: 65%;
    background-color: #fff;
    overflow-y: auto;
    height: 100%;
}

.category-item {
    display: flex;
    align-items: center;
    padding: 15px;
    border-bottom: 1px solid #eee;
    color: #333;
    text-decoration: none;
    position: relative;
}

.category-item.active {
    background-color: #fff;
    border-left: 3px solid var(--primary-color, #ff6600);
}

.category-item:active {
    background-color: #f9f9f9;
}

.category-item .icon {
    width: 24px;
    height: 24px;
    margin-right: 15px;
    vertical-align: middle;
}

.category-item .name {
    font-size: 16px;
    flex: 1;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.subcategory-item {
    display: block;
    padding: 15px;
    border-bottom: 1px solid #f5f5f5;
    color: #333;
    text-decoration: none;
    font-size: 15px;
}

.subcategory-item:last-child {
    border-bottom: none;
}

.subcategory-item:active {
    background-color: #f9f9f9;
}

.subcategory-list {
    display: none;
}

.subcategory-list.active {
    display: block;
}

/* 移动端友好的提示框样式 */
.toast-container {
    position: fixed;
    bottom: 30px;
    left: 0;
    right: 0;
    display: flex;
    justify-content: center;
    z-index: 9999;
    pointer-events: none;
}
.toast {
    background-color: rgba(0, 0, 0, 0.7);
    color: #fff;
    padding: 12px 20px;
    border-radius: 25px;
    margin: 0 15px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
    opacity: 0;
    transform: translateY(20px);
    transition: all 0.3s ease;
    max-width: 80%;
    text-align: center;
    font-size: 14px;
}
.toast.success {
    background-color: rgba(76, 175, 80, 0.9);
}
.toast.error {
    background-color: rgba(244, 67, 54, 0.9);
}
.toast.show {
    opacity: 1;
    transform: translateY(0);
}

/* 移动端友好的确认对话框样式 */
.confirm-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.5);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 9999;
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;
}
.confirm-overlay.show {
    opacity: 1;
    visibility: visible;
}
.confirm-dialog {
    background-color: #fff;
    border-radius: 8px;
    width: 80%;
    max-width: 300px;
    overflow: hidden;
    transform: scale(0.9);
    transition: transform 0.3s ease;
}
.confirm-overlay.show .confirm-dialog {
    transform: scale(1);
}
.confirm-title {
    padding: 15px;
    text-align: center;
    font-size: 16px;
    font-weight: bold;
    color: #333;
    border-bottom: 1px solid #eee;
}
.confirm-buttons {
    display: flex;
    border-top: 1px solid #eee;
}
.confirm-btn {
    flex: 1;
    padding: 12px 0;
    text-align: center;
    background: none;
    border: none;
    font-size: 15px;
    cursor: pointer;
}
.confirm-btn.cancel {
    color: #999;
    border-right: 1px solid #eee;
}
.confirm-btn.confirm {
    color: var(--primary-color, #ff6600);
    font-weight: bold;
}

.form-hint-container {
    padding: 0 10px 8px;
    border-top: none;
    text-align: left;
}

.checkbox-container {
    border-top: none;
    padding-top: 0;
    padding-bottom: 10px;
}

/* 整体页面背景 */
body {
    background-color: #f5f5f5;
}

/* 为表单项添加右箭头指示 */
.form-arrow .form-right:after {
    content: '\f054';
    font-family: 'Font Awesome 5 Free';
    font-weight: 900;
    font-size: 14px;
    color: #ccc;
    margin-left: 8px;
}

/* 表单组件居中 */
.post-content {
    padding: 0;
    margin: 0;
    background-color: #f5f5f5;
}

/* 修改头部样式 */
.header-title {
    font-size: 18px;
    font-weight: 500;
    flex: 1;
    text-align: center;
}

/* 去除表单控件点击后的焦点框 */
.form-control:focus,
.form-select:focus,
.form-textarea:focus,
select:focus,
input:focus,
textarea:focus,
button:focus {
    outline: none;
    box-shadow: none;
    -webkit-tap-highlight-color: transparent;
}

/* 确保iOS上也没有高亮效果 */
input, 
select, 
textarea, 
button {
    -webkit-appearance: none;
    -webkit-tap-highlight-color: transparent;
}

/* 移除点击时的边框变化 */
.form-control:focus {
    border-color: #eee;
}

/* 移除Safari上的内阴影 */
input[type="text"],
input[type="tel"],
input[type="password"],
textarea {
    -webkit-appearance: none;
}

/* 修复之前样式中可能存在的矛盾设置 */
.btn-primary:focus,
.submit-button:focus,
.weixin-checkbox input:focus,
.radio-item input:focus, 
.checkbox-item input:focus {
    outline: none !important;
    box-shadow: none !important;
}

/* 确保点击右侧箭头区域也没有高亮 */
.form-arrow .form-right {
    -webkit-tap-highlight-color: transparent;
}

/* 确保checkbox和radio在点击时无边框 */
input[type="checkbox"],
input[type="radio"] {
    appearance: none;
    -webkit-appearance: none;
    border: 1px solid #ccc;
    width: 18px;
    height: 18px;
    background-color: #fff;
    border-radius: 3px;
    position: relative;
    outline: none;
}

input[type="radio"] {
    border-radius: 50%;
}

input[type="checkbox"]:checked,
input[type="radio"]:checked {
    background-color: var(--primary-color, #ff9800);
    border-color: var(--primary-color, #ff9800);
}

input[type="checkbox"]:checked:after {
    content: '\f00c';
    font-family: 'Font Awesome 5 Free';
    font-weight: 900;
    font-size: 10px;
    color: #fff;
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
}

input[type="radio"]:checked:after {
    content: '';
    width: 8px;
    height: 8px;
    background-color: #fff;
    border-radius: 50%;
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
}

/* 覆盖可能有的焦点样式 */
.form-control,
.form-select,
.form-textarea {
    border-color: #eee;
}

.form-control:active,
.form-select:active,
.form-textarea:active {
    border-color: #eee;
}

/* 去掉Chrome/Safari上表单元素的默认样式 */
input:-webkit-autofill,
input:-webkit-autofill:hover,
input:-webkit-autofill:focus {
    -webkit-box-shadow: 0 0 0px 1000px #fff inset !important;
    -webkit-text-fill-color: #333 !important;
    transition: background-color 5000s ease-in-out 0s;
}

/* 修改链接的点击效果 */
a {
    -webkit-tap-highlight-color: transparent;
}

a:focus {
    outline: none;
}

/* 自定义表单控件的激活状态 */
.form-textarea:focus {
    border-color: #eee;
}

.upload-btn:focus {
    outline: none;
}

/* 添加垂直布局表单样式 */
.form-vertical {
    padding: 10px 10px;
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    border-bottom: 1px solid #eeeeee;
}

.form-vertical .form-label {
    width: 100%;
    margin-bottom: 6px;
    padding-left: 0;
}

.form-vertical .form-right {
    width: 100%;
    margin-left: 0;
    justify-content: flex-start;
}

.form-vertical .form-control,
.form-vertical .form-textarea {
    text-align: left;
    border: 1px solid #eee;
    padding: 10px;
    background-color: transparent;
    border-radius: 4px;
}

.form-vertical.required .form-label:before {
    margin-right: 4px;
}

/* 调整图片上传样式 */
.form-vertical .image-upload {
    width: 100%;
    margin-top: 5px;
}

/* 调整textarea样式 */
.form-vertical .form-textarea {
    min-height: 100px;
    border: 1px solid #eee;
}

/* 除了提交按钮容器，不需要底部边框 */
.form-group.submit-container {
    border-bottom: none;
}

/* 复选框容器不需要上边框 */
.form-group.checkbox-container {
    border-top: none;
}

/* 正在提交和提交成功的样式 */
.loading-overlay,
.success-overlay,
.error-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.7);
    display: none;
    align-items: center;
    justify-content: center;
    z-index: 9999;
    transition: all 0.3s ease;
}

.loading-overlay.show,
.success-overlay.show,
.error-overlay.show {
    display: flex !important;
    opacity: 1;
    visibility: visible;
}

.loading-spinner {
    background-color: #fff;
    border-radius: 8px;
    padding: 20px 30px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    max-width: 80%;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
}

.loading-spinner i {
    font-size: 40px;
    color: var(--primary-color, #ff9800);
    margin-bottom: 10px;
}

.loading-text {
    font-size: 16px;
    color: #333;
    font-weight: 500;
}

.success-message {
    background-color: #fff;
    border-radius: 8px;
    padding: 30px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    width: 80%;
    max-width: 300px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
    text-align: center;
}

.success-message i {
    font-size: 50px;
    color: #4caf50;
    margin-bottom: 15px;
}

.success-text {
    font-size: 18px;
    color: #333;
    font-weight: 500;
    margin-bottom: 10px;
}

.success-subtext {
    font-size: 14px;
    color: #666;
    margin-bottom: 20px;
}

.success-btn {
    background-color: var(--primary-color, #ff9800);
    color: #fff;
    border: none;
    border-radius: 4px;
    padding: 10px 30px;
    font-size: 16px;
    font-weight: 500;
    cursor: pointer;
}

.success-btn:active {
    background-color: #f57c00;
}

/* 错误弹窗样式 */
.error-message-popup {
    background-color: #fff;
    border-radius: 8px;
    padding: 30px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    width: 80%;
    max-width: 300px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
    text-align: center;
}

.error-message-popup i {
    font-size: 50px;
    color: #ff3b30;
    margin-bottom: 15px;
}

.error-text {
    font-size: 18px;
    color: #333;
    font-weight: 500;
    margin-bottom: 10px;
}

.error-subtext {
    font-size: 14px;
    color: #666;
    margin-bottom: 20px;
    line-height: 1.5;
}

.error-btn {
    background-color: #ff3b30;
    color: white;
    border: none;
    padding: 12px 30px;
    border-radius: 6px;
    font-size: 16px;
    cursor: pointer;
    transition: all 0.3s ease;
    min-width: 100px;
}

.error-btn:active {
    background-color: #e6342a;
}

#contact_weixin {
    text-align: left !important;
    direction: ltr !important;
    text-indent: 0 !important;
}

/* 确保所有输入框提示文本左对齐 */
input::placeholder {
    text-align: left !important;
}

/* 确保所有输入框光标和文本左对齐 */
.form-control {
    text-align: left !important;
}

/* 发布须知折叠样式 */
.notice-title.collapsible {
    display: flex;
    align-items: center;
    justify-content: space-between;
    cursor: pointer;
    user-select: none;
}

.notice-title .toggle-icon {
    font-size: 12px;
    transition: transform 0.3s ease;
}

.notice-title .toggle-icon.rotate {
    transform: rotate(180deg);
}

.notice-content {
    color: #666;
    font-size: 13px;
    line-height: 1.5;
    max-height: 300px;
    overflow: hidden;
    transition: max-height 0.3s ease, margin 0.3s ease, opacity 0.3s ease;
    opacity: 1;
}

.notice-content.collapsed {
    max-height: 0;
    margin-top: 0;
    opacity: 0;
}

/* 选中分类样式 */
.selected-category {
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 100%;
}

.selected-category strong {
    font-size: 15px;
    font-weight: 500;
    color: #333;
}

.reselect-category {
    font-size: 13px;
    color: #999;
    text-decoration: none;
}

/* 主题适配样式调整，修复深色和抖音主题 */
html.theme-dark {
    background-color: #121212 !important;
}

html.theme-douyin {
    background-color: #000000 !important;
}

body.theme-dark {
    background-color: #121212;
    color: #e0e0e0;
}

body.theme-douyin {
    background-color: #000000;
    color: #ffffff;
}

/* 深色主题表单适配 */
.theme-dark .form-panel,
.theme-douyin .form-panel {
    background-color: #1e1e1e;
}

.theme-dark .form-group,
.theme-douyin .form-group {
    border-bottom: 1px solid #333333;
}

.theme-dark .form-label,
.theme-douyin .form-label {
    color: #e0e0e0;
}

.theme-dark .form-control,
.theme-douyin .form-control,
.theme-dark .form-textarea,
.theme-douyin .form-textarea {
    color: #ffffff;
    background-color: transparent;
}

.theme-dark .form-control::placeholder,
.theme-douyin .form-control::placeholder {
    color: #666666;
}

.theme-dark .form-hint,
.theme-douyin .form-hint {
    color: #999999;
}

.theme-dark .loading-spinner,
.theme-douyin .loading-spinner,
.theme-dark .success-message,
.theme-douyin .success-message {
    background-color: #1e1e1e;
}

.theme-dark .loading-text,
.theme-douyin .loading-text,
.theme-dark .success-text,
.theme-douyin .success-text {
    color: #e0e0e0;
}

.theme-dark .success-subtext,
.theme-douyin .success-subtext {
    color: #999999;
}

.theme-dark .posting-notice,
.theme-douyin .posting-notice {
    background-color: #2a2a2a;
}

.theme-dark .notice-content,
.theme-douyin .notice-content {
    color: #b0b0b0;
}

.theme-dark .upload-btn,
.theme-douyin .upload-btn {
    background-color: #2a2a2a;
    border-color: #444444;
}

.theme-dark .selected-category strong,
.theme-douyin .selected-category strong {
    color: #e0e0e0;
}

.theme-dark .form-select,
.theme-douyin .form-select {
    background-color: #1e1e1e;
    color: #ffffff;
}