{include file="header.htm"}

<div class="card mb-4">
    <div class="card-header d-flex justify-content-between align-items-center">
        <div>
            <h3 class="card-title">
                {if isset($selected_parent_id) && $selected_parent_id > 0}
                添加子栏目
                {else}
                添加新闻栏目
                {/if}
            </h3>
        </div>
        <div>
            {if isset($selected_parent_id) && $selected_parent_id > 0}
            <a href="news_category.php?parent_id={$selected_parent_id}" class="btn btn-sm btn-light-info">返回父栏目</a>
            {/if}
        </div>
    </div>
    
    <div class="card-body">
        <form action="news_category.php?op=add{if isset($selected_parent_id) && $selected_parent_id > 0}&parent_id={$selected_parent_id}{/if}" method="post" class="form-horizontal">
            <div class="form-group">
                <label class="form-label">上级栏目：</label>
                <div class="form-field">
                    <select name="parentid" class="form-control">
                        <option value="0">作为顶级栏目</option>
                        {if isset($parent_cats)}
                        {foreach from=$parent_cats item=cat}
                        <option value="{$cat.catid}" {if isset($selected_parent_id) && $selected_parent_id == $cat.catid}selected{/if}>{$cat.catname}</option>
                        {/foreach}
                        {/if}
                    </select>
                </div>
            </div>
            <div class="form-group">
                <label class="form-label">栏目名称：</label>
                <div class="form-field">
                    <input type="text" name="catname" class="form-control" required>
                </div>
            </div>
            <div class="form-group">
                <label class="form-label">栏目拼音：</label>
                <div class="form-field">
                    <input type="text" name="pinyin" class="form-control" placeholder="用于伪静态URL，如：botou-news">
                    <div class="form-hint">用于生成伪静态URL，只能包含字母、数字和连字符，如：botou-news</div>
                </div>
            </div>
            <div class="form-group">
                <label class="form-label">排序：</label>
                <div class="form-field">
                    <input type="number" name="sort_order" value="50" class="form-control">
                    <div class="form-hint">数字越小排序越靠前</div>
                </div>
            </div>
            <div class="form-group">
                <label class="form-label">是否显示：</label>
                <div class="form-field">
                    <label class="radio-inline">
                        <input type="radio" name="is_show" value="1" checked> 显示
                    </label>
                    <label class="radio-inline">
                        <input type="radio" name="is_show" value="0"> 隐藏
                    </label>
                </div>
            </div>
            <div class="form-group form-btn-group">
                <div class="form-field">
                    <button type="submit" class="btn btn-primary">保存</button>
                    <button type="button" class="btn btn-secondary" onclick="history.back();">返回</button>
                </div>
            </div>
        </form>
    </div>
</div>

{include file="footer.htm"}

<script>
// 只在DOM加载完成后执行
document.addEventListener('DOMContentLoaded', function() {
    // 设置当前菜单选中状态
    var menuElement = document.getElementById("menu_news_category");
    if (menuElement) {
        menuElement.className += " active";
    }
});
</script> 