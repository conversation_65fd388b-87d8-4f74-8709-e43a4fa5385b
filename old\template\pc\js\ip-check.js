// 立即执行的IP检查函数
(function() {
    // 创建并立即显示加载遮罩
    const loadingOverlay = document.createElement('div');
    loadingOverlay.style.cssText = `
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: #fff;
        z-index: 999999;
        display: flex;
        justify-content: center;
        align-items: center;
    `;
    loadingOverlay.innerHTML = '<div style="font-size: 16px; color: #666;">正在检查访问权限...</div>';
    
    // 在页面开始加载时就插入遮罩
    document.addEventListener('DOMContentLoaded', function() {
        document.body.appendChild(loadingOverlay);
        document.body.style.overflow = 'hidden';
    });

    // 创建访问限制遮罩层
    function createRestrictOverlay() {
        const overlay = document.createElement('div');
        overlay.style.cssText = `
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: #fff;
            z-index: 999999;
            display: flex;
            justify-content: center;
            align-items: center;
            flex-direction: column;
        `;

        const message = document.createElement('div');
        message.style.cssText = `
            font-size: 24px;
            color: #333;
            text-align: center;
            margin-bottom: 20px;
        `;
        message.innerHTML = '抱歉，本站仅对沧州地区开放<br>Sorry, this site is only available in Cangzhou area';

        const subMessage = document.createElement('div');
        subMessage.style.cssText = `
            font-size: 16px;
            color: #666;
            text-align: center;
        `;
        subMessage.textContent = '如有疑问请联系网站管理员';

        overlay.appendChild(message);
        overlay.appendChild(subMessage);
        
        // 替换加载遮罩
        if (loadingOverlay.parentNode) {
            loadingOverlay.parentNode.replaceChild(overlay, loadingOverlay);
        } else {
            document.body.appendChild(overlay);
        }
        document.body.style.overflow = 'hidden';
    }

    // 移除加载遮罩
    function removeLoadingOverlay() {
        if (loadingOverlay.parentNode) {
            loadingOverlay.parentNode.removeChild(loadingOverlay);
            document.body.style.overflow = '';
        }
    }

    // 检查IP地址
    fetch('https://ipapi.co/json/')
        .then(response => response.json())
        .then(data => {
            const location = (data.city || '').toLowerCase();
            const region = (data.region || '').toLowerCase();
            
            if (!location.includes('cangzhou') && !region.includes('cangzhou')) {
                createRestrictOverlay();
            } else {
                removeLoadingOverlay();
            }
        })
        .catch(error => {
            console.error('IP location check failed:', error);
            createRestrictOverlay();
        });
})();