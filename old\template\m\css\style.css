/* 消息提示页面样式 */

.message-panel {
    background-color: #fff;
    border-radius: 8px;
    margin: 20px 10px;
    padding: 25px 20px;
    text-align: center;
    box-shadow: 0 2px 10px rgba(0,0,0,0.08);
}

/* 错误页面样式 */
.error-icon {
    font-size: 60px;
    color: #ff3b30;
    margin-bottom: 20px;
}

.error-title {
    font-size: 22px;
    margin-bottom: 15px;
    color: #ff3b30;
    font-weight: 500;
    font-family: var(--font-family);
}

.error-message {
    margin-bottom: 25px;
    color: #666;
    line-height: 1.6;
    font-family: var(--font-family);
    font-size: 16px;
}

/* 成功页面样式 */
.success-icon {
    font-size: 60px;
    color: #4cd964;
    margin-bottom: 20px;
}

.success-title {
    font-size: 22px;
    margin-bottom: 15px;
    color: #4cd964;
    font-weight: 500;
    font-family: var(--font-family);
}

.success-message {
    margin-bottom: 25px;
    color: #666;
    line-height: 1.6;
    font-size: 16px;
}

.countdown {
    margin-top: 15px;
    color: #999;
    font-size: 13px;
}

/* 操作按钮样式 */
.btn {
    display: inline-block;
    padding: 12px 20px;
    background-color: var(--primary-color, #ff6600);
    color: #fff;
    border-radius: 6px;
    text-decoration: none;
    font-size: 15px;
    text-align: center;
    width: 80%;
    margin: 15px auto 0;
    border: none;
    cursor: pointer;
    font-weight: 500;
    box-shadow: 0 2px 8px rgba(255, 102, 0, 0.2);
    transition: all 0.3s ease;
}

.btn:active {
    transform: translateY(2px);
    box-shadow: 0 1px 3px rgba(255, 102, 0, 0.2);
}

/* 举报页面样式 */
.panel {
    background-color: #fff;
    padding: 15px;
    margin-bottom: 10px;
    border-radius: 0;
}

.panel-title {
    font-size: 17px;
    margin-bottom: 10px;
    padding-bottom: 8px;
    border-bottom: 1px solid #eee;
}

.post-info {
    margin-bottom: 10px;
    padding: 8px;
    background-color: #f9f9f9;
    border-radius: 0;
}

.post-info h3 {
    margin-bottom: 10px;
    font-size: 17px;
}

.post-info p {
    margin-bottom: 5px;
    color: #666;
    font-size: 14px;
}

.notice {
    background-color: #f8f8f8;
    padding: 10px;
    border-left: 3px solid var(--primary-color, #ff6600);
    margin-bottom: 10px;
    font-size: 14px;
}

/* 表单样式 */
.form-group {
    margin-bottom: 12px;
}

.form-group label {
    display: block;
    margin-bottom: 5px;
    font-weight: bold;
}

.radio-group {
    margin-bottom: 10px;
    display: flex;
    flex-wrap: wrap;
}

.radio-item {
    background-color: #f9f9f9;
    padding: 8px 10px;
    border-radius: 0;
    margin-right: 8px;
    margin-bottom: 8px;
    flex-grow: 1;
    min-width: 80px;
    text-align: center;
}

.radio-item input[type="radio"] {
    margin-right: 8px;
}

.form-control {
    width: 100%;
    padding: 10px;
    border: 1px solid #ddd;
    border-radius: 0;
}

textarea.form-control {
    height: 120px;
    resize: none;
}

.btn-secondary {
    background-color: #999;
    margin-left: 10px;
}

.btn-group {
    display: flex;
    margin-bottom: 10px;
}

/* 404错误页面样式 */
.error-container {
    padding: 30px 20px;
    max-width: 500px;
    margin: 0 auto;
}

.error-box {
    background-color: #fff;
    border-radius: 8px;
    padding: 30px 20px;
    text-align: center;
    box-shadow: 0 1px 3px rgba(0,0,0,0.1);
}

.page-not-found .error-icon {
    font-size: 60px;
    color: var(--primary-color, #ff6600);
    margin-bottom: 20px;
}

.page-not-found .error-title {
    font-size: 20px;
    font-weight: bold;
    margin-bottom: 15px;
    color: #333;
}

.page-not-found .btn {
    border-radius: 20px;
    max-width: 200px;
}
