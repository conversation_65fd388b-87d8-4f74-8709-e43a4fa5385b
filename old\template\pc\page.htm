<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="utf-8">
    <title>{if $title}{$title} - {$site_name}{else}{$site_name}{/if}</title>
    <meta name="keywords" content="{if $meta_keywords}{$meta_keywords}{elseif $site_keywords}{$site_keywords}{else}分类信息,免费发布,信息平台{/if}" />
    <meta name="description" content="{if $meta_description}{$meta_description}{elseif $site_description}{$site_description}{else}提供免费发布分类信息服务的网站{/if}" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    
    <link rel="stylesheet" href="/template/pc/css/common.css">
    <link rel="stylesheet" href="/template/pc/css/index.css">
    <link rel="stylesheet" href="/static/css/content-responsive.css?v=<?php echo time(); ?>">
    <script type="text/javascript" src="/template/pc/js/m.js"></script>
    
    <style>
        /* 单页专用样式 - 左右分栏布局 */
        .page-wrapper {
            display: flex;
            background: #fff;
            border-radius: 5px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
            margin-bottom: 20px;
            min-height: 600px;
        }

        /* 左侧导航 */
        .page-sidebar {
            width: 200px;
            background: #f8f9fa;
            border-right: 1px solid #eee;
            border-radius: 5px 0 0 5px;
        }

        .sidebar-header {
            padding: 20px 15px;
            border-bottom: 1px solid #eee;
            background: #e9ecef;
            font-weight: 600;
            color: #495057;
            font-size: 16px;
        }

        .sidebar-menu {
            list-style: none;
            margin: 0;
            padding: 0;
        }

        .sidebar-menu li {
            border-bottom: 1px solid #eee;
        }

        .sidebar-menu li:last-child {
            border-bottom: none;
        }

        .sidebar-menu a {
            display: block;
            padding: 15px 20px;
            color: #666;
            text-decoration: none;
            transition: all 0.3s ease;
            font-size: 14px;
        }

        .sidebar-menu a:hover {
            background: #e9ecef;
            color: #333;
            padding-left: 25px;
        }

        .sidebar-menu a.active {
            background: #007bff;
            color: #fff;
            border-left: 4px solid #0056b3;
        }

        .sidebar-menu a.active:hover {
            background: #0056b3;
            color: #fff;
        }

        /* 右侧内容区 */
        .page-main {
            flex: 1;
            display: flex;
            flex-direction: column;
        }

        .page-header {
            padding: 20px 30px;
            border-bottom: 1px solid #eee;
            background: #f8f9fa;
        }

        .page-title {
            font-size: 24px;
            color: #333;
            margin: 0;
            font-weight: 600;
            line-height: 1.3;
        }

        .page-meta {
            margin-top: 8px;
            color: #666;
            font-size: 13px;
        }

        .page-content {
            flex: 1;
            padding: 30px;
            line-height: 1.8;
            font-size: 15px;
            color: #333;
            overflow-y: auto;
        }
        
        .page-content h1,
        .page-content h2,
        .page-content h3,
        .page-content h4,
        .page-content h5,
        .page-content h6 {
            color: #2c3e50;
            margin-top: 25px;
            margin-bottom: 12px;
            font-weight: 600;
        }

        .page-content h1 {
            font-size: 20px;
            border-bottom: 2px solid #007bff;
            padding-bottom: 8px;
        }

        .page-content h2 {
            font-size: 18px;
            border-left: 4px solid #007bff;
            padding-left: 12px;
        }

        .page-content h3 {
            font-size: 16px;
        }

        .page-content p {
            margin-bottom: 12px;
            line-height: 1.7;
        }

        /* 只对没有内联text-align样式的段落应用justify */
        .page-content p:not([style*="text-align"]) {
            text-align: justify;
        }
        
        .page-content ul,
        .page-content ol {
            margin: 15px 0;
            padding-left: 30px;
        }
        
        .page-content li {
            margin-bottom: 8px;
        }
        
        .page-content img {
            max-width: 100%;
            height: auto;
            border-radius: 5px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            margin: 15px 0;
        }
        
        .page-content blockquote {
            background: #f8f9fa;
            border-left: 4px solid #3498db;
            margin: 20px 0;
            padding: 15px 20px;
            font-style: italic;
            color: #666;
        }
        
        .page-content table {
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
            border: 1px solid #ddd;
        }
        
        .page-content table th,
        .page-content table td {
            padding: 12px;
            text-align: left;
            border-bottom: 1px solid #ddd;
        }
        
        .page-content table th {
            background-color: #f8f9fa;
            font-weight: 600;
            color: #333;
        }
        
        .page-content table tr:hover {
            background-color: #f5f5f5;
        }
        
        .page-content code {
            background: #f4f4f4;
            padding: 2px 6px;
            border-radius: 3px;
            font-family: 'Courier New', monospace;
            font-size: 14px;
            color: #e74c3c;
        }
        
        .page-content pre {
            background: #f4f4f4;
            padding: 15px;
            border-radius: 5px;
            overflow-x: auto;
            margin: 15px 0;
        }
        
        .page-content pre code {
            background: none;
            padding: 0;
            color: #333;
        }
        


        /* 响应式设计 */
        @media (max-width: 768px) {
            .page-wrapper {
                flex-direction: column;
            }

            .page-sidebar {
                width: 100%;
                border-right: none;
                border-bottom: 1px solid #eee;
                border-radius: 5px 5px 0 0;
            }

            .sidebar-menu {
                display: flex;
                overflow-x: auto;
            }

            .sidebar-menu li {
                border-bottom: none;
                border-right: 1px solid #eee;
                white-space: nowrap;
            }

            .sidebar-menu li:last-child {
                border-right: none;
            }

            .sidebar-menu a {
                padding: 12px 15px;
            }

            .page-header,
            .page-content,
            .page-actions {
                padding: 15px;
            }

            .page-title {
                font-size: 20px;
            }

            .page-content {
                font-size: 14px;
            }

            .page-content h1 {
                font-size: 18px;
            }

            .page-content h2 {
                font-size: 16px;
            }

            .page-content h3 {
                font-size: 15px;
            }
        }
    </style>
</head>
<body>
    {include file="header.htm"}
    
    <!-- 广告栏 -->
    <div class="mo-box yui-1200">
        <a href="https://www.botou.net/aboutus/tuiguang.html" target="_blank" title="泊头生活网广告招商">
            <img src="/template/pc/images/2022112309104176804697_1240_90.png" alt="广告位">
        </a>
    </div>
    
    <div class="yui-clear"></div>
    
    <!-- 主内容区域 -->
    <div class="yui-content yui-1200">
        <div class="page-wrapper">
            <!-- 左侧导航 -->
            <div class="page-sidebar">
                <div class="sidebar-header">网站介绍</div>
                <ul class="sidebar-menu">
                    {loop $nav_pages $nav_page}
                    <li>
                        <a href="{$nav_page.url}"{if $nav_page.is_current} class="active"{/if}>
                            {$nav_page.title}
                        </a>
                    </li>
                    {/loop}
                </ul>
            </div>

            <!-- 右侧主内容 -->
            <div class="page-main">
                <!-- 页面头部 -->
                <div class="page-header">
                    <h1 class="page-title">{$title}</h1>
                    <div class="page-meta">
                        <span><i class="fas fa-clock"></i> 更新时间：{php}echo date('Y-m-d H:i', $page['updated_at']);{/php}</span>
                    </div>
                </div>

                <!-- 页面内容 -->
                <div class="page-content">
                    <?php echo $content; ?>
                </div>
            </div>
        </div>
    </div>
    
    {include file="footer.htm"}
    
    <script>
    // 页面加载完成后的处理
    document.addEventListener('DOMContentLoaded', function() {
        // 为外部链接添加新窗口打开
        const links = document.querySelectorAll('.page-content a[href^="http"]');
        links.forEach(function(link) {
            if (!link.hostname || link.hostname !== window.location.hostname) {
                link.target = '_blank';
                link.rel = 'noopener noreferrer';
            }
        });
        
        // 图片点击放大功能
        const images = document.querySelectorAll('.page-content img');
        images.forEach(function(img) {
            img.style.cursor = 'pointer';
            img.addEventListener('click', function() {
                const overlay = document.createElement('div');
                overlay.style.cssText = `
                    position: fixed;
                    top: 0;
                    left: 0;
                    width: 100%;
                    height: 100%;
                    background: rgba(0,0,0,0.8);
                    display: flex;
                    justify-content: center;
                    align-items: center;
                    z-index: 9999;
                    cursor: pointer;
                `;
                
                const enlargedImg = document.createElement('img');
                enlargedImg.src = this.src;
                enlargedImg.style.cssText = `
                    max-width: 90%;
                    max-height: 90%;
                    border-radius: 5px;
                    box-shadow: 0 4px 20px rgba(0,0,0,0.5);
                `;
                
                overlay.appendChild(enlargedImg);
                document.body.appendChild(overlay);
                
                overlay.addEventListener('click', function() {
                    document.body.removeChild(overlay);
                });
            });
        });
    });
    </script>
</body>
</html>
