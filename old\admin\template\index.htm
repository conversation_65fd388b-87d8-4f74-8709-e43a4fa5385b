{include file="header.htm"}

<div class="section">
    <h2 class="section-title">数据概览</h2>

    <div class="stats-grid">
        <div class="stat-card">
            <div class="stat-icon" style="background-color: var(--primary-color);">
                <i class="fas fa-file-alt"></i>
            </div>
            <div class="stat-title">信息总数</div>
            <div class="stat-value">{$stats.info_total|number_format}</div>
            <div class="stat-trend trend-up">
                <i class="fas fa-arrow-up"></i>
                <span>5.3%</span>
            </div>
        </div>

        <div class="stat-card">
            <div class="stat-icon" style="background-color: var(--success-color);">
                <i class="fas fa-users"></i>
            </div>
            <div class="stat-title">用户总数</div>
            <div class="stat-value">{$stats.user_total|number_format}</div>
            <div class="stat-trend trend-up">
                <i class="fas fa-arrow-up"></i>
                <span>3.2%</span>
            </div>
        </div>

        <div class="stat-card">
            <div class="stat-icon" style="background-color: var(--warning-color);">
                <i class="fas fa-calendar-day"></i>
            </div>
            <div class="stat-title">今日发布</div>
            <div class="stat-value">{$stats.today_info|number_format}</div>
            <div class="stat-trend trend-up">
                <i class="fas fa-arrow-up"></i>
                <span>2.1%</span>
            </div>
        </div>

        <a href="info.php?status=0" class="stat-card">
            <div class="stat-icon" style="background-color: var(--danger-color);">
                <i class="fas fa-hourglass-half"></i>
            </div>
            <div class="stat-title">待审核</div>
            <div class="stat-value">{$stats.pending_info|number_format}</div>
            <div class="stat-trend trend-down">
                <i class="fas fa-arrow-down"></i>
                <span>1.5%</span>
            </div>
        </a>

        <a href="report.php?status=0" class="stat-card">
            <div class="stat-icon" style="background-color: var(--warning-color);">
                <i class="fas fa-flag"></i>
            </div>
            <div class="stat-title">待处理举报</div>
            <div class="stat-value">{$stats.pending_reports|number_format}</div>
            <div class="stat-trend">
                <i class="fas fa-exclamation-triangle"></i>
                <span>需关注</span>
            </div>
        </a>
    </div>
</div>

<div class="section">
    <div class="card">
        <h3 class="card-title">系统信息</h3>
        <table class="table">
            <tr>
                <td width="30%">操作系统</td>
                <td>{$system_info.os}</td>
            </tr>
            <tr>
                <td>PHP 版本</td>
                <td>{$system_info.php_version}</td>
            </tr>
            <tr>
                <td>MySQL 版本</td>
                <td>{$system_info.mysql_version}</td>
            </tr>
            <tr>
                <td>Web 服务器</td>
                <td>{$system_info.server_software}</td>
            </tr>
            <tr>
                <td>上传文件大小限制</td>
                <td>{$system_info.upload_max_filesize}</td>
            </tr>
            <tr>
                <td>脚本执行时间限制</td>
                <td>{$system_info.max_execution_time}</td>
            </tr>
            <tr>
                <td>内存限制</td>
                <td>{$system_info.memory_limit}</td>
            </tr>
        </table>
    </div>
</div>

<div class="section">
    <div class="card">
        <h3 class="card-title">管理员信息</h3>
        <div class="alert alert-info">
            <i class="fas fa-info-circle"></i>
            <div>
                <p>欢迎回来，{$admin.username}！您上次登录时间：{$admin.last_login}</p>
            </div>
        </div>

        <div class="d-flex gap-3 mt-3">
            <a href="cache_manager.php?action=warmup_cache" class="btn btn-primary">
                <i class="fas fa-sync"></i>
                <span>预热缓存</span>
            </a>
            <a href="cache_manager.php?action=clear_all" class="btn btn-warning">
                <i class="fas fa-trash"></i>
                <span>清除缓存</span>
            </a>
            <a href="logout.php" class="btn btn-outline">
                <i class="fas fa-sign-out-alt"></i>
                <span>退出登录</span>
            </a>
        </div>
    </div>
</div>

{include file="footer.htm"} 