#UEditor Change List
##1.5.0
###bug修复&优化改进
1.修复对嵌套的span标签设置样式时，被嵌套的标签不能正确设置上给定的样式

##1.4.4
###bug修复&优化改进
1. 修复展示页中li没有加前置选择器，可能导致影响展示页中li的css问题
2. 产出的数据中的color自动从rgb格式变成16进制格式
3. 过滤掉word中粘贴过来的a标签被误命中为锚点
4. 扩展的UI组件需要配置toolbars参数才可显示
5. ASP 创建目录 BUG 修复
6. JSP版本修复默认上传路径错误的bug。
7. 添加链接前缀的白名单，白名单里的前缀不加 http://

##1.4.3
###bug修复&优化改进
1. 修复hasContents接口在非ie下只有空格时判断还为真的问题
2. 修复在粘贴word内容时，会误命中cm,pt这样的文本内容变成px的问题
3. 优化删除编辑器再创建编辑器时，编辑器的容器id发生变化的问题
4. 修复提交jsonp请求时，callback参数的xss漏洞
5. 新增jsp后台多种服务器配置下的路径定位
6. 修复ZeroClipboard的flash地址参数名称错误
7. 修复getActionUrl的bug
8. 整理配置参数，把遗漏在代码中的配置项整理到ueditor.config.js里
9. 修复图片拉伸工具和编辑拉伸长高器的样式冲突
10. 修复文字的unicode编码会被错误再次解析问题
11. 添加消息提示功能，冒泡提示信息
12. 优化上传功能提示，当后端配置项没正常加载，禁用上传功能
13. 修复单图上传按钮和jqueryValidate不兼容的问题
14. 简化了与jqueryValidate的结合操作，具体看_examples/jqueryValidateDemo.html
15. 修复在删除编辑器后，再次创建时丢失原有id的问题
16. 修复查找替换在一些块节点中会导致替换错误

##1.4.2
###重构前后端交互功能
1. 前端上传模块统一改用webuploader
2. 整体重构了文件上传的配置方式，改为统一在后端配置，前后端自动打通，[详细文档]()
3. 统一各上传模块的提交地址，各模块通过action参数区分类型，[详细文档]()
4. 提供serverparam命令，可在提交时追加任意参数，[详细文档]()
5. 统一了前端各上传模块的布局样式
6. 支持了在线附件预览和插入
7. 统一了后端返回数据格式，[详细文档]()
8. 各在线预览列表模块支持分组加载
9. 增加点击直接选择文件上传图片插件
10. 优化了粘贴图片的功能，上传时有loading和出错的提示
11. 添加jsonp的跨域请求支持

###bug修复&优化改进
1. 修复内容过多时，回到顶部，顶部内容被工具栏遮挡问题
2. 修复htmlparser解析标签的bug
3. 修复锚点是#开头时还添加http://的bug
4. 修复全屏后，退出全屏高度没有缩回来的问题
5. 文字选中后按delete删除，无法触发contentchange事件
6. 修复选择图片时点击8个控制点不拖动，contentchange会误触发的问题
7. 修复执行命令会触发多次contentchange事件
8. 修复输入文字，设置高度300，没有滚动条
9. 修复在不可编辑模式下，链接认可修改的问题
10. 修复全局css对于ueditor有时展现会出现按钮独占一行的问题
11. 修复在ie11下上传图片失败的问题
12. 修复chrome 34版本下报错问题
13. 修复ie8下插入多张远程图片之后，对话框假死问题
14. 修复uparse,在页面中多次调用某些模块无效果问题
15. 修复容器宽度100%时,改变窗口大小,宽度不自适应的问题
16. 将桥接ui和编辑器的业务代码放到了新建的adapter目录，方便维护
17. 修复拖拽改变图片大小功能和bootstrap不兼容的问题
18. 修复在表格水平边框上拖拽，不能改变大小的问题
19. 修复在表格标题上可以向左向下合并单元格的问题
20. 修复grunt打包出错的问题
21. 优化ie11下的兼容问题，主要修复了表格下的诸多问题
22. 优化插入图片，添加原文件名作为alt属性

###新功能添加
1. 添加了enableContextMenu配置开关，开关右键菜单，默认为true
2. 添加disablePInList配置，指定产出的列表中是否嵌套P标签，默认是false
3. 添加retainOnlyLabelPasted配置，指定粘贴时是否是只保留标签模式，默认是false
4. 优化了添加toolbar上各类ui的方式，方便二次开发
6. 优化有时产出数据会带有 bookmark标签的脏数据问题
7. 添加LocalStorage本地存储工具
8. 优化自动排版功能，选项保存到localStorage或UserData
9. 添加右键菜单的复制粘贴的支持，非ie不提供粘贴功能

##1.3.6

###bug修复
1.	script/style标签内容，在ie下的编辑状态显示内容问题
2.	修复预览窗口没有滚动条问题 [出自](http://www.ueditorbbs.com/forum.php?mod=viewthread&tid=28231&extra=page%3D1)
3.	修复在ie67下自动寻址的问题
4.	修复ff下图片拖拽大小不触发contentchange问题
5.	修复注释被删除的问题，包括在script中的[出自](http://www.ueditorbbs.com/forum.php?mod=viewthread&tid=12509&extra=page%3D1)
6.	修复在源码模式下不能使用setContent的问题，[出自](http://www.ueditorbbs.com/forum.php?mod=viewthread&tid=26910&extra=page%3D1)
7.	修复在给定宽度为100%时，ie11在拖动窗口大小时，编辑区域不随着变化的问题
8.	修复在ie67下destroy方法调用报错
9.	修复在插入代码模式下，ie9+以上的浏览器键盘操作(enter/tab等)错误或者失效的问题
10.	修复不规则源码在ie下解析成文本的问题
11.	修复p标签在ie下嵌套的问题
12. 修复目录大纲更新事件导致失焦点的问题
13. 针对word粘贴列表到编辑器中自动转换为list标签，由于有误命中情况，所以默认关闭该功能。提供**autoTransWordToList**配置项(默认为false)，开发者可酌情配置。
14. 添加禁止表格嵌套的开关，解决excel中粘贴到编辑器中会有冗余的嵌套表格问题，配置项**disabledTableInTable**(默认是true)。
15. 过滤掉excel的表格粘贴到ie中时，会有bitmap的冗余占位图片问题

###功能更新
1.	支持视频上传插入，多浏览器自适配播放器

#1.3.5

###新增功能
1.	asp后台的支持
2.	添加本地自动保存功能
3.	增加数据可视化展示功能
4.	编辑器实例上添加isFocus,blur方法
5.	新增在chrome下针对图片的拖拽宽高功能
6.	新增在高端浏览器下，qq截图粘贴上传，拖拽图片到编辑上传
7.	添加表格插入列标题功能
8.	添加设置表格可排序功能，支持表格在预览页排序
9.  添加生成目录功能

###已有功能优化
1.	ie8以上版本使用w3cRange
2.	使用grunt作为打包工具
3.	修复了过滤规则对于script/style的内容的转码
5.	自动寻址功能重构
6.	修复下拉菜单高度问题
7.	针对ie默认带有的autolink功能，添加开启禁用选项，创建时传入autolink:false就可禁用ie的autolink功能
8.	支持插入动态地图
9.	**API文档更新**
10.	图片上传路径可配置，增加前后端路径验证
11.	对uparse进行了拆分重构
12.	随下载包提供各种功能说明文档
13. 背景颜色功能重构，可以在预览页显示背景
14. 重写了查找替换插件，解决ff下window.find方法失效的问题


##*******
1.	查找替换支持正则表达式
2.	修复了ie67下初始化宽高给定百分比
3.	增加类似word中的快捷菜单，默认关闭
4.	针对默认过滤回转换div为p标签，提供了配置开关allowDivTransToP,默认为true
5.	修复了在ie下删除分割线后光标定位的问题
6.	提供了手动加载语言文件，避免ie下有时会因语言文件加载失败导致编辑器加载失败,提示"not import language file"的错误
7.	优化了编辑器初始化时获得contentWindow可能不存在的情况
8.	优化了编辑器加载自定义样式的问题，默认initialStyle传入的css样式优先级最高，其次是指定的外部css文件
9.	工具栏支持指定位置折行，'|'表示分割符,'||'表示折行
10.	表格操作功能升级，优化了对表格的拖拉及双击操作，并且支持IE6+浏览器。
11.	修复编辑器在禁用状态下仍然可以拖动表格边框的bug。
12.	修复了分割线不能删除的问题
13.	修复了初始化内容过多时，编辑器不自动长高，要点击编辑器才会长高的问题
14.	优化了添加字符边框的展示效果，避免出现重叠的问题
15.	修复下拉菜单超出屏幕的bug
16.	修复table属性初始化时table布局错误的bug
17.	优化了选择工具栏上下拉菜单类型的操作命令时，选区会有闪动的问题
18.	优化了关于swfupload的一个xss漏洞
19.	优化了对于ie9,10的支持


##1.2.6
1.	优化了编辑器路径的设置，可以不用手动设置路径，自动识别相关路径，解决路径设置繁琐的问题
2.	重写了过滤粘贴机制，采用黑白名单，可以书写符合自己需求的过滤规则，可以完全定义标签的属性，甚至是style上的某个属性及其数值
3.	优化了拖拽机制，处理浮动图片拖拽不能跟指定的某行对齐
4.	数据同步改为失去焦点就执行，可以不再使用sync方法手动同步数据
5.	添加了字体边框
6.	优化了backspace/del键的操作
7.	重写了插入代码功能，插入代码编写支持tab和回车键
8.	表格支持排序和隔行显示
9.	改使用closure的压缩工具
10.	优化了undo/redo操作
11.	优化了ui界面


##1.2.5
###新增功能
1.	table整体重构
2.	table支持插入表头和标题
3.	table支持拷贝
4.	table支持任意调整宽高
5.	table支持任意前插后插行列
6.	table键盘操作仿word用户体验
7.	添加table平均分布行、列
8.	添加table单元格对齐方式
9.	添加table对齐方式
10.	添加选中部分表格,点击backspace或delete删除功能
11.	重写表格属性、单元格属性dialog
12.	粘贴支持纯文本,源码,纯标签3个模式选择
13.	添加计算字数的getContentLength接口
14.	添加计算字数事件wordcount
15.	图片上传支持参数动态绑定
16.	重写了list功能，支持一,一),(一),1),(1),——等新的列表标签
17.	调整了list中tab键的逻辑
18.	添加了可以限制列表的层级
19.	全屏快捷键 ALT+Z
20.	添加了uparseplugin.js展示页加载器

###优化修复
1.	优化了插入代码功能
2.	ie下默认禁用源码模式下的代码高亮
3.	截图功能支持非ie浏览器
4.	修正了非ie下中文输入时回退不准确的问题
5.	改进了键盘输入时做回退的操作

##1.2.4
###新增功能
1.	官网新增API文档
2. CSS按照UI结构进行了模块化拆分
3. 新增皮肤切换功能，并提供一套新皮肤（可通过配置项theme来设置）
4. 新增编辑器容器拖动缩放功能,配置项为:scaleEnabled、minFrameWidth、minFrameHeight
5. 新增音乐插件
6. 增加了源码模式下,全屏按钮可以使用
7. 添加了UE.getEditor工厂方法
8. 添加了针对jquery配合使用的demo
9. 添加了针对jqueryValidation配合使用的demo
10. 添加了初始化编辑器宽高配置，配置为项:initialFrameWidth、initialFrameHeight

###优化修复
1. 修复涂鸦路径在配置时，添加参数时请求报错
2. 修复涂鸦opera下缩放不能使用
3. 修复编辑器全屏功能失效问题
4. codemirror版本升级到最新版
5. 对opera/safari的支持进行了进一步的优化
6. 优化了部分demo页的代码
7. 修改原来的minFrameHeight为拖动时的最小高度

##1.2.3
###新增功能
1. 新增国际化支持
2. 新增涂鸦功能
3. 新增大小写功能
4. 新增getAllHtml方法，可以将整个页面的内容打出来,可以在ueditor.configplugin.js里通过配置allHtmlEnabled,来配置在提交时是否使用getAllHtml来得到内容
5. 新增插入模板的功能
6. 新增背景功能
7. 新增UE.instants全局对象，下边挂接了所有实例化的编辑器对象
8. Editor下新增ready方法，当编辑器ready后执行传入的fn,如果编辑器已经ready好了，就马上执行fn
9. 新增topOffset配置参数，用于设置AutoFloat时工具栏距离顶部的高度
10. 新增sourceEditorFirst配置参数，用于控制编辑器初始化时是否显示成源码模式，默认为否
11. 新增在表格内实例化编辑器的demo
12. 新增getDialog(dialogName)接口，可以获取dialog对象。

###优化修复
1. chrome下会出现alt+tab切换时，导致选区位置不对
2. focus方法添加参数可以指向到内容末尾
3. 完全支持opera浏览器
4. 修复了表格中实例化编辑器时工具栏浮动错位问题
5. 优化了后台处理文件代码,文件夹按照日期自动生成


##1.2.2
1.	编辑器不可编辑时，可以配置哪些功能可以使用，例如全屏
2.	table的边框为0时，采用虚线显示
3.	修复firefox下插入大量代码时，代码格式显示不正确的问题
4.	附件上传成功后显示初始文件名
5.	自定制下载优化
6.	当图片上传超时时，增加提示信息
7.	修复自动排版对H1不生效的问题
8.	修复插入超链接，超链接地址包含script标签，预览时会执行script语句的问题


##1.2.1
1.	插入表情时，按住CTRL键可连续插入多个表情
2.	按住CTRL+Enter提交表单
3.	增加readonly属性在ueditor.configplugin.js，编辑器实例上增加setEnabled,setDisabled方法，设置编辑区域是否可以编辑
4.	Editor上添加了getPlainTxt方法，得到编辑器的纯文本内容，但会保留段落格式
5.	修正了initialContent赋值失效的问题，赋值顺序以标签内容为先，如果没有再看initialContent内容。
6.	为insertHtml命令添加了过滤机制
7.	getContent将“&nbsp;”转成空格，连续2个空格则以“ &nbsp;”表示
8.	当选区在一个超链接中，就可以在弹出层中直接修改这个超链接中的文本
9.	与后台交互的路径整体进行了调整
10.	超链接窗口可以修改超链接显示的文字
11.	增加插入百度应用的功能
12.	为每个plugin的在代码中添加了配置项的容错代码，若配置项不存在，不会报错
13.	提供后台的jsp版本
14.	重写了ui和和编辑器的交互层，dialog改为显示时创建，整体代码减少22k
15.	修正了代码高亮跟jquery冲突的问题
16.	改进了多个编辑器实例，使用一个name做为form提交，后台都可以取到
17.	添加是否删除空的inlineElement节点（包括嵌套的情况）的配置项：autoClearEmptyNode
18.	修正了chrome下粘贴文本带有white-space样式， 导致编辑器内容不能折行的问题
19.	在配置项中增加isShow设置初始化时是否显示编辑器，在编辑器实例上增加setShow,setHide方法设置编辑器的显示/隐藏
20.	修正在jquery中实例化编辑器时与UE自带的domready冲突的问题
21.	修正代码高亮中的行号与代码内容不能对齐的问题
22.	新增了图片上传对话框中可自定义配置默认Tab的功能
23.	修正.net源码包中gbk版本的乱码以及demo中使用了php路径的问题

##1.2.0
1.	远程图片抓取
2.	源码模式下css进行了简写
3.	增强了表格的编辑功能
4.	增加了baidu图片搜索功能，搜索图片然后直接插入到编辑器中
5.	重写了浮动工具栏，支持混乱模式下的工具栏滚动
6.	服务器图片在线管理
7.	word的本地图片取得寛高
8.	附件上传
9.	自动排版
10.	优化了状态反射的方式，改为编辑器获得焦点才会触发，失去焦点不在触发状态查询
11.	添加了上来就可以全屏的配置项哦去焦点之前的选区
13.	优化了查询状态反射的性能
14.	添加了contentchagne事件
15.	重写了autoheight插件，去掉setInterval的方式，并且长高时不在跳动
16.	插入视频，可以预览，并且界面加入了视屏搜索功能，并且可以插入视屏预览图到编辑器中
17.	单元格属性编辑
18.	ie下的截屏功能
19.	加强了table的dialog功能
20.	改进了autolink的效果，例如: dddhttp://www.baidu.com 回车,http://www.baidu.com也可以被匹配到了
21.	文件上传提供flash源码
22.	修改了行间距的展示方式
23.	段间距变为段前距和段后距
24.	提供了.net的事例代码
25.	首页提供了功能选择生成下载的新功能
26.	首页文档进行了改进
27.	分页符可以删除

##1.1.8
1.	避免了重复加载源码高亮的核心代码
2.	修复了word粘贴table过滤出错问题
3.	修复插入地图会出现style="undefined"的问题
4.	优化了list，多个相邻的属性一直的list会合并
5.	可以在列表中的一行里产生多行的效果（通过回车再回退操作），类似office的效果
6.	添加自定义样式功能
7.	修了在chrome下右键删除td里的图片会把整个td删除的问题
8.	改进了不同的页面调用一个editor，URL问题
9.	增加了颜色选择器的颜色
10.	改进了提供的后台程序的安全性
11.	代码高亮支持折行
12.	改进了源码编辑模式下的性能(ie下),并且支持自动换行
13.	修改了在destroy之后会在ie下报错的问题
14.	给初始化容器name值，那么在后台取值的键值就是name给定的值，方便多实例在一个form下提交
15.	支持插入script/style这样的标签
16.	修复了列表里插入浮动图片，图片不占位问题
17.	源码模式下,去掉了pre中的&nbsp;
18.	完善了_example下的demo例子
19.	base64的图片被过滤掉了

##*******
1.	支持图片相对路径模式
2.	word粘贴首行缩进问题
3.	添加了图片边距
4.	提供了图片等比压缩时基准边选择配置的功能
5.	dialog在某些页面不显示问题
6.	添加了行内间距的调整
7.	在editor实例下添加了destroy方法
8.	全屏按钮位置不对的问题
9.	iframe.css支持相对和绝对路径
10.	修正了focus方法在ff下失效的问题
11.	提供了对FF3.6的支持
12.	添加了Shift+Enter软回车功能
10.	统一了颜色rgb转成#

##*******
1.	去掉了iframe.css 改为在ueditor.configplugin.js中配置，避免css文件找不到的问题
2.	给下拉菜单添加了默认的文字说明
3.	Ueditor.css去掉了对外部页面css的影响
4.	修正了ie9下，编辑器的高度不随着内容缩短的问题
5.	修正了粘贴有时会出现粘贴失败的情况
6.	修正了在ie下点击图片会出现js错误的问题
7.	修正了在ie下选全部替换，回退，再替换会出现替换失败的问题
8.	增加表情本地化模式，可在config中配置是否开启本地化
9.	flash的多图片上传
10.	支持了源码模式的下的代码高亮
11.	增加插入代码支持的语言，改进了插入代码的展示效果
12.	增加了字数统计
13.	增加了对图片的排版操作
14.	解决ie6和ie7下工具栏浮动时cpu占用过高的bug
15.	优化了文本模式粘贴的效果
16.	优化了word粘贴的效果
17.	在word粘贴本地图片时添加引导上传功能
18.	更好的ie9支持
19.	优化首行缩进效果
20.	使用script标签代替textarea标签作为编辑器容器，简化前后端转码的配置。
21.	优化了路径配置，修正了*******中需要修改多处路径的问题
22.	增加了图片操作浮层的开关配置
23.	同时支持网络图片和本地图片的等比缩放
24.	优化了源码模式下的代码格式

##*******
1.	去掉了iframe.css 改为在ueditor.configplugin.js中配置，避免css文件找不到的问题
2.	给下拉菜单添加了默认的文字说明
3.	Ueditor.css去掉了对外部页面css的影响
4.	修正了ie9下，编辑器的高度不随着内容缩短的问题
5.	修正了粘贴有时会出现粘贴失败的情况
6.	修正了在ie下点击图片会出现js错误的问题
7.	修正了在ie下选全部替换，回退，再替换会出现替换失败的问题

##1.1.6
1. 插入日期按钮现在使用tangram日历控件
2. table可再编辑
3. 粘贴excel表格的问题
4. ff下最大化和切换源码出现光标不能跟着键盘改变和不能切出输入法的问题
5. tab按键功能
6. 支持多级列表
7. 超链接可以在非ie下去除下划线
8. 字体，字号，在editor-configplugin.js中可配置



##1.1.5
1.	右键的策略，只显示选区内可操作的条目
2.	禁止elementpath还会留下边框问题
3.	字体改为了px
4.	插入分页符
5.	整合浮动toolbar为autofloat插件
6.	初始化的值会在第一次操作前清除，而且不在有延迟感
7.	配置项都放到了editor-configplugin.js中
8.	修正了多实例的问题
9.	插入iframe功能
10.	粘贴过滤掉内容会有提示，没过滤任何内容不会出现提示
11.	修正代码高亮的显示效果
12.	list放弃原生改为手动实现，修正一系列原生的bug
13.	初始给个textarea会把内容取出作为初始值
14.	去掉了源码状态下冗余的table/td/pre的style属性
15.	fixed剪切出去会带start/end
16.	fixex源码模式下getContent内容不是新的
17.	table加入了设置背景颜色和边框颜色



##1.1.4
1.  锚点
2.  首行缩进
3.  行间距
4.  右键菜单
5.  插入代码
6.  文件上传(php版本）
7.  修复一些bug

##1.1.3
1.  修复chrome下粘贴的bug
2.  自动转换office粘入的有序列表和无序列表
3.  插入图片不再等比缩放，显示原始大小

##1.1.2
1.  修正IE9下autoHeight插件会一直长高的问题
2.  增加对IE6下大写style属性的转换处理（现统一转换成小写）
3.  格式刷
4.  上下标互斥
5.  form提交的支持
6.  增加了focus属性，可以初始化时，设置是否编辑器获得焦点
7.  增加了下滑线，删除线按钮，去掉了原来的下拉框
8.  autolink支持，使非ie在输入链接时能自动加上a标签
9.  google地图支持
10. 修正了一些bug

##1.1
1.	修改了删除链接的机制，允许一次性删除多个超链接
2.	改变了目录结构，方便部署（大大减少了开发代码过程中需要引入的js数量）
3.	修正部分bug

##1.0 (2011-7-8)
1.	完成功能的开发
