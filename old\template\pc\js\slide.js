/*
 * Simple Slider functionality
 */
$.fn.slider = function() {
    var $slider = $(this);
    var $slides = $slider.find('#slider li');
    var $pagination = $('#pagination-wrap ul');
    var currentIndex = 0;
    var slideCount = $slides.length;
    var timer;

    // 初始化
    function init() {
        // 隐藏所有幻灯片
        $slides.hide();
        // 显示第一张
        $slides.eq(0).show();
        
        // 创建分页点
        createPagination();
        
        // 绑定事件
        bindEvents();
        
        // 开始自动播放
        startAutoPlay();
    }

    // 创建分页点
    function createPagination() {
        $pagination.empty();
        for (var i = 0; i < slideCount; i++) {
            $pagination.append('<li class="' + (i === 0 ? 'active' : '') + '"></li>');
        }
    }

    // 切换到指定幻灯片
    function goToSlide(index) {
        if (index === currentIndex) return;
        
        // 隐藏当前幻灯片
        $slides.eq(currentIndex).fadeOut(300);
        
        // 显示新幻灯片
        $slides.eq(index).fadeIn(300);
        
        // 更新分页点
        $pagination.find('li').removeClass('active');
        $pagination.find('li').eq(index).addClass('active');
        
        // 更新当前索引
        currentIndex = index;
    }

    // 下一张幻灯片
    function nextSlide() {
        var nextIndex = (currentIndex + 1) % slideCount;
        goToSlide(nextIndex);
    }

    // 上一张幻灯片
    function prevSlide() {
        var prevIndex = (currentIndex - 1 + slideCount) % slideCount;
        goToSlide(prevIndex);
    }

    // 开始自动播放
    function startAutoPlay() {
        stopAutoPlay();
        timer = setInterval(nextSlide, 3000);
    }

    // 停止自动播放
    function stopAutoPlay() {
        if (timer) clearInterval(timer);
    }

    // 绑定事件
    function bindEvents() {
        // 点击下一张
        $('#next').click(function(e) {
            e.preventDefault();
            nextSlide();
            startAutoPlay();
        });

        // 点击上一张
        $('#previous').click(function(e) {
            e.preventDefault();
            prevSlide();
            startAutoPlay();
        });

        // 点击分页点
        $pagination.on('click', 'li', function() {
            var index = $(this).index();
            goToSlide(index);
            startAutoPlay();
        });

        // 鼠标悬停时停止自动播放
        $slider.hover(
            function() { stopAutoPlay(); },
            function() { startAutoPlay(); }
        );
    }

    // 初始化轮播图
    init();
    
    return this;
}; 