<?php
// 简化页面 - 移除海报功能
// 开启错误显示
error_reporting(E_ALL);
ini_set('display_errors', 1);

// 定义安全常量
define('IN_BTMPS', true);

// 引入公共文件
require_once '../include/common.inc.php';

// 获取信息ID
$id = isset($_GET['id']) ? intval($_GET['id']) : 0;
if (!$id) {
    header('Location: /');
    exit;
}

// 查询信息详情
try {
    $sql = "SELECT p.*, c.name as category_name, c.pinyin as category_pinyin, r.name as region_name
            FROM posts p
            LEFT JOIN categories c ON p.category_id = c.id
            LEFT JOIN regions r ON p.region_id = r.id
            WHERE p.id = ?";
    $result = $db->query($sql, array($id));
    $post = $db->fetch_array($result);

    if (!$post) {
        echo "没有找到ID为 {$id} 的信息";
        exit;
    }
} catch (Exception $e) {
    echo "数据库查询错误: " . $e->getMessage();
    exit;
}

// 页面标题
$pageTitle = '信息详情 - ' . htmlspecialchars($post['title']);
?>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $pageTitle; ?></title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, "PingFang SC", "Hiragino Sans GB", "Microsoft YaHei", sans-serif;
            background: #f5f5f5;
            margin: 0;
            padding: 20px;
            color: #333;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            border-radius: 8px;
            padding: 30px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #2c3e50;
            margin-bottom: 20px;
            font-size: 24px;
        }
        .info {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 6px;
            margin: 20px 0;
        }
        .back-btn {
            display: inline-block;
            background: #007bff;
            color: white;
            padding: 10px 20px;
            text-decoration: none;
            border-radius: 4px;
            margin-top: 20px;
        }
        .back-btn:hover {
            background: #0056b3;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1><?php echo htmlspecialchars($post['title']); ?></h1>

        <div class="info">
            <p><strong>信息编号:</strong> <?php echo $post['id']; ?></p>
            <p><strong>分类:</strong> <?php echo htmlspecialchars($post['category_name']); ?></p>
            <p><strong>地区:</strong> <?php echo htmlspecialchars($post['region_name']); ?></p>
            <p><strong>发布时间:</strong> <?php echo date('Y-m-d H:i', strtotime($post['created_at'])); ?></p>
            <p><strong>浏览次数:</strong> <?php echo number_format($post['view_count']); ?></p>
            <?php if (!empty($post['price'])): ?>
            <p><strong>价格:</strong> ￥<?php echo number_format($post['price'], 2); ?></p>
            <?php endif; ?>
        </div>

        <div class="info">
            <h3>详细描述</h3>
            <p><?php echo nl2br(htmlspecialchars($post['content'])); ?></p>
        </div>

        <a href="/view.php?id=<?php echo $post['id']; ?>" class="back-btn">查看完整信息</a>
        <a href="/" class="back-btn">返回首页</a>
    </div>
</body>
</html>