<!DOCTYPE html>
<?php
// 获取主题设置
$theme = isset($_COOKIE['site_theme']) ? $_COOKIE['site_theme'] : 'red';
$theme_class = 'theme-' . $theme;
?>
<html lang="zh-CN" class="<?php echo $theme_class; ?>">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <title><?php echo (isset($news['title'])) ? $news['title'] : ""; ?> - <?php echo $site_name ?? ""; ?></title>
    <meta name="keywords" content="<?php if(null !== ($news ?? null) && is_array($news) && array_key_exists('keywords', $news) && !empty($news['keywords'])): ?><?php echo (isset($news['keywords'])) ? $news['keywords'] : ""; ?><?php else: ?><?php echo (isset($news['title'])) ? $news['title'] : ""; ?>,<?php echo $site_name ?? ""; ?>,新闻资讯<?php endif; ?>" />
    <meta name="description" content="<?php if(null !== ($news ?? null) && is_array($news) && array_key_exists('description', $news) && !empty($news['description'])): ?><?php echo (isset($news['description'])) ? $news['description'] : ""; ?><?php else: ?><?php echo (isset($news['title'])) ? $news['title'] : ""; ?> - <?php echo $site_name ?? ""; ?>新闻资讯<?php endif; ?>" />

    <link rel="stylesheet" href="/static/font-awesome/css/all.min.css">
    <link rel="stylesheet" href="/static/css/themes.css?v=<?php echo time(); ?>">
    <link rel="stylesheet" href="/template/m/css/common.css?v=<?php echo time(); ?>">
    <link rel="stylesheet" href="/static/css/content-responsive.css?v=<?php echo time(); ?>">
    <script src="/static/js/common.js"></script>
    <script src="/static/js/page-stabilizer.js"></script>

    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
            background: #f8f9fa;
            min-height: 100vh;
            color: #333;
            margin: 0;
            padding: 0;
        }

        /* 搜索弹出层 */
        .search-layer {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: var(--primary-color);
            z-index: 1001;
            transform: translateY(-100%);
            transition: transform 0.3s ease;
        }

        .search-layer.active {
            transform: translateY(0);
        }

        .search-header {
            display: flex;
            align-items: center;
            padding: 15px;
            height: 50px;
        }

        .search-back {
            background: none;
            border: none;
            color: white;
            font-size: 18px;
            margin-right: 15px;
            cursor: pointer;
        }

        .search-input-wrapper {
            flex: 1;
            display: flex;
            background: rgba(255,255,255,0.2);
            border-radius: 20px;
            overflow: hidden;
        }

        .search-input {
            flex: 1;
            border: none;
            background: transparent;
            color: white;
            padding: 8px 15px;
            font-size: 16px;
            outline: none;
        }

        .search-input::placeholder {
            color: rgba(255,255,255,0.7);
        }

        .search-submit {
            background: none;
            border: none;
            color: white;
            padding: 8px 15px;
            cursor: pointer;
        }

        /* 头部分享按钮样式 */
        .header-share-icon {
            color: white;
            text-decoration: none;
            font-size: 18px;
            padding: 8px;
            border-radius: 50%;
            transition: background-color 0.2s;
        }

        .header-share-icon:hover {
            background-color: rgba(255,255,255,0.1);
            color: white;
        }

        /* 文章内容 */
        .article-container {
            background: white;
        }

        .article-header {
            padding: 20px;
            border-bottom: 1px solid #f0f0f0;
        }

        .article-title {
            font-size: 22px;
            font-weight: 700;
            line-height: 1.4;
            color: #333;
            margin-bottom: 15px;
        }

        .article-meta {
            display: flex;
            align-items: center;
            justify-content: space-between;
            flex-wrap: wrap;
            gap: 10px;
            color: #666;
            font-size: 14px;
        }

        .meta-left {
            display: flex;
            align-items: center;
            gap: 15px;
            flex-wrap: wrap;
        }

        .meta-item {
            display: flex;
            align-items: center;
            gap: 5px;
        }

        .meta-item i {
            font-size: 13px;
            color: #999;
        }

        .article-tags {
            display: flex;
            gap: 8px;
        }

        .tag {
            padding: 4px 10px;
            font-size: 12px;
            border-radius: 12px;
            font-weight: 500;
            color: white;
        }

        .tag.top {
            background: linear-gradient(135deg, #ff6b6b, #ee5a52);
        }

        .tag.rec {
            background: linear-gradient(135deg, #feca57, #ff9ff3);
        }

        .article-content {
            padding: 20px;
            line-height: 1.8;
            font-size: 16px;
            color: #333;
        }

        .article-content p {
            margin-bottom: 16px;
        }

        .article-content img {
            max-width: 100%;
            height: auto;
            border-radius: 8px;
            margin: 20px 0;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        }

        .article-content p[style*="text-align: center"] img,
        .article-content p[style*="text-align:center"] img {
            margin: 20px auto;
            display: block;
        }

        .article-content h1,
        .article-content h2,
        .article-content h3,
        .article-content h4,
        .article-content h5,
        .article-content h6 {
            margin: 25px 0 15px;
            color: #333;
            font-weight: 600;
        }

        .article-content ul,
        .article-content ol {
            margin: 16px 0;
            padding-left: 20px;
        }

        .article-content li {
            margin-bottom: 8px;
        }

        .article-content blockquote {
            background: #f8f9fa;
            border-left: 4px solid #667eea;
            padding: 15px 20px;
            margin: 20px 0;
            border-radius: 0 8px 8px 0;
            font-style: italic;
        }



        /* 图片放大遮罩 */
        .image-overlay {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.9);
            z-index: 9999;
            justify-content: center;
            align-items: center;
        }

        .image-overlay img {
            max-width: 90%;
            max-height: 90%;
            object-fit: contain;
        }

        .close-btn {
            position: absolute;
            top: 20px;
            right: 20px;
            color: white;
            font-size: 24px;
            cursor: pointer;
            z-index: 10000;
        }
    </style>
</head>
<body>
    <header>
        <div class="header-inner">
            <div class="header-left">
                <a href="javascript:history.back();" class="header-back">
                    <i class="fas fa-chevron-left"></i>
                </a>
            </div>
            <div class="header-title">新闻详情</div>
            <div class="header-right">
                <a href="javascript:shareNews();" class="header-share-icon">
                    <i class="fas fa-share-alt"></i>
                </a>
            </div>
        </div>
    </header>

    <!-- 文章内容 -->
    <div class="article-container">
        <div class="article-header">
            <h1 class="article-title"><?php echo (isset($news['title'])) ? $news['title'] : ""; ?></h1>
            <div class="article-meta">
                <div class="meta-left">
                    <div class="meta-item">
                        <i class="far fa-clock"></i>
                        <span><?php echo date('Y-m-d H:i', $news['addtime']); ?></span>
                    </div>
                    <div class="meta-item">
                        <i class="far fa-eye"></i>
                        <span><?php echo (isset($news['click'])) ? $news['click'] : ""; ?></span>
                    </div>
                    <?php if($news['author']): ?>
                    <div class="meta-item">
                        <i class="far fa-user"></i>
                        <span><?php echo (isset($news['author'])) ? $news['author'] : ""; ?></span>
                    </div>
                    <?php endif; ?>
                </div>
                <div class="article-tags">
                    <?php if($news['is_top']): ?><span class="tag top">置顶</span><?php endif; ?>
                    <?php if($news['is_recommend']): ?><span class="tag rec">推荐</span><?php endif; ?>
                </div>
            </div>
        </div>

        <div class="article-content">
            <?php echo $news['content']; ?>
        </div>


    </div>

    <footer>
    <div class="container">
        <div class="footer-nav">
            <a href="/login.php">登录/注册</a>
            <a href="/fee.php">电话费用</a>
            <a href="/feedback.php">用户反馈</a>
        </div>
        <div class="theme-switcher">
            <p>主题切换</p>
            <div class="theme-dots">
                <a href="javascript:void(0);" onclick="switchTheme('red')" class="theme-dot theme-red" title="红色主题"></a>
                <a href="javascript:void(0);" onclick="switchTheme('blue')" class="theme-dot theme-blue" title="蓝色主题"></a>
                <a href="javascript:void(0);" onclick="switchTheme('green')" class="theme-dot theme-green" title="绿色主题"></a>
                <a href="javascript:void(0);" onclick="switchTheme('purple')" class="theme-dot theme-purple" title="紫色主题"></a>
                <a href="javascript:void(0);" onclick="switchTheme('orange')" class="theme-dot theme-orange" title="橙色主题"></a>
                <a href="javascript:void(0);" onclick="switchTheme('pink')" class="theme-dot theme-pink" title="粉色主题"></a>
                <a href="javascript:void(0);" onclick="switchTheme('ocean')" class="theme-dot theme-ocean" title="海洋主题"></a>
                <a href="javascript:void(0);" onclick="switchTheme('wechat')" class="theme-dot theme-wechat" title="微信风格"></a>
                <a href="javascript:void(0);" onclick="switchTheme('alipay')" class="theme-dot theme-alipay" title="支付宝风格"></a>
                <a href="javascript:void(0);" onclick="switchTheme('simple')" class="theme-dot theme-simple" title="简约主题"></a>
            </div>
        </div>
        <div class="footer-info">
            <p>京ICP证060405号</p>
            <p>客户服务热线: 10105858</p>
        </div>
    </div>
    <script>
    // 主题切换功能
    function switchTheme(theme) {
        // 设置cookie，有效期30天
        var date = new Date();
        date.setTime(date.getTime() + (30 * 24 * 60 * 60 * 1000));
        document.cookie = "site_theme=" + theme + "; expires=" + date.toUTCString() + "; path=/";
        
        // 更新页面上的主题类
        document.documentElement.className = document.documentElement.className.replace(/theme-\w+/g, '');
        document.documentElement.classList.add('theme-' + theme);
        
        // 同时更新body的主题类，确保背景色只应用于内容区域
        document.body.className = document.body.className.replace(/theme-\w+/g, '');
        document.body.classList.add('theme-' + theme);
        
        // 更新当前选中的主题点
        highlightCurrentTheme(theme);
        
        // 显示切换成功提示
        var themeNames = {
            'red': '红色',
            'blue': '蓝色',
            'green': '绿色',
            'purple': '紫色',
            'orange': '橙色',
            'pink': '粉色',
            'ocean': '海洋',
            'wechat': '微信风格',
            'alipay': '支付宝风格',
            'miui': '小米风格',
            'douyin': '抖音风格',
            'simple': '简约'
        };
        
        // 创建提示元素
        var toast = document.createElement('div');
        toast.className = 'theme-toast';
        toast.textContent = '已切换到' + themeNames[theme] + '主题';
        document.body.appendChild(toast);
        
        // 2秒后移除提示
        setTimeout(function() {
            toast.classList.add('hide');
            setTimeout(function() {
                document.body.removeChild(toast);
            }, 300);
        }, 2000);
    }
    
    // 高亮当前主题
    function highlightCurrentTheme(theme) {
        // 移除所有主题点的高亮
        var themeDots = document.querySelectorAll('.theme-dot');
        themeDots.forEach(function(dot) {
            dot.classList.remove('active');
        });
        
        // 添加当前主题的高亮
        var currentThemeDot = document.querySelector('.theme-dot.theme-' + theme);
        if (currentThemeDot) {
            currentThemeDot.classList.add('active');
        }
    }
    
    // 在页面加载时，根据当前主题高亮对应的主题点
    document.addEventListener('DOMContentLoaded', function() {
        // 获取当前主题
        var currentTheme = 'red'; // 默认主题
        var htmlClass = document.documentElement.className;
        var themeMatch = htmlClass.match(/theme-(\w+)/);
        
        if (themeMatch && themeMatch[1]) {
            currentTheme = themeMatch[1];
            
            // 确保body也具有相同的主题类
            document.body.className = document.body.className.replace(/theme-\w+/g, '');
            document.body.classList.add('theme-' + currentTheme);
        }
        
        // 高亮当前主题
        highlightCurrentTheme(currentTheme);
    });
    </script>
    <style>
    /* 主题切换样式 */
    html, body {
        min-height: 100%;
    }
    
    body {
        background-color: #f5f5f5; /* 默认背景色 */
        margin: 0;
        padding: 0;
    }
    
    /* 确保主题颜色只应用于body */
    html.theme-red, html.theme-blue, html.theme-green, 
    html.theme-purple, html.theme-orange, html.theme-pink,
    html.theme-ocean, html.theme-wechat, html.theme-alipay,
    html.theme-miui, html.theme-douyin {
        background-color: #fff; /* 重置HTML背景为白色 */
    }
    
    /* 主题背景色应用到body */
    body.theme-red { background-color: #fff5f5; }
    body.theme-blue { background-color: #f5f8ff; }
    body.theme-green { background-color: #f5fff8; }
    body.theme-purple { background-color: #f8f5ff; }
    body.theme-orange { background-color: #fff9f5; }
    body.theme-pink { background-color: #fff5f9; }
    body.theme-ocean { background-color: #f5faff; }
    body.theme-wechat { background-color: #f5fff7; }
    body.theme-alipay { background-color: #f5faff; }
    body.theme-simple { background-color: #f8f8f8; }
    
    .theme-switcher {
        text-align: center;
        margin: 15px 0;
        padding: 10px;
        background-color: rgba(255, 255, 255, 0.9);
        border-radius: 10px;
    }
    
    .theme-switcher p {
        margin: 0 0 8px 0;
        font-size: 14px;
        color: #666;
        font-weight: 500;
    }
    
    .theme-dots {
        display: flex;
        flex-wrap: wrap;
        justify-content: center;
        gap: 12px;
        padding: 5px;
    }
    
    .theme-dot {
        display: inline-block;
        width: 22px;
        height: 22px;
        border-radius: 50%;
        cursor: pointer;
        transition: transform 0.2s, box-shadow 0.2s;
        border: 2px solid #fff;
        box-shadow: 0 1px 3px rgba(0,0,0,0.1);
    }
    
    .theme-dot:hover {
        transform: scale(1.2);
        box-shadow: 0 2px 5px rgba(0,0,0,0.2);
    }
    
    .theme-dot.active {
        transform: scale(1.2);
        box-shadow: 0 0 0 2px #fff, 0 0 0 4px var(--primary-color, currentColor);
    }
    
    /* 主题颜色 */
    .theme-red {
        background-color: #e53935;
    }
    
    .theme-blue {
        background-color: #4285f4;
    }
    
    .theme-green {
        background-color: #00a878;
    }
    
    .theme-purple {
        background-color: #7b68ee;
    }
    
    .theme-orange {
        background-color: #ff6b01;
    }
    
    .theme-pink {
        background-color: #e91e63;
    }
    
    .theme-ocean {
        background-color: #006994;
    }
    
    .theme-wechat {
        background-color: #07c160;
    }
    
    .theme-alipay {
        background-color: #1677ff;
    }
    
    .theme-simple {
        background-color: #ffffff;
        border: 1px solid #eeeeee;
    }
    
    /* 主题切换toast提示 */
    .theme-toast {
        position: fixed;
        bottom: 80px;
        left: 50%;
        transform: translateX(-50%);
        background-color: rgba(0, 0, 0, 0.7);
        color: white;
        padding: 10px 20px;
        border-radius: 20px;
        font-size: 14px;
        z-index: 1000;
        opacity: 1;
        transition: opacity 0.3s;
    }
    
    .theme-toast.hide {
        opacity: 0;
    }
    
    /* 简约主题特殊处理 */
    .theme-simple header {
        background-color: #ffffff !important;
        color: #333333 !important;
        border-bottom: 1px solid #eeeeee !important;
        box-shadow: 0 1px 3px rgba(0,0,0,0.05) !important;
    }
    
    .theme-simple .header-back,
    .theme-simple .header-title,
    .theme-simple .header-share,
    .theme-simple .header-search-icon {
        color: #333333 !important;
    }
    
    .theme-simple .header-back:active,
    .theme-simple .header-share:active,
    .theme-simple .header-search-icon:active {
        background-color: rgba(0,0,0,0.05) !important;
    }
    </style>
</footer>


    <!-- 底部导航栏 -->
    <!-- 移动端底部导航栏 -->
<nav class="navbar">
    <a href="/" class="nav-item <?php if($current_page == 'index'): ?>active<?php endif; ?>">
        <span class="nav-icon"><i class="fas fa-home"></i></span>
        <span class="nav-text">首页</span>
    </a>
    <a href="/category.php" class="nav-item <?php if($current_page == 'category'): ?>active<?php endif; ?>">
        <span class="nav-icon"><i class="fas fa-th-large"></i></span>
        <span class="nav-text">分类</span>
    </a>
    <a href="/post.php" class="nav-item publish <?php if($current_page == 'post'): ?>active<?php endif; ?>">
        <span class="nav-icon"><i class="fas fa-plus"></i></span>
        <span class="nav-text">发布</span>
    </a>
    <a href="/message.php" class="nav-item <?php if($current_page == 'message'): ?>active<?php endif; ?>">
        <span class="nav-icon"><i class="fas fa-comment-alt"></i></span>
        <span class="nav-text">消息</span>
    </a>
    <a href="/member/" class="nav-item <?php if($current_page == 'member'): ?>active<?php endif; ?>">
        <span class="nav-icon"><i class="fas fa-user"></i></span>
        <span class="nav-text">我的</span>
    </a>
</nav>


    <!-- 图片放大遮罩 -->
    <div class="image-overlay" id="imageOverlay">
        <div class="close-btn" onclick="closeImageOverlay()">
            <i class="fas fa-times"></i>
        </div>
        <img id="overlayImage" src="" alt="">
    </div>

    <script>
    // 页面加载完成后的处理
    document.addEventListener('DOMContentLoaded', function() {
        // 为外部链接添加新窗口打开
        const links = document.querySelectorAll('.news-content a[href^="http"]');
        links.forEach(function(link) {
            if (!link.hostname || link.hostname !== window.location.hostname) {
                link.target = '_blank';
                link.rel = 'noopener noreferrer';
            }
        });

        // 图片点击放大功能
        const images = document.querySelectorAll('.news-content img');
        images.forEach(function(img) {
            img.style.cursor = 'pointer';
            img.addEventListener('click', function() {
                showImageOverlay(this.src);
            });
        });
    });

    // 显示图片放大遮罩
    function showImageOverlay(src) {
        const overlay = document.getElementById('imageOverlay');
        const overlayImage = document.getElementById('overlayImage');
        overlayImage.src = src;
        overlay.style.display = 'flex';
        document.body.style.overflow = 'hidden';
    }

    // 关闭图片放大遮罩
    function closeImageOverlay() {
        const overlay = document.getElementById('imageOverlay');
        overlay.style.display = 'none';
        document.body.style.overflow = 'auto';
    }

    // 点击遮罩背景关闭
    document.getElementById('imageOverlay').addEventListener('click', function(e) {
        if (e.target === this) {
            closeImageOverlay();
        }
    });
    </script>



    <script>
    function shareNews() {
        const title = '<?php echo (isset($news['title'])) ? $news['title'] : ""; ?>';
        const url = window.location.href;

        // 检查是否支持Web Share API
        if (navigator.share) {
            navigator.share({
                title: title,
                url: url
            }).catch(err => console.log('分享失败:', err));
        } else {
            // 降级方案：复制链接到剪贴板
            if (navigator.clipboard) {
                navigator.clipboard.writeText(url).then(() => {
                    alert('链接已复制到剪贴板');
                }).catch(() => {
                    // 如果复制失败，显示链接
                    prompt('请复制以下链接:', url);
                });
            } else {
                // 最后的降级方案
                prompt('请复制以下链接:', url);
            }
        }
    }
    </script>

    <script src="/template/m/js/app-style.js"></script>
</body>
</html>
