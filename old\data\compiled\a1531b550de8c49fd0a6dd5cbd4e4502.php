<!DOCTYPE html>
<?php
// 获取主题设置
$theme = isset($_COOKIE['site_theme']) ? $_COOKIE['site_theme'] : 'red';
$theme_class = 'theme-' . $theme;
?>
<html lang="zh-CN" class="<?php echo $theme_class; ?>">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <title><?php if($page['title']): ?><?php echo (isset($page['title'])) ? $page['title'] : ""; ?> - <?php echo $site_name ?? ""; ?><?php else: ?><?php echo $site_name ?? ""; ?><?php endif; ?></title>
    <meta name="keywords" content="<?php if($page['meta_keywords']): ?><?php echo (isset($page['meta_keywords'])) ? $page['meta_keywords'] : ""; ?><?php else: ?><?php echo $site_keywords ?? ""; ?><?php endif; ?>">
    <meta name="description" content="<?php if($page['meta_description']): ?><?php echo (isset($page['meta_description'])) ? $page['meta_description'] : ""; ?><?php else: ?><?php echo $site_description ?? ""; ?><?php endif; ?>">
    
    <link rel="stylesheet" href="/static/font-awesome/css/all.min.css">
    <link rel="stylesheet" href="/static/css/themes.css?v=<?php echo time(); ?>">
    <link rel="stylesheet" href="/template/m/css/common.css?v=<?php echo time(); ?>">
    <link rel="stylesheet" href="/static/css/content-responsive.css?v=<?php echo time(); ?>">
    <script src="/static/js/common.js"></script>
    <script src="/static/js/page-stabilizer.js"></script>
    
    <style>
        /* 单页专用样式 */
        .page-container {
            background: var(--bg-color);
            margin: 0;
            min-height: calc(100vh - 120px);
            margin-top: 50px; /* 为固定头部留出空间 */
        }
        

        
        .page-content {
            padding: 15px;
            line-height: 1.6;
            font-size: 15px;
            color: var(--text-color);
            background: var(--bg-color);
            min-height: calc(100vh - 100px);
        }
        
        .page-content h1,
        .page-content h2,
        .page-content h3,
        .page-content h4,
        .page-content h5,
        .page-content h6 {
            color: var(--heading-color);
            margin: 20px 0 10px 0;
            font-weight: 600;
        }
        
        .page-content h1 {
            font-size: 20px;
            border-bottom: 2px solid var(--primary-color);
            padding-bottom: 8px;
        }
        
        .page-content h2 {
            font-size: 18px;
            border-left: 3px solid var(--primary-color);
            padding-left: 10px;
        }
        
        .page-content h3 {
            font-size: 16px;
        }
        
        .page-content p {
            margin-bottom: 12px;
            text-align: justify;
        }
        
        .page-content ul,
        .page-content ol {
            margin: 12px 0;
            padding-left: 20px;
        }
        
        .page-content li {
            margin-bottom: 6px;
        }
        
        .page-content img {
            max-width: 100%;
            height: auto;
            border-radius: 4px;
            margin: 10px auto;
            display: block;
        }

        /* 保持段落中图片的居中对齐 */
        .page-content p[style*="text-align: center"] img,
        .page-content p[style*="text-align:center"] img {
            margin: 10px auto;
            display: block;
        }

        /* 支持内联样式的居中 - 不覆盖内联样式 */
        .page-content p:not([style*="text-align"]) {
            text-align: justify;
        }
        
        .page-content blockquote {
            background: var(--card-bg);
            border-left: 3px solid var(--primary-color);
            margin: 15px 0;
            padding: 10px 15px;
            font-style: italic;
            color: var(--text-secondary);
            border-radius: 0 4px 4px 0;
        }
        
        .page-content table {
            width: 100%;
            border-collapse: collapse;
            margin: 15px 0;
            font-size: 14px;
            background: var(--card-bg);
            border-radius: 4px;
            overflow: hidden;
        }
        
        .page-content table th,
        .page-content table td {
            padding: 8px 10px;
            text-align: left;
            border-bottom: 1px solid var(--border-color);
        }
        
        .page-content table th {
            background: var(--primary-color);
            color: white;
            font-weight: 600;
        }
        
        .page-content code {
            background: var(--code-bg);
            padding: 2px 4px;
            border-radius: 3px;
            font-family: 'Courier New', monospace;
            font-size: 13px;
            color: var(--code-color);
        }
        
        .page-content pre {
            background: var(--code-bg);
            padding: 12px;
            border-radius: 4px;
            overflow-x: auto;
            margin: 12px 0;
            font-size: 13px;
        }
        
        .page-content pre code {
            background: none;
            padding: 0;
            color: var(--text-color);
        }
        

        
        /* 简约主题样式 */
        html.theme-simple .page-header {
            background: #ffffff;
            color: #333333;
            border-bottom: 1px solid #eeeeee;
        }
        
        html.theme-simple .page-title {
            color: #333333;
        }
        
        html.theme-simple .page-meta {
            color: #666666;
        }
        
        /* 图片点击放大遮罩 */
        .image-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0,0,0,0.9);
            display: flex;
            justify-content: center;
            align-items: center;
            z-index: 9999;
            opacity: 0;
            visibility: hidden;
            transition: all 0.3s;
        }
        
        .image-overlay.active {
            opacity: 1;
            visibility: visible;
        }
        
        .image-overlay img {
            max-width: 90%;
            max-height: 90%;
            border-radius: 4px;
            transform: scale(0.8);
            transition: transform 0.3s;
        }
        
        .image-overlay.active img {
            transform: scale(1);
        }
        
        .image-overlay .close-btn {
            position: absolute;
            top: 20px;
            right: 20px;
            color: white;
            font-size: 24px;
            cursor: pointer;
            z-index: 10000;
        }
    </style>
</head>
<body>
    <!-- 移动端头部 -->
<header class="mobile-header">
    <div class="header-inner">
        <a href="javascript:history.back()" class="header-back">
            <i class="fas fa-arrow-left"></i>
        </a>
        <h1 class="header-title"><?php if(null !== ($page ?? null) && is_array($page) && array_key_exists('title', $page)): ?><?php echo (isset($page['title'])) ? $page['title'] : ""; ?><?php else: ?>页面<?php endif; ?></h1>
        <a href="/" class="header-home">
            <i class="fas fa-home"></i>
        </a>
    </div>
</header>

<style>
.mobile-header {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    height: 50px;
    background: var(--primary-color);
    color: white;
    z-index: 1000;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.header-inner {
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 100%;
    padding: 0 15px;
    max-width: 100%;
}

.header-back,
.header-home {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 40px;
    height: 40px;
    color: white;
    text-decoration: none;
    border-radius: 50%;
    transition: background-color 0.2s;
}

.header-back:hover,
.header-home:hover,
.header-back:active,
.header-home:active {
    background-color: rgba(255,255,255,0.1);
    color: white;
}

.header-title {
    flex: 1;
    text-align: center;
    font-size: 16px;
    font-weight: 500;
    margin: 0;
    padding: 0 10px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    color: white;
}

/* 简约主题样式 */
html.theme-simple .mobile-header {
    background: #ffffff;
    color: #333333;
    border-bottom: 1px solid #eeeeee;
}

html.theme-simple .header-back,
html.theme-simple .header-home,
html.theme-simple .header-title {
    color: #333333;
}

html.theme-simple .header-back:hover,
html.theme-simple .header-home:hover,
html.theme-simple .header-back:active,
html.theme-simple .header-home:active {
    background-color: rgba(0,0,0,0.05);
    color: #333333;
}

/* 为页面内容添加顶部间距 */
body {
    padding-top: 50px;
}
</style>
    
    <!-- 主内容区域 -->
    <div class="page-container">
        <!-- 页面内容 -->
        <div class="page-content">
            <?php echo $page['content']; ?>
        </div>
    </div>
    
    <!-- 图片放大遮罩 -->
    <div class="image-overlay" id="imageOverlay">
        <div class="close-btn" onclick="closeImageOverlay()">
            <i class="fas fa-times"></i>
        </div>
        <img id="overlayImage" src="" alt="">
    </div>
    
    <footer>
    <div class="container">
        <div class="footer-nav">
            <a href="/login.php">登录/注册</a>
            <a href="/fee.php">电话费用</a>
            <a href="/feedback.php">用户反馈</a>
        </div>
        <div class="theme-switcher">
            <p>主题切换</p>
            <div class="theme-dots">
                <a href="javascript:void(0);" onclick="switchTheme('red')" class="theme-dot theme-red" title="红色主题"></a>
                <a href="javascript:void(0);" onclick="switchTheme('blue')" class="theme-dot theme-blue" title="蓝色主题"></a>
                <a href="javascript:void(0);" onclick="switchTheme('green')" class="theme-dot theme-green" title="绿色主题"></a>
                <a href="javascript:void(0);" onclick="switchTheme('purple')" class="theme-dot theme-purple" title="紫色主题"></a>
                <a href="javascript:void(0);" onclick="switchTheme('orange')" class="theme-dot theme-orange" title="橙色主题"></a>
                <a href="javascript:void(0);" onclick="switchTheme('pink')" class="theme-dot theme-pink" title="粉色主题"></a>
                <a href="javascript:void(0);" onclick="switchTheme('ocean')" class="theme-dot theme-ocean" title="海洋主题"></a>
                <a href="javascript:void(0);" onclick="switchTheme('wechat')" class="theme-dot theme-wechat" title="微信风格"></a>
                <a href="javascript:void(0);" onclick="switchTheme('alipay')" class="theme-dot theme-alipay" title="支付宝风格"></a>
                <a href="javascript:void(0);" onclick="switchTheme('simple')" class="theme-dot theme-simple" title="简约主题"></a>
            </div>
        </div>
        <div class="footer-info">
            <p>京ICP证060405号</p>
            <p>客户服务热线: 10105858</p>
        </div>
    </div>
    <script>
    // 主题切换功能
    function switchTheme(theme) {
        // 设置cookie，有效期30天
        var date = new Date();
        date.setTime(date.getTime() + (30 * 24 * 60 * 60 * 1000));
        document.cookie = "site_theme=" + theme + "; expires=" + date.toUTCString() + "; path=/";
        
        // 更新页面上的主题类
        document.documentElement.className = document.documentElement.className.replace(/theme-\w+/g, '');
        document.documentElement.classList.add('theme-' + theme);
        
        // 同时更新body的主题类，确保背景色只应用于内容区域
        document.body.className = document.body.className.replace(/theme-\w+/g, '');
        document.body.classList.add('theme-' + theme);
        
        // 更新当前选中的主题点
        highlightCurrentTheme(theme);
        
        // 显示切换成功提示
        var themeNames = {
            'red': '红色',
            'blue': '蓝色',
            'green': '绿色',
            'purple': '紫色',
            'orange': '橙色',
            'pink': '粉色',
            'ocean': '海洋',
            'wechat': '微信风格',
            'alipay': '支付宝风格',
            'miui': '小米风格',
            'douyin': '抖音风格',
            'simple': '简约'
        };
        
        // 创建提示元素
        var toast = document.createElement('div');
        toast.className = 'theme-toast';
        toast.textContent = '已切换到' + themeNames[theme] + '主题';
        document.body.appendChild(toast);
        
        // 2秒后移除提示
        setTimeout(function() {
            toast.classList.add('hide');
            setTimeout(function() {
                document.body.removeChild(toast);
            }, 300);
        }, 2000);
    }
    
    // 高亮当前主题
    function highlightCurrentTheme(theme) {
        // 移除所有主题点的高亮
        var themeDots = document.querySelectorAll('.theme-dot');
        themeDots.forEach(function(dot) {
            dot.classList.remove('active');
        });
        
        // 添加当前主题的高亮
        var currentThemeDot = document.querySelector('.theme-dot.theme-' + theme);
        if (currentThemeDot) {
            currentThemeDot.classList.add('active');
        }
    }
    
    // 在页面加载时，根据当前主题高亮对应的主题点
    document.addEventListener('DOMContentLoaded', function() {
        // 获取当前主题
        var currentTheme = 'red'; // 默认主题
        var htmlClass = document.documentElement.className;
        var themeMatch = htmlClass.match(/theme-(\w+)/);
        
        if (themeMatch && themeMatch[1]) {
            currentTheme = themeMatch[1];
            
            // 确保body也具有相同的主题类
            document.body.className = document.body.className.replace(/theme-\w+/g, '');
            document.body.classList.add('theme-' + currentTheme);
        }
        
        // 高亮当前主题
        highlightCurrentTheme(currentTheme);
    });
    </script>
    <style>
    /* 主题切换样式 */
    html, body {
        min-height: 100%;
    }
    
    body {
        background-color: #f5f5f5; /* 默认背景色 */
        margin: 0;
        padding: 0;
    }
    
    /* 确保主题颜色只应用于body */
    html.theme-red, html.theme-blue, html.theme-green, 
    html.theme-purple, html.theme-orange, html.theme-pink,
    html.theme-ocean, html.theme-wechat, html.theme-alipay,
    html.theme-miui, html.theme-douyin {
        background-color: #fff; /* 重置HTML背景为白色 */
    }
    
    /* 主题背景色应用到body */
    body.theme-red { background-color: #fff5f5; }
    body.theme-blue { background-color: #f5f8ff; }
    body.theme-green { background-color: #f5fff8; }
    body.theme-purple { background-color: #f8f5ff; }
    body.theme-orange { background-color: #fff9f5; }
    body.theme-pink { background-color: #fff5f9; }
    body.theme-ocean { background-color: #f5faff; }
    body.theme-wechat { background-color: #f5fff7; }
    body.theme-alipay { background-color: #f5faff; }
    body.theme-simple { background-color: #f8f8f8; }
    
    .theme-switcher {
        text-align: center;
        margin: 15px 0;
        padding: 10px;
        background-color: rgba(255, 255, 255, 0.9);
        border-radius: 10px;
    }
    
    .theme-switcher p {
        margin: 0 0 8px 0;
        font-size: 14px;
        color: #666;
        font-weight: 500;
    }
    
    .theme-dots {
        display: flex;
        flex-wrap: wrap;
        justify-content: center;
        gap: 12px;
        padding: 5px;
    }
    
    .theme-dot {
        display: inline-block;
        width: 22px;
        height: 22px;
        border-radius: 50%;
        cursor: pointer;
        transition: transform 0.2s, box-shadow 0.2s;
        border: 2px solid #fff;
        box-shadow: 0 1px 3px rgba(0,0,0,0.1);
    }
    
    .theme-dot:hover {
        transform: scale(1.2);
        box-shadow: 0 2px 5px rgba(0,0,0,0.2);
    }
    
    .theme-dot.active {
        transform: scale(1.2);
        box-shadow: 0 0 0 2px #fff, 0 0 0 4px var(--primary-color, currentColor);
    }
    
    /* 主题颜色 */
    .theme-red {
        background-color: #e53935;
    }
    
    .theme-blue {
        background-color: #4285f4;
    }
    
    .theme-green {
        background-color: #00a878;
    }
    
    .theme-purple {
        background-color: #7b68ee;
    }
    
    .theme-orange {
        background-color: #ff6b01;
    }
    
    .theme-pink {
        background-color: #e91e63;
    }
    
    .theme-ocean {
        background-color: #006994;
    }
    
    .theme-wechat {
        background-color: #07c160;
    }
    
    .theme-alipay {
        background-color: #1677ff;
    }
    
    .theme-simple {
        background-color: #ffffff;
        border: 1px solid #eeeeee;
    }
    
    /* 主题切换toast提示 */
    .theme-toast {
        position: fixed;
        bottom: 80px;
        left: 50%;
        transform: translateX(-50%);
        background-color: rgba(0, 0, 0, 0.7);
        color: white;
        padding: 10px 20px;
        border-radius: 20px;
        font-size: 14px;
        z-index: 1000;
        opacity: 1;
        transition: opacity 0.3s;
    }
    
    .theme-toast.hide {
        opacity: 0;
    }
    
    /* 简约主题特殊处理 */
    .theme-simple header {
        background-color: #ffffff !important;
        color: #333333 !important;
        border-bottom: 1px solid #eeeeee !important;
        box-shadow: 0 1px 3px rgba(0,0,0,0.05) !important;
    }
    
    .theme-simple .header-back,
    .theme-simple .header-title,
    .theme-simple .header-share,
    .theme-simple .header-search-icon {
        color: #333333 !important;
    }
    
    .theme-simple .header-back:active,
    .theme-simple .header-share:active,
    .theme-simple .header-search-icon:active {
        background-color: rgba(0,0,0,0.05) !important;
    }
    </style>
</footer>


    <!-- 底部导航栏 -->
    <!-- 移动端底部导航栏 -->
<nav class="navbar">
    <a href="/" class="nav-item <?php if($current_page == 'index'): ?>active<?php endif; ?>">
        <span class="nav-icon"><i class="fas fa-home"></i></span>
        <span class="nav-text">首页</span>
    </a>
    <a href="/category.php" class="nav-item <?php if($current_page == 'category'): ?>active<?php endif; ?>">
        <span class="nav-icon"><i class="fas fa-th-large"></i></span>
        <span class="nav-text">分类</span>
    </a>
    <a href="/post.php" class="nav-item publish <?php if($current_page == 'post'): ?>active<?php endif; ?>">
        <span class="nav-icon"><i class="fas fa-plus"></i></span>
        <span class="nav-text">发布</span>
    </a>
    <a href="/message.php" class="nav-item <?php if($current_page == 'message'): ?>active<?php endif; ?>">
        <span class="nav-icon"><i class="fas fa-comment-alt"></i></span>
        <span class="nav-text">消息</span>
    </a>
    <a href="/member/" class="nav-item <?php if($current_page == 'member'): ?>active<?php endif; ?>">
        <span class="nav-icon"><i class="fas fa-user"></i></span>
        <span class="nav-text">我的</span>
    </a>
</nav>

    
    <script>
    // 页面加载完成后的处理
    document.addEventListener('DOMContentLoaded', function() {
        // 为外部链接添加新窗口打开
        const links = document.querySelectorAll('.page-content a[href^="http"]');
        links.forEach(function(link) {
            if (!link.hostname || link.hostname !== window.location.hostname) {
                link.target = '_blank';
                link.rel = 'noopener noreferrer';
            }
        });
        
        // 图片点击放大功能
        const images = document.querySelectorAll('.page-content img');
        images.forEach(function(img) {
            img.style.cursor = 'pointer';
            img.addEventListener('click', function() {
                showImageOverlay(this.src);
            });
        });
    });
    
    // 显示图片放大遮罩
    function showImageOverlay(src) {
        const overlay = document.getElementById('imageOverlay');
        const overlayImage = document.getElementById('overlayImage');
        overlayImage.src = src;
        overlay.classList.add('active');
        document.body.style.overflow = 'hidden';
    }
    
    // 关闭图片放大遮罩
    function closeImageOverlay() {
        const overlay = document.getElementById('imageOverlay');
        overlay.classList.remove('active');
        document.body.style.overflow = '';
    }
    
    // 点击遮罩背景关闭
    document.getElementById('imageOverlay').addEventListener('click', function(e) {
        if (e.target === this) {
            closeImageOverlay();
        }
    });
    
    // 分享或打印功能
    function shareOrPrint() {
        if (navigator.share) {
            navigator.share({
                title: document.title,
                url: window.location.href
            }).catch(function(error) {
                console.log('分享失败:', error);
                fallbackShare();
            });
        } else {
            fallbackShare();
        }
    }
    
    // 备用分享方式
    function fallbackShare() {
        const actions = [
            { text: '复制链接', action: copyLink },
            { text: '打印页面', action: function() { window.print(); } }
        ];
        
        const actionSheet = document.createElement('div');
        actionSheet.style.cssText = `
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            background: var(--card-bg);
            border-top: 1px solid var(--border-color);
            z-index: 9999;
            padding: 15px;
            transform: translateY(100%);
            transition: transform 0.3s;
        `;
        
        actions.forEach(function(action) {
            const btn = document.createElement('button');
            btn.textContent = action.text;
            btn.style.cssText = `
                display: block;
                width: 100%;
                padding: 12px;
                margin-bottom: 10px;
                background: var(--primary-color);
                color: white;
                border: none;
                border-radius: 6px;
                font-size: 16px;
            `;
            btn.onclick = function() {
                action.action();
                document.body.removeChild(actionSheet);
            };
            actionSheet.appendChild(btn);
        });
        
        const cancelBtn = document.createElement('button');
        cancelBtn.textContent = '取消';
        cancelBtn.style.cssText = `
            display: block;
            width: 100%;
            padding: 12px;
            background: transparent;
            color: var(--text-color);
            border: 1px solid var(--border-color);
            border-radius: 6px;
            font-size: 16px;
        `;
        cancelBtn.onclick = function() {
            document.body.removeChild(actionSheet);
        };
        actionSheet.appendChild(cancelBtn);
        
        document.body.appendChild(actionSheet);
        setTimeout(function() {
            actionSheet.style.transform = 'translateY(0)';
        }, 10);
    }
    
    // 复制链接
    function copyLink() {
        if (navigator.clipboard) {
            navigator.clipboard.writeText(window.location.href).then(function() {
                showToast('链接已复制到剪贴板');
            });
        } else {
            // 备用方法
            const textArea = document.createElement('textarea');
            textArea.value = window.location.href;
            document.body.appendChild(textArea);
            textArea.select();
            document.execCommand('copy');
            document.body.removeChild(textArea);
            showToast('链接已复制到剪贴板');
        }
    }
    
    // 显示提示消息
    function showToast(message) {
        const toast = document.createElement('div');
        toast.textContent = message;
        toast.style.cssText = `
            position: fixed;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: rgba(0,0,0,0.8);
            color: white;
            padding: 10px 20px;
            border-radius: 20px;
            z-index: 10000;
            font-size: 14px;
        `;
        document.body.appendChild(toast);
        setTimeout(function() {
            document.body.removeChild(toast);
        }, 2000);
    }
    </script>
</body>
</html>
