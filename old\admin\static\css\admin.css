
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

:root {
    --primary-color: #1b68ff;
    --primary-hover: #0045ce;
    --success-color: #3ad29f;
    --warning-color: #eea303;
    --danger-color: #f82f58;
    --info-color: #17a2b8;
    --text-color: #001a4e;
    --text-secondary: #6c757d;
    --border-color: #e9ecef;
    --bg-gray: #f8f9fa;
    --sidebar-width: 160px;
    --sidebar-collapsed-width: 64px;
    --sidebar-bg: #343a40;
    --header-height: 64px;
    --card-shadow: 0 0.5rem 1rem rgba(18, 38, 63, 0.05);
    --transition: all 0.3s ease;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
    font-size: 14px;
    line-height: 1.5;
    color: var(--text-color);
    background: var(--bg-gray);
}


.wrapper {
    display: flex;
    min-height: 100vh;
}


.sidebar {
    width: var(--sidebar-width);
    background: var(--sidebar-bg);
    color: #fff;
    position: fixed;
    height: 100vh;
    left: 0;
    top: 0;
    z-index: 1000;
    transition: var(--transition);
    padding-top: var(--header-height);
    overflow-y: auto;
    box-shadow: 2px 0 6px rgba(0,0,0,0.1);
}

.sidebar.collapsed {
    width: var(--sidebar-collapsed-width);
    overflow-x: hidden;
}

.sidebar-header {
    position: fixed;
    top: 0;
    left: 0;
    width: var(--sidebar-width);
    height: var(--header-height);
    background: var(--sidebar-bg);
    display: flex;
    align-items: center;
    padding: 0 20px;
    transition: var(--transition);
    z-index: 1001;
}

.sidebar.collapsed .sidebar-header {
    width: var(--sidebar-collapsed-width);
    padding: 0;
    justify-content: center;
}

.logo {
    font-size: 20px;
    font-weight: bold;
    color: #fff;
    text-decoration: none;
    display: flex;
    align-items: center;
    gap: 12px;
    width: 100%;
}

.logo i {
    font-size: 24px;
}

.sidebar.collapsed .logo {
    justify-content: center;
    padding: 0;
    margin: 0;
}

.sidebar.collapsed .logo span {
    display: none;
}


.menu-item {
    padding: 12px 20px;
    color: rgba(255,255,255,0.7);
    display: flex;
    align-items: center;
    gap: 12px;
    cursor: pointer;
    transition: var(--transition);
    position: relative;
    border-left: 3px solid transparent;
}

.sidebar.collapsed .menu-item {
    padding: 12px 0;
    justify-content: center;
    border-left: none;
}

.menu-item:hover {
    color: #fff;
    background: rgba(255,255,255,0.1);
    border-left: 3px solid var(--primary-color);
}

.menu-item.active {
    color: #fff;
    background: rgba(0,0,0,0.2);
    border-left: 3px solid var(--primary-color);
}

.menu-item i {
    font-size: 18px;
    width: 24px;
    text-align: center;
}

.menu-item span {
    transition: var(--transition);
}

.sidebar.collapsed .menu-item span {
    display: none;
}

.menu-item a {
    color: inherit;
    text-decoration: none;
    display: flex;
    align-items: center;
    gap: 12px;
    width: 100%;
}


.main-content {
    margin-left: var(--sidebar-width);
    flex: 1;
    padding: 24px;
    padding-top: calc(var(--header-height) + 24px);
    transition: var(--transition);
}

.wrapper.collapsed .main-content {
    margin-left: var(--sidebar-collapsed-width);
}

/* Top navigation */
.top-nav {
    height: var(--header-height);
    background: #fff;
    box-shadow: var(--card-shadow);
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 24px;
    position: fixed;
    top: 0;
    right: 0;
    left: var(--sidebar-width);
    z-index: 999;
    transition: var(--transition);
}

.wrapper.collapsed .top-nav {
    left: var(--sidebar-collapsed-width);
}

.nav-left {
    display: flex;
    align-items: center;
    gap: 16px;
}

.toggle-sidebar {
    font-size: 20px;
    cursor: pointer;
    color: var(--text-secondary);
    transition: var(--transition);
}

.toggle-sidebar:hover {
    color: var(--text-color);
}

.breadcrumb {
    display: flex;
    align-items: center;
    gap: 8px;
    color: var(--text-secondary);
}

.breadcrumb i {
    font-size: 12px;
}

.nav-right {
    display: flex;
    align-items: center;
    gap: 16px;
}

.nav-item {
    position: relative;
    cursor: pointer;
    color: var(--text-secondary);
    transition: var(--transition);
    padding: 8px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
}

.nav-item:hover {
    color: var(--primary-color);
    background-color: rgba(27, 104, 255, 0.1);
}

.nav-item i {
    font-size: 18px;
}

.user-item {
    display: flex;
    align-items: center;
    background-color: var(--bg-gray);
    border-radius: 24px;
    padding: 6px 16px;
    margin-left: 8px;
    border: 1px solid var(--border-color);
}

.user-item:hover {
    border-color: var(--primary-color);
    background-color: rgba(27, 104, 255, 0.05);
}

.user-avatar {
    width: 30px;
    height: 30px;
    border-radius: 50%;
    background: var(--primary-color);
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 10px;
    font-weight: 500;
}

.user-name {
    font-weight: 500;
    margin-right: 12px;
}

.logout-link {
    color: var(--text-secondary);
    transition: var(--transition);
    display: flex;
    align-items: center;
}

.logout-link:hover {
    color: var(--danger-color);
}

.badge {
    position: absolute;
    top: 0;
    right: 0;
    background: var(--danger-color);
    color: #fff;
    font-size: 12px;
    height: 18px;
    min-width: 18px;
    border-radius: 9px;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 0 6px;
}

/* Page title */
.page-title {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 24px;
}

.page-title h1 {
    font-size: 24px;
    font-weight: 500;
    margin: 0;
}

/* Component styles */
.section {
    margin-bottom: 32px;
}

.section-title {
    font-size: 18px;
    font-weight: 600;
    margin-bottom: 16px;
    color: var(--text-color);
}

.card {
    background: #fff;
    border-radius: 8px;
    box-shadow: var(--card-shadow);
    padding: 24px;
    margin-bottom: 24px;
}

.card-title {
    font-size: 16px;
    font-weight: 600;
    margin-bottom: 16px;
    color: var(--text-color);
}

.card-header {
    padding: 16px 24px;
    border-bottom: 1px solid var(--border-color);
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin: -24px -24px 24px -24px;
}

.card-body {
    padding: 0;
}

.card-body-narrow {
    max-width: 1200px;
}

.mb-4 {
    margin-bottom: 1.5rem !important;
}

.mt-3 {
    margin-top: 1rem !important;
}

/* Table styles */
.table {
    width: 100%;
    border-collapse: collapse;
    color: var(--text-secondary);
}

.table th,
.table td {
    padding: 12px;
    border-bottom: 1px solid var(--border-color);
    text-align: left;
}

.table th {
    font-weight: 600;
    background: var(--bg-gray);
    color: var(--text-color);
}

.table tr:hover {
    background: var(--bg-gray);
}

.content-table {
    width: 100%;
    border-collapse: collapse;
    background: #fff;
    border-radius: 4px;
    table-layout: fixed;
}

.content-table th {
    background-color: #f8f9fa;
    padding: 10px 15px;
    text-align: left;
    font-weight: 500;
    color: #333;
    border-bottom: 1px solid #dee2e6;
}

.content-table td {
    padding: 10px 15px;
    border-bottom: 1px solid #dee2e6;
    vertical-align: middle;
    word-break: break-word;
    overflow: hidden;
    text-overflow: ellipsis;
}

.content-table tr:last-child td {
    border-bottom: none;
}

.content-table tr:hover {
    background-color: #f8f9fa;
}

/* Table column width control */
.content-table .col-id {
    width: 60px;
}
.content-table .col-checkbox {
    width: 40px;
    text-align: center;
}
.content-table .col-title {
    width: auto;
    min-width: 250px;
}
.content-table .col-category {
    width: 150px;
}
.content-table .col-time {
    width: 160px;
}
.content-table .col-clicks {
    width: 80px;
    text-align: center;
}
.content-table .col-actions {
    width: 200px;
    text-align: right;
}

.table-responsive {
    display: block;
    width: 100%;
    overflow-x: auto;
    -webkit-overflow-scrolling: touch;
}
.text-center {
    text-align: center !important;
}
.text-right {
    text-align: right !important;
}

/* Form styles */
.form-group {
    margin-bottom: 20px;
    display: flex;
    flex-wrap: wrap;
    align-items: flex-start;
}
.form-label, .control-label {
    display: block;
    font-weight: 500;
    width: 120px;
    text-align: left;
    padding-right: 15px;
}
.form-field, .form-field.editor-container {
    flex: 0 0 auto;
    min-width: 300px;
    max-width: 800px;
}
.form-field.editor-container {
    max-width: 800px;
}
.form-hint, .help-block {
    width: 100%;
    margin-left: 0;
    margin-top: 5px;
    font-size: 12px;
    color: #666;
    display: block;
    clear: both;
}
.form-control {
    display: block;
    width: 100%;
    padding: 8px 12px;
    font-size: 14px;
    line-height: 1.5;
    color: var(--text-color);
    background-color: #fff;
    border: 1px solid var(--border-color);
    border-radius: 4px;
    transition: var(--transition);
}

.form-control:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(27, 104, 255, 0.25);
}
.form-horizontal {
    width: 100%;
}
textarea.form-control {
    min-height: 600px;
    padding: 12px;
    line-height: 1.6;
    resize: vertical;
}

/* Editor container styles */
.editor-container {
    width: 100%;
    max-width: 900px !important;
}
.editor-container textarea.form-control {
    min-height: 300px;
    font-family: "Courier New", Courier, monospace;
    font-size: 14px;
}

/* Filter form and filter tag styles */
.filter-form {
    display: flex;
    flex-wrap: nowrap;
    align-items: center;
    gap: 10px;
    margin-bottom: 20px;
    padding-bottom: 15px;
    border-bottom: 1px solid #e9ecef;
    overflow-x: auto;
}
.filter-form form {
    display: flex;
    flex-wrap: nowrap;
    align-items: center;
    width: 100%;
}
.filter-form label {
    margin-bottom: 0;
    margin-right: 5px;
    font-weight: 500;
    white-space: nowrap;
}
.filter-form .form-control {
    width: auto; 
    display: inline-block;
    padding: 4px 8px;
    min-width: 120px;
}
.filter-form-item {
    display: flex;
    align-items: center;
    margin-right: 15px;
    white-space: nowrap;
}
.filter-tag {
    display: inline-block;
    padding: 6px 10px;
    font-size: 12px;
    font-weight: normal;
    color: #fff;
    text-decoration: none;
    border-radius: 4px;
    margin-right: 5px;
    margin-bottom: 5px;
}
.filter-tag.active-all { background-color: #007bff; }
.filter-tag.inactive-all { background-color: #6c757d; }
.filter-tag.active-category { background-color: #007bff; }
.filter-tag.inactive-category { background-color: #6c757d; }
.ml-auto {
    margin-left: auto !important;
}

/* Button styles */
.btn {
    display: inline-flex;
    align-items: center;
    gap: 8px;
    padding: 8px 16px;
    border-radius: 4px;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: var(--transition);
    border: none;
    outline: none;
    text-decoration: none;
    white-space: nowrap;
    vertical-align: middle;
    user-select: none;
}

.btn i {
    font-size: 16px;
}

.btn-sm {
    padding: 0.25rem 0.5rem;
    font-size: 0.875rem;
    line-height: 1.5;
    border-radius: 0.2rem;
}

.btn-xs {
    padding: 0.15rem 0.4rem;
    font-size: 0.7rem;
}

.btn-primary {
    background: var(--primary-color);
    color: #fff;
}

.btn-primary:hover {
    background: var(--primary-hover);
}

.btn-success {
    background: var(--success-color);
    color: #fff;
}

.btn-warning {
    background: var(--warning-color);
    color: #fff;
}

.btn-danger {
    background: var(--danger-color);
    color: #fff;
}

.btn-secondary {
    color: #fff;
    background-color: var(--text-secondary);
    border-color: var(--text-secondary);
}

.btn-secondary:hover {
    background-color: #5a6268;
    border-color: #545b62;
}

.btn-outline {
    border: 1px solid var(--border-color);
    background: transparent;
    color: var(--text-color);
}

.btn-outline:hover {
    border-color: var(--primary-color);
    color: var(--primary-color);
}

/* Light button styles */
.btn-light-primary {
    background-color: rgba(27, 104, 255, 0.1);
    color: var(--primary-color);
    border: 1px solid rgba(27, 104, 255, 0.2);
}

.btn-light-primary:hover {
    background-color: rgba(27, 104, 255, 0.2);
    color: var(--primary-hover);
}

.btn-light-success {
    background-color: rgba(58, 210, 159, 0.1);
    color: var(--success-color);
    border: 1px solid rgba(58, 210, 159, 0.2);
}

.btn-light-success:hover {
    background-color: rgba(58, 210, 159, 0.2);
    color: #2a9d8f;
}

.btn-light-warning {
    background-color: rgba(238, 163, 3, 0.1);
    color: var(--warning-color);
    border: 1px solid rgba(238, 163, 3, 0.2);
}

.btn-light-warning:hover {
    background-color: rgba(238, 163, 3, 0.2);
    color: #cc8400;
}

.btn-light-danger {
    background-color: rgba(248, 47, 88, 0.1);
    color: var(--danger-color);
    border: 1px solid rgba(248, 47, 88, 0.2);
}

.btn-light-danger:hover {
    background-color: rgba(248, 47, 88, 0.2);
    color: #cc0000;
}

.btn-light-info {
    background-color: rgba(23, 162, 184, 0.1);
    color: var(--info-color);
    border: 1px solid rgba(23, 162, 184, 0.2);
}

.btn-light-info:hover {
    background-color: rgba(23, 162, 184, 0.2);
    color: #0088cc;
}

.btn-light-secondary {
    background-color: rgba(108, 117, 125, 0.1);
    color: var(--text-secondary);
    border: 1px solid rgba(108, 117, 125, 0.2);
}

.btn-light-secondary:hover {
    background-color: rgba(108, 117, 125, 0.2);
    color: #444444;
}

/* 标签样式 */
.label {
    display: inline-block;
    padding: 0.25em 0.4em;
    font-size: 75%;
    font-weight: 700;
    line-height: 1;
    text-align: center;
    white-space: nowrap;
    vertical-align: baseline;
    border-radius: 0.25rem;
    margin-right: 4px;
}
.label-danger {
    color: #fff;
    background-color: #dc3545;
}
.label-success {
    color: #fff;
    background-color: #28a745;
}
.label-warning {
    color: #212529;
    background-color: #ffc107;
}

/* 操作按钮样式 */
.info-actions {
    display: flex;
    flex-wrap: nowrap;
    justify-content: flex-end;
    gap: 5px;
    min-width: 120px;
}
.info-actions .btn {
    padding: 0.25rem 0.5rem;
    font-size: 0.875rem;
    white-space: nowrap;
    text-decoration: none !important;
}

/* 弹出框样式 */
.alert {
    position: relative;
    padding: 12px 20px;
    margin-bottom: 16px;
    border: 1px solid transparent;
    border-radius: 4px;
}
.alert .close {
    position: absolute;
    top: 0;
    right: 0;
    padding: 0.75rem 1.25rem;
    color: inherit;
    background-color: transparent;
    border: 0;
    font-size: 1.5rem;
    font-weight: 700;
    line-height: 1;
    cursor: pointer;
}
.alert-success {
    color: #155724;
    background-color: #d4edda;
    border-color: #c3e6cb;
}
.alert-danger {
    color: #721c24;
    background-color: #f8d7da;
    border-color: #f5c6cb;
}

/* 复选框和单选框 */
.checkbox-inline, .radio-inline {
    display: inline-flex;
    align-items: center;
    margin-right: 15px;
    cursor: pointer;
}
.checkbox-inline input, .radio-inline input {
    margin-right: 5px;
}

/* 表单按钮区域 */
.form-btn-group {
    margin-top: 20px;
}
.form-btn-group .form-field {
    margin-left: 120px;
}
.form-btn-group .btn {
    min-width: 120px;
    margin-right: 10px;
}

/* 表单操作按钮区域 */
.form-actions {
    margin-top: 30px;
    padding-top: 20px;
    border-top: 1px solid var(--border-color);
    display: flex;
    gap: 12px;
    align-items: center;
}

.form-actions .btn {
    min-width: 120px;
}



/* 布局工具类 */
.d-flex {
    display: flex !important;
}
.justify-content-between {
    justify-content: space-between !important;
}
.align-items-center {
    align-items: center !important;
}
.container-fluid {
    width: 100%;
    padding-right: 15px;
    padding-left: 15px;
    margin-right: auto;
    margin-left: auto;
}

/* PC端固定布局，移除响应式设计 */

/* 批量操作区域样式 */
.batch-actions {
    display: flex;
    gap: 10px;
    align-items: center;
    padding: 8px 0;
}

.batch-actions .btn-danger[disabled] {
    background-color: #f8d7da;
    border-color: #f5c6cb;
    color: #721c24;
    cursor: not-allowed;
    opacity: 0.65;
}

.batch-actions .btn-danger {
    background-color: #dc3545;
    border-color: #dc3545;
    color: #fff;
}

.batch-actions .btn-danger:hover:not([disabled]) {
    background-color: #c82333;
    border-color: #bd2130;
}

/* 复选框样式 */
input[type="checkbox"] {
    cursor: pointer;
    margin: 0;
    padding: 0;
}

/* 标签样式 */
.tag {
    display: inline-flex;
    align-items: center;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 12px;
    font-weight: 500;
}

.tag-primary {
    background: rgba(27, 104, 255, 0.1);
    color: var(--primary-color);
}

.tag-success {
    background: rgba(58, 210, 159, 0.1);
    color: var(--success-color);
}

.tag-warning {
    background: rgba(238, 163, 3, 0.1);
    color: var(--warning-color);
}

.tag-danger {
    background: rgba(248, 47, 88, 0.1);
    color: var(--danger-color);
}

/* 警告框样式 */
.alert {
    padding: 12px 16px;
    border-radius: 4px;
    margin-bottom: 16px;
    display: flex;
    align-items: center;
    gap: 12px;
    border: 1px solid transparent;
}

.alert i {
    font-size: 18px;
}

.alert-info {
    background: rgba(23, 162, 184, 0.1);
    color: var(--info-color);
    border-color: rgba(23, 162, 184, 0.2);
}

.alert-success {
    background: rgba(58, 210, 159, 0.1);
    color: var(--success-color);
    border-color: rgba(58, 210, 159, 0.2);
}

.alert-warning {
    background: rgba(238, 163, 3, 0.1);
    color: var(--warning-color);
    border-color: rgba(238, 163, 3, 0.2);
}

.alert-danger {
    background: rgba(248, 47, 88, 0.1);
    color: var(--danger-color);
    border-color: rgba(248, 47, 88, 0.2);
}

/* 统计卡片 */
.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 24px;
    margin-bottom: 24px;
}

.stat-card {
    background: #fff;
    border-radius: 8px;
    box-shadow: var(--card-shadow);
    padding: 20px;
    border: 1px solid var(--border-color);
    text-decoration: none;
    color: inherit;
    transition: var(--transition);
}

.stat-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 0.75rem 1.5rem rgba(18, 38, 63, 0.1);
}

.stat-icon {
    width: 48px;
    height: 48px;
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 16px;
}

.stat-icon i {
    font-size: 24px;
    color: #fff;
}

.stat-title {
    color: var(--text-secondary);
    font-size: 14px;
    margin-bottom: 8px;
}

.stat-value {
    font-size: 24px;
    font-weight: 600;
    color: var(--text-color);
}

.stat-trend {
    display: flex;
    align-items: center;
    gap: 4px;
    font-size: 12px;
    margin-top: 8px;
}

.trend-up {
    color: var(--success-color);
}

.trend-down {
    color: var(--danger-color);
}

/* 进度条样式 */
.progress {
    height: 8px;
    background: var(--bg-gray);
    border-radius: 4px;
    overflow: hidden;
}

.progress-bar {
    height: 100%;
    background: var(--primary-color);
    border-radius: 4px;
    transition: var(--transition);
}

/* 选项卡组件 */
.tabs {
    margin-bottom: 16px;
}

.tab-nav {
    display: flex;
    border-bottom: 1px solid var(--border-color);
    margin-bottom: 16px;
}

.tab-nav-item {
    padding: 12px 20px;
    font-weight: 500;
    color: var(--text-secondary);
    cursor: pointer;
    position: relative;
    transition: var(--transition);
}

.tab-nav-item:hover {
    color: var(--primary-color);
}

.tab-nav-item.active {
    color: var(--primary-color);
}

.tab-nav-item.active::after {
    content: '';
    position: absolute;
    bottom: -1px;
    left: 0;
    width: 100%;
    height: 2px;
    background: var(--primary-color);
}

.tab-content {
    display: none;
}

.tab-content.active {
    display: block;
}

/* 筛选表单样式 */
.filter-form {
    display: flex;
    flex-wrap: wrap;
    align-items: center;
    margin-bottom: 20px;
    padding-bottom: 15px;
    border-bottom: 1px solid var(--border-color);
    gap: 15px;
}

.filter-item {
    display: flex;
    align-items: center;
    gap: 8px;
    white-space: nowrap;
}

.filter-label {
    color: var(--text-color);
    font-weight: 500;
    margin: 0;
}

.filter-buttons {
    display: flex;
    align-items: center;
    gap: 8px;
}

/* 批量操作样式 */
.batch-actions {
    display: flex;
    gap: 10px;
    align-items: center;
    padding: 8px 0;
}

.batch-actions .btn[disabled] {
    opacity: 0.65;
    cursor: not-allowed;
}

/* 分页样式 */
.pagination-wrapper {
    margin-top: 20px;
    display: flex;
    justify-content: center;
    align-items: center;
}

.pagination-container {
    display: flex;
    align-items: center;
    justify-content: flex-end;
    width: 100%;
}

.pagination-total {
    margin-left: 10px;
    color: var(--text-secondary);
    font-size: 14px;
}

/* 分页按钮样式 */
.pagination {
    display: flex;
    align-items: center;
    gap: 8px;
    justify-content: center;
}

.page-link {
    display: inline-block;
    padding: 8px 12px;
    margin: 0 2px;
    color: var(--primary-color);
    text-decoration: none;
    border: 1px solid var(--border-color);
    border-radius: 4px;
    background-color: #fff;
    font-size: 14px;
    line-height: 1.4;
    transition: var(--transition);
}

.page-link:hover {
    background-color: var(--primary-color);
    color: #fff;
    border-color: var(--primary-color);
}

.page-current {
    display: inline-block;
    padding: 8px 12px;
    margin: 0 2px;
    color: #fff;
    background-color: var(--primary-color);
    border: 1px solid var(--primary-color);
    border-radius: 4px;
    font-size: 14px;
    line-height: 1.4;
    font-weight: 500;
}

.page-info {
    margin-left: 15px;
    color: var(--text-secondary);
    font-size: 14px;
}

/* 简单分页样式 */
.simple-pagination {
    display: flex;
    align-items: center;
    gap: 8px;
}

.pagination-btn {
    display: inline-block;
    padding: 6px 12px;
    margin: 0 2px;
    color: var(--primary-color);
    text-decoration: none;
    border: 1px solid var(--border-color);
    border-radius: 4px;
    background-color: #fff;
    font-size: 13px;
    line-height: 1.4;
    transition: var(--transition);
    min-width: 60px;
    text-align: center;
}

.pagination-btn:hover {
    background-color: var(--primary-color);
    color: #fff;
    border-color: var(--primary-color);
}

.pagination-btn.active {
    background-color: var(--primary-color);
    color: #fff;
    border-color: var(--primary-color);
    font-weight: 500;
}

.pagination-btn.disabled {
    color: var(--text-secondary);
    background-color: var(--bg-gray);
    border-color: var(--border-color);
    cursor: not-allowed;
    opacity: 0.6;
}

.pagination-btn.disabled:hover {
    background-color: var(--bg-gray);
    color: var(--text-secondary);
    border-color: var(--border-color);
}

/* 表格列宽控制 */
.table-responsive {
    display: block;
    width: 100%;
    overflow-x: auto;
    -webkit-overflow-scrolling: touch;
}

/* 操作按钮组 */
.action-buttons {
    display: flex;
    gap: 5px;
    justify-content: flex-end;
    align-items: center;
    flex-wrap: nowrap;
}

.action-buttons .btn {
    white-space: nowrap;
    text-decoration: none !important;
}

/* 禁用按钮样式 */
.btn.disabled,
.btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
    pointer-events: none;
}

/* 状态标签 */
.status-enabled {
    color: var(--success-color);
    font-weight: 500;
}

.status-disabled {
    color: var(--danger-color);
    font-weight: 500;
}

/* 表单行样式 */
.form-row {
    display: flex;
    gap: 16px;
    margin-bottom: 16px;
}

.form-col {
    flex: 1;
}

/* 单选框和复选框 */
.form-check {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-bottom: 8px;
    cursor: pointer;
}

.form-check input[type="radio"],
.form-check input[type="checkbox"] {
    width: 16px;
    height: 16px;
    margin: 0;
    cursor: pointer;
}

.form-check-label {
    color: var(--text-color);
    font-size: 14px;
    cursor: pointer;
}

/* 下拉选择框 */
.form-select {
    width: 100%;
    padding: 8px 12px;
    border: 1px solid var(--border-color);
    border-radius: 4px;
    font-size: 14px;
    color: var(--text-color);
    background-color: #fff;
    cursor: pointer;
    appearance: none;
    background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='12' height='12' viewBox='0 0 12 12'%3E%3Cpath fill='%236c757d' d='M6 8.825L1.175 4 2.238 2.938 6 6.7l3.763-3.762L10.825 4z'/%3E%3C/svg%3E");
    background-repeat: no-repeat;
    background-position: right 12px center;
    padding-right: 32px;
}

.form-select:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(27, 104, 255, 0.25);
}

/* 文本域 */
.form-textarea {
    width: 100%;
    padding: 8px 12px;
    border: 1px solid var(--border-color);
    border-radius: 4px;
    font-size: 14px;
    min-height: 100px;
    resize: vertical;
}

.form-textarea:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(27, 104, 255, 0.25);
}

/* 开关按钮 */
.form-switch {
    position: relative;
    display: inline-block;
    width: 40px;
    height: 20px;
}

.form-switch input {
    opacity: 0;
    width: 0;
    height: 0;
}

.switch-slider {
    position: absolute;
    cursor: pointer;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: var(--border-color);
    transition: var(--transition);
    border-radius: 20px;
}

.switch-slider:before {
    position: absolute;
    content: "";
    height: 16px;
    width: 16px;
    left: 2px;
    bottom: 2px;
    background-color: white;
    transition: var(--transition);
    border-radius: 50%;
}

.form-switch input:checked + .switch-slider {
    background-color: var(--primary-color);
}

.form-switch input:checked + .switch-slider:before {
    transform: translateX(20px);
}

/* 文件上传 */
.form-file {
    position: relative;
    display: inline-block;
}

.form-file input[type="file"] {
    position: absolute;
    top: 0;
    left: 0;
    opacity: 0;
    width: 100%;
    height: 100%;
    cursor: pointer;
}

.file-trigger {
    display: inline-flex;
    align-items: center;
    gap: 8px;
    padding: 8px 16px;
    border: 1px dashed var(--border-color);
    border-radius: 4px;
    color: var(--text-secondary);
    cursor: pointer;
    transition: var(--transition);
}

.file-trigger:hover {
    border-color: var(--primary-color);
    color: var(--primary-color);
}

.file-trigger i {
    font-size: 16px;
}

/* 表单验证状态 */
.form-control.is-invalid {
    border-color: var(--danger-color);
}

.form-control.is-valid {
    border-color: var(--success-color);
}

.invalid-feedback {
    display: none;
    color: var(--danger-color);
    font-size: 12px;
    margin-top: 4px;
}

.form-control.is-invalid + .invalid-feedback {
    display: block;
}

/* 工具类 */
.text-center {
    text-align: center !important;
}

.text-right {
    text-align: right !important;
}

.text-left {
    text-align: left !important;
}

/* 文本颜色工具类 */
.text-primary {
    color: var(--primary-color) !important;
}

.text-success {
    color: var(--success-color) !important;
}

.text-warning {
    color: var(--warning-color) !important;
}

.text-danger {
    color: var(--danger-color) !important;
}

.text-info {
    color: var(--info-color) !important;
}

.text-muted {
    color: var(--text-secondary) !important;
}

/* 缓存管理页面样式 */
.cache-operations {
    display: grid;
    gap: 32px;
}

.operation-group {
    background: #fff;
    border-radius: 12px;
    padding: 24px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    border: 1px solid rgba(0, 0, 0, 0.05);
}

.operation-header {
    display: flex;
    align-items: center;
    gap: 12px;
    margin-bottom: 20px;
    padding-bottom: 16px;
    border-bottom: 2px solid var(--bg-gray);
}

.operation-header i {
    font-size: 20px;
    color: var(--primary-color);
}

.operation-header span {
    font-size: 18px;
    font-weight: 600;
    color: var(--text-color);
}

.operation-header small {
    color: var(--text-secondary);
    font-size: 13px;
    margin-left: auto;
}

.operation-buttons {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 16px;
}

.operation-btn {
    display: flex;
    align-items: center;
    gap: 16px;
    padding: 20px;
    border-radius: 10px;
    text-decoration: none;
    transition: all 0.3s ease;
    border: 2px solid transparent;
    position: relative;
    overflow: hidden;
}

.operation-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s;
}

.operation-btn:hover::before {
    left: 100%;
}

.refresh-btn {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
}

.refresh-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
}

.clean-btn {
    background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
    color: white;
}

.clean-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(240, 147, 251, 0.4);
}

.danger-btn {
    background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
    color: white;
}

.danger-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(255, 107, 107, 0.4);
}

.btn-icon {
    width: 48px;
    height: 48px;
    border-radius: 12px;
    background: rgba(255, 255, 255, 0.2);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 20px;
    flex-shrink: 0;
}

.btn-content {
    flex: 1;
}

.btn-title {
    font-size: 16px;
    font-weight: 600;
    margin-bottom: 4px;
}

.btn-desc {
    font-size: 13px;
    opacity: 0.9;
}

/* 统计卡片优化 */
.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
    margin-bottom: 32px;
}

.stat-card {
    background: #fff;
    border-radius: 16px;
    padding: 24px;
    display: flex;
    align-items: center;
    gap: 16px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    border: 1px solid rgba(0, 0, 0, 0.05);
    transition: all 0.3s ease;
    text-decoration: none;
    color: inherit;
}

.stat-card:hover {
    transform: translateY(-4px);
    box-shadow: 0 8px 30px rgba(0, 0, 0, 0.12);
}

.stat-icon {
    width: 60px;
    height: 60px;
    border-radius: 16px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 24px;
    color: white;
    flex-shrink: 0;
}

.stat-content {
    flex: 1;
}

.stat-value {
    font-size: 24px;
    font-weight: 700;
    color: var(--text-color);
    margin-bottom: 4px;
}

.stat-title {
    font-size: 14px;
    color: var(--text-secondary);
    font-weight: 500;
}

.stat-desc {
    font-size: 12px;
    color: var(--text-secondary);
    opacity: 0.8;
    margin-top: 2px;
}

/* 快速提示 */
.quick-tips {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
}

.tip-item {
    background: #fff;
    border-radius: 12px;
    padding: 20px;
    display: flex;
    align-items: flex-start;
    gap: 16px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
    border-left: 4px solid var(--primary-color);
}

.tip-icon {
    width: 40px;
    height: 40px;
    border-radius: 10px;
    background: linear-gradient(135deg, var(--primary-color), var(--primary-hover));
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 16px;
    flex-shrink: 0;
}

.tip-content h4 {
    font-size: 16px;
    font-weight: 600;
    color: var(--text-color);
    margin-bottom: 8px;
}

.tip-content p {
    font-size: 14px;
    color: var(--text-secondary);
    line-height: 1.5;
    margin: 0;
}

/* PC端固定布局，移除缓存管理响应式 */

.d-flex {
    display: flex !important;
}

.d-inline-flex {
    display: inline-flex !important;
}

.justify-content-between {
    justify-content: space-between !important;
}

.justify-content-center {
    justify-content: center !important;
}

.justify-content-end {
    justify-content: flex-end !important;
}

.align-items-center {
    align-items: center !important;
}

.align-items-start {
    align-items: flex-start !important;
}

.flex-wrap {
    flex-wrap: wrap !important;
}

.gap-1 {
    gap: 0.25rem !important;
}

.gap-2 {
    gap: 0.5rem !important;
}

.gap-3 {
    gap: 1rem !important;
}

.gap-4 {
    gap: 1.5rem !important;
}

.mb-0 {
    margin-bottom: 0 !important;
}

.mb-1 {
    margin-bottom: 0.25rem !important;
}

.mb-2 {
    margin-bottom: 0.5rem !important;
}

.mb-3 {
    margin-bottom: 1rem !important;
}

.mb-4 {
    margin-bottom: 1.5rem !important;
}

.mt-0 {
    margin-top: 0 !important;
}

.mt-1 {
    margin-top: 0.25rem !important;
}

.mt-2 {
    margin-top: 0.5rem !important;
}

.mt-3 {
    margin-top: 1rem !important;
}

.mt-4 {
    margin-top: 1.5rem !important;
}

.ml-auto {
    margin-left: auto !important;
}

.mr-auto {
    margin-right: auto !important;
}

/* 栅格系统 */
.col-sm-2 {
    flex: 0 0 16.666667%;
    max-width: 16.666667%;
    padding-right: 15px;
    padding-left: 15px;
}

.col-sm-4 {
    flex: 0 0 33.333333%;
    max-width: 33.333333%;
    padding-right: 15px;
    padding-left: 15px;
}

.col-sm-6 {
    flex: 0 0 50%;
    max-width: 50%;
    padding-right: 15px;
    padding-left: 15px;
}

.col-sm-8 {
    flex: 0 0 66.666667%;
    max-width: 66.666667%;
    padding-right: 15px;
    padding-left: 15px;
}

.col-sm-10 {
    flex: 0 0 83.333333%;
    max-width: 83.333333%;
    padding-right: 15px;
    padding-left: 15px;
}

.col-sm-12 {
    flex: 0 0 100%;
    max-width: 100%;
    padding-right: 15px;
    padding-left: 15px;
}

.offset-sm-2 {
    margin-left: 16.666667%;
}

.container-fluid {
    width: 100%;
    padding-right: 15px;
    padding-left: 15px;
    margin-right: auto;
    margin-left: auto;
}

/* PC端固定布局，移除响应式设计 */



/* PC端固定布局，移除小屏幕响应式 */

/* 操作日志页面样式优化 */
.record-count {
    font-size: 13px;
    font-weight: 400;
    color: var(--text-secondary);
    margin-left: 10px;
}

/* 扩展筛选表单样式 - 使用更强的选择器 */
.card .filter-form-expanded {
    padding: 20px !important;
    background: var(--bg-gray) !important;
    border-radius: 8px !important;
    margin-bottom: 0 !important;
}

.card .filter-form-expanded .filter-row-expanded {
    display: grid !important;
    grid-template-columns: repeat(4, 1fr) !important;
    gap: 20px !important;
    margin-bottom: 16px !important;
    align-items: end !important;
}

.card .filter-form-expanded .filter-row-expanded:last-child {
    margin-bottom: 0 !important;
}

.card .filter-form-expanded .filter-item-expanded {
    display: flex !important;
    flex-direction: column !important;
    gap: 6px !important;
}

.card .filter-form-expanded .filter-item-expanded label {
    font-size: 13px !important;
    font-weight: 500 !important;
    color: var(--text-color) !important;
    margin-bottom: 0 !important;
}

.card .filter-form-expanded .filter-buttons-expanded {
    display: flex !important;
    gap: 10px !important;
    align-items: center !important;
}

/* 日志表格优化 - 去除换行 */
.table-logs td {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

/* 置顶设置样式 */
.top-settings {
    border: 1px solid #e0e0e0;
    border-radius: 6px;
    padding: 15px;
    background: #f9f9f9;
}

.top-option {
    margin-bottom: 15px;
    padding: 12px;
    border: 1px solid #ddd;
    border-radius: 4px;
    background: white;
    transition: all 0.2s ease;
}

.top-option:hover {
    border-color: #007bff;
    box-shadow: 0 2px 4px rgba(0,123,255,0.1);
}

.top-option:last-child {
    margin-bottom: 0;
}

.expire-setting {
    margin-top: 8px;
    padding: 10px;
    background: #f0f8ff;
    border-radius: 4px;
    border: 1px solid #d1ecf1;
}

.expire-setting label {
    font-weight: 500;
    margin-bottom: 5px;
    display: inline-block;
}

.expire-setting input[type="datetime-local"] {
    margin-left: 5px;
    margin-right: 10px;
    font-size: 13px;
}

.help-text {
    font-style: italic;
    margin-left: 5px;
}

.top-checkbox:checked + label {
    color: #007bff;
    font-weight: 500;
}

/* 移动端适配 */
@media (max-width: 768px) {
    .expire-setting input[type="datetime-local"] {
        width: 100% !important;
        margin: 5px 0;
        display: block !important;
    }

    .expire-setting {
        margin-left: 0 !important;
    }
}