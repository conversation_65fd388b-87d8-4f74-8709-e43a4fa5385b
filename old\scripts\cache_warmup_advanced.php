<?php
/**
 * 高级缓存预热脚本
 * 智能预热热门数据，提升网站性能
 * 兼容PHP 7.3-8.2
 */

// 设置执行时间限制
set_time_limit(300); // 5分钟

// 定义常量
define('IN_BTMPS', true);

// 引入公共文件
require_once dirname(__DIR__) . '/include/common.inc.php';

class AdvancedCacheWarmup {
    private $db;
    private $config;
    private $startTime;
    private $warmedCount = 0;
    private $errorCount = 0;
    private $logFile;
    
    public function __construct() {
        global $db, $config;
        $this->db = $db;
        $this->config = $config;
        $this->startTime = microtime(true);
        $this->logFile = DATA_PATH . 'logs/cache_warmup_' . date('Y-m-d') . '.log';
        
        // 确保日志目录存在
        $logDir = dirname($this->logFile);
        if (!is_dir($logDir)) {
            @mkdir($logDir, 0755, true);
        }
    }
    
    /**
     * 执行缓存预热
     */
    public function run() {
        $this->log("开始缓存预热...");
        
        try {
            // 1. 预热基础数据
            $this->warmupBasicData();
            
            // 2. 预热首页数据
            $this->warmupHomepageData();
            
            // 3. 预热热门分类数据
            $this->warmupHotCategories();
            
            // 4. 预热热门信息详情
            $this->warmupHotPosts();
            
            // 5. 预热新闻数据
            $this->warmupNewsData();
            
            // 6. 清理过期缓存
            $this->cleanupExpiredCache();
            
        } catch (Exception $e) {
            $this->log("预热过程中发生错误: " . $e->getMessage());
            $this->errorCount++;
        }
        
        $this->generateReport();
    }
    
    /**
     * 预热基础数据
     */
    private function warmupBasicData() {
        $this->log("预热基础数据...");
        
        // 分类数据
        if (!cache_exists('categories_all')) {
            $categories = getCachedCategories(true);
            if ($categories) {
                cache_set_smart('categories_all', $categories);
                $this->warmedCount++;
                $this->log("已预热分类数据 (" . count($categories) . " 个分类)");
            }
        }
        
        // 区域数据
        if (!cache_exists('regions_all')) {
            $regions = getCachedRegions(true);
            if ($regions) {
                cache_set_smart('regions_all', $regions);
                $this->warmedCount++;
                $this->log("已预热区域数据 (" . count($regions) . " 个区域)");
            }
        }
        
        // 导航数据
        if (!cache_exists('navigation_all')) {
            $navigation = getCachedNavigation(true);
            if ($navigation) {
                cache_set_smart('navigation_all', $navigation);
                $this->warmedCount++;
                $this->log("已预热导航数据");
            }
        }
    }
    
    /**
     * 预热首页数据
     */
    private function warmupHomepageData() {
        $this->log("预热首页数据...");
        
        $templates = ['pc', 'm', 'wx', 'app'];
        
        foreach ($templates as $template) {
            $cache_key = "index_page_{$template}";
            
            if (!cache_exists($cache_key)) {
                try {
                    // 模拟获取首页数据
                    $indexData = $this->getIndexPageData($template);
                    if ($indexData) {
                        cache_set_smart($cache_key, $indexData);
                        $this->warmedCount++;
                        $this->log("已预热{$template}端首页数据");
                    }
                } catch (Exception $e) {
                    $this->log("预热{$template}端首页数据失败: " . $e->getMessage());
                    $this->errorCount++;
                }
            }
        }
    }
    
    /**
     * 预热热门分类数据
     */
    private function warmupHotCategories() {
        $this->log("预热热门分类数据...");
        
        // 获取热门分类（按信息数量排序）
        $sql = "SELECT c.id, c.name, COUNT(p.id) as post_count 
                FROM categories c 
                LEFT JOIN posts p ON c.id = p.category_id AND p.status = 1 
                WHERE c.status = 1 
                GROUP BY c.id 
                ORDER BY post_count DESC 
                LIMIT 10";
        
        $result = $this->db->query($sql);
        
        while ($category = $this->db->fetch_array($result)) {
            $catId = $category['id'];
            
            // 预热分类列表页（第一页）
            $templates = ['pc', 'm'];
            foreach ($templates as $template) {
                $cache_key = "category_list_{$template}_{$catId}_1_30_0";
                
                if (!cache_exists($cache_key)) {
                    try {
                        $posts = getCategoryPostsDirectly($catId, 1, 30, 0, 0, false);
                        $totalPosts = getCachedCategoryPostsCount($catId, 0, 0, false);
                        
                        $cache_data = array(
                            'posts' => $posts,
                            'totalPosts' => $totalPosts
                        );
                        
                        cache_set_smart($cache_key, $cache_data);
                        $this->warmedCount++;
                        $this->log("已预热分类 {$category['name']} 的{$template}端列表页");
                    } catch (Exception $e) {
                        $this->log("预热分类 {$category['name']} 失败: " . $e->getMessage());
                        $this->errorCount++;
                    }
                }
            }
        }
    }
    
    /**
     * 预热热门信息详情
     */
    private function warmupHotPosts() {
        $this->log("预热热门信息详情...");
        
        // 获取热门信息（按浏览量排序）
        $sql = "SELECT id, title, view_count 
                FROM posts 
                WHERE status = 1 AND expired_at > NOW() 
                ORDER BY view_count DESC 
                LIMIT 20";
        
        $result = $this->db->query($sql);
        
        while ($post = $this->db->fetch_array($result)) {
            $postId = $post['id'];
            
            // 预热详情页缓存
            $templates = ['pc', 'm'];
            foreach ($templates as $template) {
                $cache_key = "detail_{$postId}_{$template}";
                
                if (!cache_exists($cache_key)) {
                    try {
                        $postDetail = getPostDetail($postId, false);
                        if ($postDetail) {
                            cache_set_smart($cache_key, $postDetail);
                            $this->warmedCount++;
                            $this->log("已预热信息详情: {$post['title']} ({$template}端)");
                        }
                    } catch (Exception $e) {
                        $this->log("预热信息详情失败 (ID: {$postId}): " . $e->getMessage());
                        $this->errorCount++;
                    }
                }
            }
        }
    }
    
    /**
     * 预热新闻数据
     */
    private function warmupNewsData() {
        $this->log("预热新闻数据...");
        
        // 预热新闻首页
        $templates = ['pc', 'm'];
        foreach ($templates as $template) {
            $cache_key = "news_home_{$template}";
            
            if (!cache_exists($cache_key)) {
                try {
                    // 这里应该调用获取新闻首页数据的函数
                    // $newsData = getNewsHomeData($template);
                    // 暂时跳过，因为需要具体的新闻模块函数
                    $this->log("跳过新闻数据预热（需要新闻模块支持）");
                } catch (Exception $e) {
                    $this->log("预热新闻数据失败: " . $e->getMessage());
                    $this->errorCount++;
                }
            }
        }
    }
    
    /**
     * 清理过期缓存
     */
    private function cleanupExpiredCache() {
        $this->log("清理过期缓存...");
        
        $cleaned = cache_cleanup();
        $this->log("已清理 {$cleaned} 个过期缓存文件");
    }
    
    /**
     * 获取首页数据
     */
    private function getIndexPageData($template) {
        // 模拟获取首页数据的过程
        $indexSize = isset($this->config['index_size']) ? intval($this->config['index_size']) : 10;
        $hotPostsCount = isset($this->config['hot_posts_count']) ? intval($this->config['hot_posts_count']) : 8;
        
        return array(
            'categories' => $GLOBALS['cached_categories'],
            'topPosts' => getTopPosts(10),
            'normalPosts' => getNormalPosts($indexSize),
            'hotPosts' => getHotPosts($hotPostsCount),
            'template' => $template
        );
    }
    
    /**
     * 生成预热报告
     */
    private function generateReport() {
        $endTime = microtime(true);
        $duration = round($endTime - $this->startTime, 2);
        
        $report = "\n=== 缓存预热完成报告 ===\n";
        $report .= "执行时间: {$duration} 秒\n";
        $report .= "预热成功: {$this->warmedCount} 项\n";
        $report .= "预热失败: {$this->errorCount} 项\n";
        $report .= "完成时间: " . date('Y-m-d H:i:s') . "\n";
        $report .= "========================\n";
        
        $this->log($report);
        
        // 输出到控制台
        echo $report;
    }
    
    /**
     * 记录日志
     */
    private function log($message) {
        $timestamp = date('Y-m-d H:i:s');
        $logMessage = "[{$timestamp}] {$message}\n";
        
        // 写入日志文件
        @file_put_contents($this->logFile, $logMessage, FILE_APPEND | LOCK_EX);
        
        // 如果是命令行模式，同时输出到控制台
        if (php_sapi_name() === 'cli') {
            echo $logMessage;
        }
    }
}

// 执行预热
if (php_sapi_name() === 'cli' || (isset($_GET['run']) && $_GET['run'] === 'warmup')) {
    $warmup = new AdvancedCacheWarmup();
    $warmup->run();
} else {
    echo "请通过命令行执行此脚本，或访问 ?run=warmup 参数";
}
