/**
 * 图片压缩相关样式
 * 适用于前台、后台、手机端
 */

/* 通用进度条样式 */
.image-compress-progress {
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background: rgba(0, 0, 0, 0.9);
    color: white;
    padding: 20px;
    border-radius: 8px;
    z-index: 9999;
    text-align: center;
    min-width: 300px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
}

.progress-bar {
    width: 100%;
    height: 6px;
    background: #333;
    border-radius: 3px;
    overflow: hidden;
    margin: 10px 0;
}

.progress-fill {
    height: 100%;
    background: linear-gradient(90deg, #007bff, #0056b3);
    border-radius: 3px;
    transition: width 0.3s ease;
}

.progress-text {
    font-size: 14px;
    margin-top: 10px;
    color: #ccc;
}

/* 图片信息提示 */
.image-info {
    position: absolute;
    top: 5px;
    left: 5px;
    background: rgba(0, 0, 0, 0.7);
    color: white;
    border-radius: 50%;
    width: 20px;
    height: 20px;
    text-align: center;
    line-height: 20px;
    font-size: 12px;
    cursor: help;
    z-index: 10;
}

.image-info:hover {
    background: rgba(0, 0, 0, 0.9);
}

/* 压缩信息面板 */
.compress-info {
    position: absolute;
    bottom: 2px;
    left: 2px;
    right: 2px;
    background: rgba(0, 0, 0, 0.7);
    color: white;
    font-size: 10px;
    padding: 2px 4px;
    border-radius: 2px;
    text-align: center;
}

/* 后台管理专用样式 */
.admin-image-info {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    background: linear-gradient(rgba(0, 0, 0, 0.8), transparent);
    color: white;
    font-size: 11px;
    padding: 5px;
    border-radius: 4px 4px 0 0;
    opacity: 0;
    transition: opacity 0.3s;
}

.image-item:hover .admin-image-info {
    opacity: 1;
}

/* 移动端优化 */
@media (max-width: 768px) {
    .image-compress-progress {
        min-width: 280px;
        padding: 15px;
    }
    
    .progress-text {
        font-size: 12px;
    }
    
    .image-info {
        width: 18px;
        height: 18px;
        line-height: 18px;
        font-size: 11px;
    }
    
    .compress-info {
        font-size: 9px;
        padding: 1px 3px;
    }
}

/* 压缩状态指示器 */
.compress-status {
    position: absolute;
    top: 5px;
    right: 5px;
    padding: 2px 6px;
    border-radius: 10px;
    font-size: 10px;
    font-weight: bold;
    text-transform: uppercase;
}

.compress-status.compressed {
    background: #28a745;
    color: white;
}

.compress-status.original {
    background: #6c757d;
    color: white;
}

.compress-status.processing {
    background: #ffc107;
    color: #212529;
    animation: pulse 1.5s infinite;
}

@keyframes pulse {
    0% { opacity: 1; }
    50% { opacity: 0.5; }
    100% { opacity: 1; }
}

/* 压缩详情工具提示 */
.compress-tooltip {
    position: relative;
    display: inline-block;
}

.compress-tooltip .tooltiptext {
    visibility: hidden;
    width: 200px;
    background-color: #333;
    color: #fff;
    text-align: left;
    border-radius: 6px;
    padding: 8px;
    position: absolute;
    z-index: 1;
    bottom: 125%;
    left: 50%;
    margin-left: -100px;
    opacity: 0;
    transition: opacity 0.3s;
    font-size: 12px;
    line-height: 1.4;
}

.compress-tooltip .tooltiptext::after {
    content: "";
    position: absolute;
    top: 100%;
    left: 50%;
    margin-left: -5px;
    border-width: 5px;
    border-style: solid;
    border-color: #333 transparent transparent transparent;
}

.compress-tooltip:hover .tooltiptext {
    visibility: visible;
    opacity: 1;
}

/* 压缩设置面板 */
.compress-settings {
    background: #f8f9fa;
    border: 1px solid #dee2e6;
    border-radius: 4px;
    padding: 15px;
    margin: 10px 0;
}

.compress-settings h4 {
    margin: 0 0 10px 0;
    font-size: 14px;
    color: #495057;
}

.compress-option {
    display: flex;
    align-items: center;
    margin: 8px 0;
}

.compress-option label {
    margin-left: 8px;
    font-size: 13px;
    color: #6c757d;
}

.compress-option input[type="range"] {
    flex: 1;
    margin: 0 10px;
}

.compress-option .value-display {
    min-width: 40px;
    text-align: right;
    font-size: 12px;
    color: #495057;
}

/* 压缩统计 */
.compress-stats {
    background: #e9ecef;
    border-radius: 4px;
    padding: 10px;
    margin: 10px 0;
    font-size: 12px;
    color: #495057;
}

.compress-stats .stat-item {
    display: flex;
    justify-content: space-between;
    margin: 3px 0;
}

.compress-stats .stat-value {
    font-weight: bold;
    color: #007bff;
}

/* 错误提示 */
.compress-error {
    background: #f8d7da;
    color: #721c24;
    border: 1px solid #f5c6cb;
    border-radius: 4px;
    padding: 8px 12px;
    margin: 5px 0;
    font-size: 12px;
}

/* 成功提示 */
.compress-success {
    background: #d4edda;
    color: #155724;
    border: 1px solid #c3e6cb;
    border-radius: 4px;
    padding: 8px 12px;
    margin: 5px 0;
    font-size: 12px;
}

/* 加载动画 */
.compress-loading {
    display: inline-block;
    width: 16px;
    height: 16px;
    border: 2px solid #f3f3f3;
    border-top: 2px solid #007bff;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-right: 8px;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* 响应式调整 */
@media (max-width: 480px) {
    .compress-settings {
        padding: 10px;
    }
    
    .compress-option {
        flex-direction: column;
        align-items: flex-start;
    }
    
    .compress-option input[type="range"] {
        width: 100%;
        margin: 5px 0;
    }
    
    .compress-tooltip .tooltiptext {
        width: 150px;
        margin-left: -75px;
        font-size: 11px;
    }
}
