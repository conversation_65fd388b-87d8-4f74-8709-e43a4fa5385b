/* 信息管理页面样式 */

/* 页面头部 */
.header-inner {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 15px;
    height: 56px;
}

.header-left {
    flex: 1;
    display: flex;
    align-items: center;
}

.header-back {
    color: #fff;
    text-decoration: none;
    font-size: 18px;
    margin-right: 15px;
}

.header-title {
    font-size: 19px;
    font-weight: 500;
    flex: 2;
    text-align: center;
    font-family: var(--font-family);
}

.header-right {
    flex: 1;
    display: flex;
    justify-content: flex-end;
}

/* 信息显示区域 */
.post-meta {
    background-color: #fff;
    padding: 20px;
    margin: 15px 15px 10px;
    border-radius: 0;
    box-shadow: none;
    border: 1px solid #eee;
}

.post-title {
    font-size: 19px;
    font-weight: 500;
    margin-bottom: 12px;
    color: var(--text-color);
    font-family: var(--font-family);
    letter-spacing: -0.2px;
}

.post-info {
    display: flex;
    align-items: center;
    color: var(--text-light);
    font-size: 15px;
}

.post-info-item {
    display: flex;
    align-items: center;
    margin-right: 15px;
}

.post-info-item i {
    margin-right: 5px;
    color: var(--accent-color);
}

/* 密码输入区域 */
.form-container {
    width: 100%;
    padding: 0;
}

.card {
    background: white;
    border-radius: 0;
    overflow: hidden;
    margin: 0 15px 15px;
    box-shadow: none;
    border: 1px solid #eee;
}

.card-header {
    background-color: var(--secondary-color);
    color: white;
    padding: 15px;
    font-weight: 500;
    font-size: 17px;
    border: none;
}
        
.password-section {
    padding: 20px 15px;
    background-color: white;
    position: relative;
    border-bottom: 1px solid #eee;
}

.password-input {
    position: relative;
    margin-bottom: 20px;
}

.password-input label {
    display: block;
    font-size: 15px;
    margin-bottom: 8px;
    color: var(--text-color);
    font-weight: 400;
}
        
.password-input input {
    width: 100%;
    border: 1px solid #ddd;
    border-radius: 0;
    padding: 12px 15px;
    font-size: 16px;
    outline: none;
    transition: all 0.2s;
}

.password-input input:focus {
    border-color: var(--primary-color);
    box-shadow: none;
}

.password-toggle {
    position: absolute;
    right: 15px;
    top: 40px;
    color: #999;
    cursor: pointer;
    font-size: 18px;
}

/* 操作按钮区 */
.action-section {
    padding: 15px;
    display: none; /* 默认隐藏 */
    background-color: #fff;
}

.action-buttons {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 10px;
    margin-bottom: 0;
}

.action-button {
    border: none;
    border-radius: 0;
    padding: 15px;
    text-align: center;
    font-size: 15px;
    font-weight: 400;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    text-decoration: none;
    transition: all 0.2s;
}

.action-button i {
    margin-right: 8px;
    font-size: 16px;
}

.edit-button {
    background-color: var(--primary-color);
}

.refresh-button {
    background-color: var(--secondary-color);
}

.view-button {
    background-color: var(--accent-color);
    opacity: 1;
    grid-column: 1 / 3;
}

.delete-button {
    background-color: #e74c3c;
    grid-column: 1 / 3;
}

.action-button:hover {
    transform: none;
    opacity: 0.9;
    box-shadow: none;
}

.submit-button {
    width: 100%;
    background-color: var(--primary-color);
    color: white;
    border: none;
    border-radius: 0;
    padding: 15px;
    font-size: 16px;
    font-weight: 400;
    cursor: pointer;
    transition: opacity 0.2s;
}

.submit-button:hover {
    background-color: var(--primary-color);
    opacity: 0.9;
}

/* 加载动画 */
.loader {
    display: none;
    text-align: center;
    padding: 20px 0;
    background-color: #fff;
    border-bottom: 1px solid #eee;
}

.loader-spinner {
    width: 40px;
    height: 40px;
    border: 2px solid rgba(var(--primary-color-rgb), 0.3);
    border-radius: 50%;
    border-top-color: var(--primary-color);
    animation: spin 1s linear infinite;
    margin: 0 auto 15px;
}

@keyframes spin {
    to { transform: rotate(360deg); }
}