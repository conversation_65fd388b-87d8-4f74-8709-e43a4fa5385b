<?php
/**
 * 缓存监控工具
 * 提供缓存性能监控、统计分析和管理功能
 */

define('IN_BTMPS', true);
require_once '../include/common.inc.php';

// 检查管理员权限
session_start();
if (!isset($_SESSION['admin_logged_in']) || $_SESSION['admin_logged_in'] !== true) {
    header('Location: login.php');
    exit;
}

// 处理AJAX请求
if (isset($_GET['action'])) {
    header('Content-Type: application/json; charset=utf-8');
    
    switch ($_GET['action']) {
        case 'stats':
            echo json_encode(getCacheMonitorStats());
            break;
            
        case 'hot_keys':
            $limit = isset($_GET['limit']) ? intval($_GET['limit']) : 10;
            echo json_encode(cache_hot_keys($limit));
            break;
            
        case 'cleanup':
            $cleaned = cache_cleanup();
            echo json_encode(['success' => true, 'cleaned' => $cleaned]);
            break;
            
        case 'warmup':
            $warmed = cache_warmup();
            echo json_encode(['success' => true, 'warmed' => $warmed]);
            break;
            
        case 'clear_level':
            $level = isset($_GET['level']) ? intval($_GET['level']) : null;
            $cleared = cache_clear($level);
            echo json_encode(['success' => true, 'cleared' => $cleared]);
            break;
            
        default:
            echo json_encode(['error' => '未知操作']);
    }
    exit;
}

/**
 * 获取缓存监控统计数据
 */
function getCacheMonitorStats() {
    global $config;
    
    // 基础统计
    $info = $GLOBALS['file_cache']->getInfo();
    $performance = cache_stats();
    
    // 分层统计
    $levelStats = [];
    $levels = [
        0 => '永久缓存',
        1 => '长期缓存', 
        2 => '中期缓存',
        3 => '短期缓存',
        4 => '临时缓存'
    ];
    
    foreach ($levels as $levelId => $levelName) {
        $levelDir = DATA_PATH . 'cache/' . ['permanent', 'long', 'medium', 'short', 'temp'][$levelId] . '/';
        if (is_dir($levelDir)) {
            $files = glob($levelDir . '*.cache');
            $size = 0;
            $count = count($files);
            
            foreach ($files as $file) {
                $size += filesize($file);
            }
            
            $levelStats[] = [
                'level' => $levelId,
                'name' => $levelName,
                'count' => $count,
                'size' => $size,
                'size_formatted' => formatBytes($size)
            ];
        }
    }
    
    // 缓存配置
    $cacheConfig = [
        'cache_enable' => isset($config['cache_enable']) ? $config['cache_enable'] : 1,
        'cache_compress' => isset($config['cache_compress']) ? $config['cache_compress'] : 0,
        'cache_index' => isset($config['cache_index']) ? $config['cache_index'] : 3600,
        'cache_list' => isset($config['cache_list']) ? $config['cache_list'] : 1800,
        'cache_post' => isset($config['cache_post']) ? $config['cache_post'] : 1800,
        'cache_search' => isset($config['cache_search']) ? $config['cache_search'] : 600,
    ];
    
    return [
        'basic' => $info,
        'performance' => $performance,
        'levels' => $levelStats,
        'config' => $cacheConfig,
        'timestamp' => time()
    ];
}

/**
 * 格式化字节数
 */
function formatBytes($bytes, $precision = 2) {
    $units = array('B', 'KB', 'MB', 'GB', 'TB');
    
    for ($i = 0; $bytes > 1024 && $i < count($units) - 1; $i++) {
        $bytes /= 1024;
    }
    
    return round($bytes, $precision) . ' ' . $units[$i];
}

$pageTitle = '缓存监控';
?>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $pageTitle; ?> - 后台管理</title>
    <link href="static/css/bootstrap.min.css" rel="stylesheet">
    <link href="static/css/admin.css" rel="stylesheet">
    <style>
        .stats-card {
            background: #fff;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .stats-number {
            font-size: 2em;
            font-weight: bold;
            color: #007bff;
        }
        .level-bar {
            height: 20px;
            background: #e9ecef;
            border-radius: 10px;
            overflow: hidden;
            margin: 10px 0;
        }
        .level-fill {
            height: 100%;
            background: linear-gradient(90deg, #007bff, #28a745);
            transition: width 0.3s ease;
        }
        .hot-key-item {
            padding: 10px;
            border-bottom: 1px solid #eee;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        .btn-group-sm .btn {
            margin-right: 5px;
        }
        .refresh-indicator {
            display: none;
            color: #007bff;
        }
    </style>
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <div class="col-md-12">
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <h2><?php echo $pageTitle; ?></h2>
                    <div class="btn-group btn-group-sm">
                        <button class="btn btn-primary" onclick="refreshStats()">
                            <span class="refresh-indicator">⟳</span> 刷新数据
                        </button>
                        <button class="btn btn-success" onclick="warmupCache()">预热缓存</button>
                        <button class="btn btn-warning" onclick="cleanupCache()">清理过期</button>
                        <button class="btn btn-danger" onclick="clearAllCache()">清空所有</button>
                    </div>
                </div>
                
                <!-- 基础统计 -->
                <div class="row" id="basic-stats">
                    <div class="col-md-3">
                        <div class="stats-card text-center">
                            <div class="stats-number" id="total-files">-</div>
                            <div>缓存文件数</div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="stats-card text-center">
                            <div class="stats-number" id="total-size">-</div>
                            <div>总占用空间</div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="stats-card text-center">
                            <div class="stats-number" id="hit-rate">-</div>
                            <div>缓存命中率</div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="stats-card text-center">
                            <div class="stats-number" id="compression">-</div>
                            <div>压缩状态</div>
                        </div>
                    </div>
                </div>
                
                <!-- 分层统计 -->
                <div class="row">
                    <div class="col-md-8">
                        <div class="stats-card">
                            <h5>分层缓存统计</h5>
                            <div id="level-stats"></div>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="stats-card">
                            <h5>热门缓存键</h5>
                            <div id="hot-keys"></div>
                        </div>
                    </div>
                </div>
                
                <!-- 操作日志 -->
                <div class="row">
                    <div class="col-md-12">
                        <div class="stats-card">
                            <h5>操作日志</h5>
                            <div id="operation-log" style="height: 200px; overflow-y: auto; background: #f8f9fa; padding: 10px; border-radius: 4px;">
                                <div class="text-muted">等待操作...</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="static/js/jquery.min.js"></script>
    <script src="static/js/bootstrap.min.js"></script>
    <script>
        // 页面加载时获取数据
        $(document).ready(function() {
            refreshStats();
            loadHotKeys();
            
            // 每30秒自动刷新
            setInterval(refreshStats, 30000);
        });
        
        // 刷新统计数据
        function refreshStats() {
            $('.refresh-indicator').show();
            
            $.get('?action=stats', function(data) {
                updateBasicStats(data.basic, data.performance);
                updateLevelStats(data.levels);
                $('.refresh-indicator').hide();
            }).fail(function() {
                addLog('获取统计数据失败', 'error');
                $('.refresh-indicator').hide();
            });
        }
        
        // 更新基础统计
        function updateBasicStats(basic, performance) {
            $('#total-files').text(basic.count);
            $('#total-size').text(basic.total_size_formatted);
            $('#hit-rate').text(performance.hit_rate);
            $('#compression').text(performance.compression_enabled ? '已启用' : '未启用');
        }
        
        // 更新分层统计
        function updateLevelStats(levels) {
            let html = '';
            let maxSize = Math.max(...levels.map(l => l.size));
            
            levels.forEach(function(level) {
                let percentage = maxSize > 0 ? (level.size / maxSize * 100) : 0;
                html += `
                    <div class="mb-3">
                        <div class="d-flex justify-content-between">
                            <span>${level.name}</span>
                            <span>${level.count} 个文件 (${level.size_formatted})</span>
                        </div>
                        <div class="level-bar">
                            <div class="level-fill" style="width: ${percentage}%"></div>
                        </div>
                        <div class="text-right">
                            <button class="btn btn-sm btn-outline-danger" onclick="clearLevel(${level.level})">
                                清空此层
                            </button>
                        </div>
                    </div>
                `;
            });
            
            $('#level-stats').html(html);
        }
        
        // 加载热门缓存键
        function loadHotKeys() {
            $.get('?action=hot_keys&limit=10', function(data) {
                let html = '';
                if (data.length === 0) {
                    html = '<div class="text-muted text-center">暂无数据</div>';
                } else {
                    data.forEach(function(item) {
                        html += `
                            <div class="hot-key-item">
                                <div>
                                    <div style="font-weight: bold;">${item.key}</div>
                                    <small class="text-muted">${item.level} | ${item.hits} 次命中</small>
                                </div>
                            </div>
                        `;
                    });
                }
                $('#hot-keys').html(html);
            });
        }
        
        // 预热缓存
        function warmupCache() {
            addLog('开始预热缓存...', 'info');
            $.get('?action=warmup', function(data) {
                if (data.success) {
                    addLog(`缓存预热完成，预热了 ${data.warmed} 项`, 'success');
                    refreshStats();
                } else {
                    addLog('缓存预热失败', 'error');
                }
            });
        }
        
        // 清理过期缓存
        function cleanupCache() {
            addLog('开始清理过期缓存...', 'info');
            $.get('?action=cleanup', function(data) {
                if (data.success) {
                    addLog(`清理完成，删除了 ${data.cleaned} 个过期文件`, 'success');
                    refreshStats();
                } else {
                    addLog('清理失败', 'error');
                }
            });
        }
        
        // 清空所有缓存
        function clearAllCache() {
            if (confirm('确定要清空所有缓存吗？此操作不可恢复！')) {
                addLog('开始清空所有缓存...', 'warning');
                $.get('?action=clear_level', function(data) {
                    if (data.success) {
                        addLog(`清空完成，删除了 ${data.cleared} 个文件`, 'success');
                        refreshStats();
                    } else {
                        addLog('清空失败', 'error');
                    }
                });
            }
        }
        
        // 清空指定层级缓存
        function clearLevel(level) {
            if (confirm('确定要清空此层级的缓存吗？')) {
                addLog(`开始清空层级 ${level} 的缓存...`, 'info');
                $.get(`?action=clear_level&level=${level}`, function(data) {
                    if (data.success) {
                        addLog(`层级清空完成，删除了 ${data.cleared} 个文件`, 'success');
                        refreshStats();
                    } else {
                        addLog('层级清空失败', 'error');
                    }
                });
            }
        }
        
        // 添加操作日志
        function addLog(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            const colors = {
                'info': '#17a2b8',
                'success': '#28a745', 
                'warning': '#ffc107',
                'error': '#dc3545'
            };
            
            const logHtml = `
                <div style="color: ${colors[type]}; margin-bottom: 5px;">
                    [${timestamp}] ${message}
                </div>
            `;
            
            $('#operation-log').prepend(logHtml);
            
            // 限制日志条数
            const logs = $('#operation-log > div');
            if (logs.length > 50) {
                logs.slice(50).remove();
            }
        }
    </script>
</body>
</html>
