<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php if(null !== ($page_title ?? null)): ?><?php echo $page_title ?? ""; ?> - <?php endif; ?>分类信息网站后台管理</title>
    <link href="../static/font-awesome/css/all.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="static/css/admin_clean.css?v=<?php echo time(); ?>" rel="stylesheet">
    <link href="static/css/pagination.css" rel="stylesheet">
    <link href="../static/css/image-compress.css" rel="stylesheet">
</head>
<body>
    <div class="wrapper" id="wrapper">
        <!-- 侧边栏 -->
        <div class="sidebar" id="sidebar">
            <div class="sidebar-header">
                <a href="index.php" class="logo">
                    <i class="fas fa-cube"></i>
                    <span>管理系统</span>
                </a>
            </div>
            <!-- 侧边栏菜单 -->
<div class="menu-item <?php if($current_page == 'index'): ?>active<?php endif; ?>">
    <a href="index.php">
        <i class="fas fa-home"></i>
        <span>控制面板</span>
    </a>
</div>

<div class="menu-item <?php if($current_page == 'category'): ?>active<?php endif; ?>">
    <a href="category.php">
        <i class="fas fa-list"></i>
        <span>分类管理</span>
    </a>
</div>

<div class="menu-item <?php if($current_page == 'region'): ?>active<?php endif; ?>">
    <a href="region.php">
        <i class="fas fa-map-marker-alt"></i>
        <span>区域管理</span>
    </a>
</div>

<div class="menu-item <?php if($current_page == 'info'): ?>active<?php endif; ?>">
    <a href="info.php">
        <i class="fas fa-file-alt"></i>
        <span>信息管理</span>
    </a>
</div>

<div class="menu-item <?php if($current_page == 'news_category'): ?>active<?php endif; ?>">
    <a href="news_category.php">
        <i class="fas fa-newspaper"></i>
        <span>新闻栏目</span>
    </a>
</div>

<div class="menu-item <?php if($current_page == 'news'): ?>active<?php endif; ?>">
    <a href="news.php">
        <i class="fas fa-edit"></i>
        <span>新闻管理</span>
    </a>
</div>

<div class="menu-item <?php if($current_page == 'pages'): ?>active<?php endif; ?>">
    <a href="pages.php">
        <i class="fas fa-file-alt"></i>
        <span>单页管理</span>
    </a>
</div>

<div class="menu-item <?php if($current_page == 'links'): ?>active<?php endif; ?>">
    <a href="links.php">
        <i class="fas fa-link"></i>
        <span>友情链接</span>
    </a>
</div>

<div class="menu-item <?php if($current_page == 'report'): ?>active<?php endif; ?>">
    <a href="report.php">
        <i class="fas fa-flag"></i>
        <span>举报管理</span>
    </a>
</div>

<div class="menu-item <?php if($current_page == 'admin'): ?>active<?php endif; ?>">
    <a href="admin.php">
        <i class="fas fa-user-shield"></i>
        <span>管理员管理</span>
    </a>
</div>

<div class="menu-item <?php if($current_page == 'operation_logs'): ?>active<?php endif; ?>">
    <a href="operation_logs.php">
        <i class="fas fa-history"></i>
        <span>操作日志</span>
    </a>
</div>

<div class="menu-item <?php if($current_page == 'mobile_security'): ?>active<?php endif; ?>">
    <a href="mobile_security.php">
        <i class="fas fa-shield-alt"></i>
        <span>手机号安全</span>
    </a>
</div>

<div class="menu-item <?php if($current_page == 'setting'): ?>active<?php endif; ?>">
    <a href="setting.php">
        <i class="fas fa-cog"></i>
        <span>系统设置</span>
    </a>
</div>

<div class="menu-item <?php if($current_page == 'cache_manager'): ?>active<?php endif; ?>">
    <a href="cache_manager.php">
        <i class="fas fa-memory"></i>
        <span>缓存管理</span>
    </a>
</div>



<div class="menu-item <?php if($current_page == 'db_backup'): ?>active<?php endif; ?>">
    <a href="db_backup.php">
        <i class="fas fa-database"></i>
        <span>数据库备份</span>
    </a>
</div>
        </div>

        <!-- 顶部导航 -->
<div class="top-nav">
    <div class="nav-left">
        <div class="toggle-sidebar" id="toggle-sidebar">
            <i class="fas fa-bars"></i>
        </div>
        <div class="breadcrumb">
            <span class="admin-badge"><?php echo (isset($admin['username'])) ? $admin['username'] : ""; ?></span>
            <i class="fas fa-chevron-right"></i>
            <span>控制台</span>
            <?php if(null !== ($breadcrumb ?? null)): ?>
            <i class="fas fa-chevron-right"></i>
            <span><?php echo $breadcrumb ?? ""; ?></span>
            <?php endif; ?>
        </div>
    </div>
    <div class="nav-right">
        <div class="nav-item" id="clear-cache-btn" title="清理所有缓存">
            <i class="fas fa-trash-alt"></i>
        </div>
        <div class="nav-item" title="前台首页">
            <a href="../" target="_blank" style="color:inherit;text-decoration:none;">
                <i class="fas fa-home"></i>
            </a>
        </div>
        <div class="user-item">
            <div class="user-avatar"><i class="fas fa-user"></i></div>
            <span class="user-name"><?php echo (isset($admin['username'])) ? $admin['username'] : ""; ?></span>
            <a href="logout.php" class="logout-link" title="退出登录">
                <i class="fas fa-sign-out-alt"></i>
            </a>
        </div>
    </div>
</div>

<!-- 清理缓存功能的遮罩层和对话框 -->
<div id="cache-overlay" style="display:none; position:fixed; top:0; left:0; width:100%; height:100%; background:rgba(0,0,0,0.5); z-index:2000;"></div>
<div id="cache-modal" style="display:none; position:fixed; top:50%; left:50%; transform:translate(-50%,-50%); background:#fff; border-radius:8px; box-shadow:0 4px 20px rgba(0,0,0,0.2); width:300px; padding:20px; z-index:2001;">
    <div style="margin-bottom:15px; font-size:16px; font-weight:600;">确认清理缓存</div>
    <p style="margin-bottom:20px; font-size:14px; color:#666;">此操作将清理所有缓存，包括：</p>
    <ul style="margin-bottom:20px; padding-left:20px; font-size:14px; color:#666;">
        <li>页面缓存</li>
        <li>数据缓存</li>
        <li>模板编译文件</li>
    </ul>
    <div style="display:flex; justify-content:flex-end; gap:10px;">
        <button id="cancel-clear-cache" style="padding:8px 16px; border:1px solid #ddd; background:#fff; border-radius:4px; cursor:pointer;">取消</button>
        <button id="confirm-clear-cache" style="padding:8px 16px; border:none; background:#dc3545; color:#fff; border-radius:4px; cursor:pointer;">确认清理</button>
    </div>
</div>

<!-- 成功提示框 -->
<div id="success-toast" style="display:none; position:fixed; top:50%; left:50%; transform:translate(-50%,-50%); background:#28a745; color:#fff; padding:15px 25px; border-radius:6px; box-shadow:0 4px 12px rgba(0,0,0,0.15); z-index:2002; font-size:14px;">
    <i class="fas fa-check-circle" style="margin-right:8px;"></i>
    缓存清理成功！
</div>

<script>
    document.addEventListener('DOMContentLoaded', function() {
        const clearCacheBtn = document.getElementById('clear-cache-btn');
        const cacheOverlay = document.getElementById('cache-overlay');
        const cacheModal = document.getElementById('cache-modal');
        const cancelClearCache = document.getElementById('cancel-clear-cache');
        const confirmClearCache = document.getElementById('confirm-clear-cache');
        const successToast = document.getElementById('success-toast');
        
        if (clearCacheBtn && cacheOverlay && cacheModal) {
            clearCacheBtn.addEventListener('click', function() {
                cacheOverlay.style.display = 'block';
                cacheModal.style.display = 'block';
            });
            
            cancelClearCache.addEventListener('click', function() {
                cacheOverlay.style.display = 'none';
                cacheModal.style.display = 'none';
            });
            
            cacheOverlay.addEventListener('click', function() {
                cacheOverlay.style.display = 'none';
                cacheModal.style.display = 'none';
            });
            
            confirmClearCache.addEventListener('click', function() {
                // 发送清理缓存请求
                const xhr = new XMLHttpRequest();
                xhr.open('POST', 'cache_manager.php', true);
                xhr.setRequestHeader('Content-Type', 'application/x-www-form-urlencoded');
                
                confirmClearCache.innerHTML = '清理中...';
                confirmClearCache.disabled = true;
                
                xhr.onreadystatechange = function() {
                    if (xhr.readyState === 4) {
                        cacheOverlay.style.display = 'none';
                        cacheModal.style.display = 'none';
                        
                        if (xhr.status === 200) {
                            // 显示成功提示
                            successToast.style.display = 'block';
                            successToast.style.transform = 'translate(-50%, -50%) scale(0.8)';
                            
                            setTimeout(function() {
                                successToast.style.transform = 'translate(-50%, -50%) scale(1)';
                            }, 100);
                            
                            setTimeout(function() {
                                successToast.style.transform = 'translate(-50%, -50%) scale(0.8)';
                                setTimeout(function() {
                                    successToast.style.display = 'none';
                                    successToast.style.transform = 'translate(-50%, -50%) scale(1)';
                                }, 300);
                            }, 2000);
                        }
                        
                        confirmClearCache.disabled = false;
                        confirmClearCache.innerHTML = '确认清理';
                    }
                };
                
                xhr.send('action=clear_cache&type=all');
            });
        }
    });
</script>


        <!-- 主内容区 (开始) -->
        <div class="main-content">

<script>
    document.addEventListener('DOMContentLoaded', function() {
        // 侧边栏折叠功能
        const toggleSidebar = document.getElementById('toggle-sidebar');
        const sidebar = document.getElementById('sidebar');
        const wrapper = document.getElementById('wrapper');
        
        if (toggleSidebar && sidebar && wrapper) {
            toggleSidebar.addEventListener('click', function() {
                sidebar.classList.toggle('collapsed');
                wrapper.classList.toggle('collapsed');
                localStorage.setItem('sidebar_collapsed', sidebar.classList.contains('collapsed'));
            });
            
            // 恢复侧边栏状态
            const isCollapsed = localStorage.getItem('sidebar_collapsed') === 'true';
            if (isCollapsed) {
                sidebar.classList.add('collapsed');
                wrapper.classList.add('collapsed');
            }
        }
        
        // 设置当前页面的菜单项为激活状态
        const currentPath = window.location.pathname;
        const menuItems = document.querySelectorAll('.menu-item a');
        
        menuItems.forEach(function(item) {
            const href = item.getAttribute('href');
            if (href) {
                // 检查完整路径匹配
                if (currentPath.endsWith(href)) {
                    const menuItem = item.closest('.menu-item');
                    if (menuItem) {
                        menuItem.classList.add('active');
                    }
                    
                    foundActive = true;
                }
            }
        });
        
        // 如果没有找到完全匹配的，尝试部分匹配
        if (!foundActive) {
            const pathParts = currentPath.split('/');
            const filename = pathParts[pathParts.length - 1];
            
            if (filename) {
                menuItems.forEach(function(item) {
                    const href = item.getAttribute('href');
                    if (href && href.includes(filename.split('.')[0])) {
                        const menuItem = item.closest('.menu-item');
                        if (menuItem) {
                            menuItem.classList.add('active');
                        }
                    }
                });
            }
        }
        
        // 保存当前激活的菜单项
        menuItems.forEach(function(item) {
            item.addEventListener('click', function() {
                const href = this.getAttribute('href');
                if (href) {
                    localStorage.setItem('active_menu_item', href);
                }
            });
        });
    });

    // 处理URL参数中的错误和成功消息
    function handleUrlMessages() {
        const urlParams = new URLSearchParams(window.location.search);
        const message = urlParams.get('message');
        const error = urlParams.get('error');

        if (message) {
            showSuccessMessage(message);
            // 清除URL中的message参数
            clearUrlParameter('message');
        }

        if (error) {
            showErrorMessage(error);
            // 清除URL中的error参数
            clearUrlParameter('error');
        }
    }

    // 显示成功消息
    function showSuccessMessage(message) {
        const alertHtml = `
            <div class="alert alert-success alert-dismissible fade show" role="alert" style="position: fixed; top: 70px; right: 20px; z-index: 9999; min-width: 300px; box-shadow: 0 4px 12px rgba(0,0,0,0.15);">
                <i class="fas fa-check-circle me-2"></i>
                <span>${message}</span>
                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
            </div>
        `;
        document.body.insertAdjacentHTML('beforeend', alertHtml);

        // 5秒后自动消失
        setTimeout(() => {
            const alert = document.querySelector('.alert-success');
            if (alert) {
                alert.remove();
            }
        }, 5000);
    }

    // 显示错误消息
    function showErrorMessage(message) {
        const alertHtml = `
            <div class="alert alert-danger alert-dismissible fade show" role="alert" style="position: fixed; top: 70px; right: 20px; z-index: 9999; min-width: 300px; box-shadow: 0 4px 12px rgba(0,0,0,0.15);">
                <i class="fas fa-exclamation-circle me-2"></i>
                <span>${message}</span>
                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
            </div>
        `;
        document.body.insertAdjacentHTML('beforeend', alertHtml);

        // 8秒后自动消失（错误消息显示时间稍长）
        setTimeout(() => {
            const alert = document.querySelector('.alert-danger');
            if (alert) {
                alert.remove();
            }
        }, 8000);
    }

    // 清除URL参数
    function clearUrlParameter(param) {
        const url = new URL(window.location);
        url.searchParams.delete(param);
        window.history.replaceState({}, document.title, url.toString());
    }

    // 页面加载完成后处理URL消息
    document.addEventListener('DOMContentLoaded', function() {
        handleUrlMessages();
    });
</script>


<!-- 页面标题 -->
<div class="page-title">
    <h1>系统设置</h1>
    <div class="d-flex gap-2">
        <a href="index.php" class="btn btn-outline">
            <i class="fas fa-arrow-left"></i>
            <span>返回首页</span>
        </a>
    </div>
</div>

<!-- 内容区域 -->
<div class="section">
    <!-- 消息提示 -->
    <?php if(null !== ($message ?? null) && !empty($message)): ?>
    <div class="alert alert-success">
        <i class="fas fa-check-circle"></i>
        <div>
            <p><?php echo $message ?? ""; ?></p>
        </div>
    </div>
    <?php endif; ?>

    <?php if(null !== ($debug_info ?? null) && !empty($debug_info)): ?>
    <div class="alert alert-info">
        <i class="fas fa-info-circle"></i>
        <div>
            <p>调试信息</p>
            <div><?php echo $debug_info ?? ""; ?></div>
        </div>
    </div>
    <?php endif; ?>

    <!-- 设置选项卡 -->
    <div class="setting-tabs">
        <?php if(null !== ($groups ?? null) && is_array($groups)): foreach($groups as $group_key => $group_name): ?>
        <a href="?group=<?php echo $group_key ?? ""; ?>" class="setting-tab<?php if($current_group == $group_key): ?> active<?php endif; ?>">
            <?php echo $group_name ?? ""; ?>
        </a>
        <?php endforeach; endif; ?>
    </div>

    <!-- 设置表单 -->
    <div class="card">
        <form method="post" action="" class="form-horizontal">
            <input type="hidden" name="action" value="save_settings">
            <input type="hidden" name="group" value="<?php echo $current_group ?? ""; ?>">

            <?php if(null !== ($settings ?? null) && is_array($settings)): foreach($settings as $setting): ?>
            <div class="form-group">
                <label class="form-label" for="<?php echo (isset($setting['setting_key'])) ? $setting['setting_key'] : ""; ?>"><?php echo (isset($setting['setting_title'])) ? $setting['setting_title'] : ""; ?></label>

                <div class="form-field">
                    <?php if($setting['setting_type'] == 'text'): ?>
                    <input type="text" id="<?php echo (isset($setting['setting_key'])) ? $setting['setting_key'] : ""; ?>" name="<?php echo (isset($setting['setting_key'])) ? $setting['setting_key'] : ""; ?>" value="<?php echo (isset($setting['setting_value'])) ? $setting['setting_value'] : ""; ?>" class="form-control" placeholder="请输入<?php echo (isset($setting['setting_title'])) ? $setting['setting_title'] : ""; ?>">

                    <?php elseif($setting['setting_type'] == 'number'): ?>
                    <input type="number" id="<?php echo (isset($setting['setting_key'])) ? $setting['setting_key'] : ""; ?>" name="<?php echo (isset($setting['setting_key'])) ? $setting['setting_key'] : ""; ?>" value="<?php echo (isset($setting['setting_value'])) ? $setting['setting_value'] : ""; ?>" class="form-control" min="1" step="1" placeholder="请输入数字">

                    <?php elseif($setting['setting_type'] == 'textarea'): ?>
                    <textarea id="<?php echo (isset($setting['setting_key'])) ? $setting['setting_key'] : ""; ?>" name="<?php echo (isset($setting['setting_key'])) ? $setting['setting_key'] : ""; ?>" class="form-textarea" placeholder="请输入<?php echo (isset($setting['setting_title'])) ? $setting['setting_title'] : ""; ?>"><?php echo (isset($setting['setting_value'])) ? $setting['setting_value'] : ""; ?></textarea>

                    <?php elseif($setting['setting_type'] == 'radio' || $setting['setting_type'] == 'switch'): ?>
                    <div class="d-flex gap-3">
                        <div class="form-check">
                            <input type="radio" name="<?php echo (isset($setting['setting_key'])) ? $setting['setting_key'] : ""; ?>" id="<?php echo (isset($setting['setting_key'])) ? $setting['setting_key'] : ""; ?>_1" value="1" <?php if($setting['setting_value'] == '1'): ?>checked<?php endif; ?>>
                            <label class="form-check-label" for="<?php echo (isset($setting['setting_key'])) ? $setting['setting_key'] : ""; ?>_1">启用</label>
                        </div>
                        <div class="form-check">
                            <input type="radio" name="<?php echo (isset($setting['setting_key'])) ? $setting['setting_key'] : ""; ?>" id="<?php echo (isset($setting['setting_key'])) ? $setting['setting_key'] : ""; ?>_0" value="0" <?php if($setting['setting_value'] == '0'): ?>checked<?php endif; ?>>
                            <label class="form-check-label" for="<?php echo (isset($setting['setting_key'])) ? $setting['setting_key'] : ""; ?>_0">禁用</label>
                        </div>
                    </div>

                    <?php elseif($setting['setting_type'] == 'select'): ?>
                    <select id="<?php echo (isset($setting['setting_key'])) ? $setting['setting_key'] : ""; ?>" name="<?php echo (isset($setting['setting_key'])) ? $setting['setting_key'] : ""; ?>" class="form-select">
                        <?php if($setting['setting_key'] == 'mobile_pagination_mode'): ?>
                        <option value="pagination" <?php if($setting['setting_value'] == 'pagination'): ?>selected<?php endif; ?>>传统分页模式（上一页/下一页）</option>
                        <option value="loadmore" <?php if($setting['setting_value'] == 'loadmore'): ?>selected<?php endif; ?>>点击加载更多模式</option>
                        <option value="infinite" <?php if($setting['setting_value'] == 'infinite'): ?>selected<?php endif; ?>>滚动无限加载模式</option>
                        <?php else: ?>
                        {assign var=options value=json_decode($setting.setting_options, true)}
                        <?php if(null !== ($options ?? null) && is_array($options)): foreach($options as $option_key => $option_value): ?>
                        <option value="<?php echo $option_key ?? ""; ?>" <?php if($setting['setting_value'] == $option_key): ?>selected<?php endif; ?>><?php echo $option_value ?? ""; ?></option>
                        <?php endforeach; endif; ?>
                        <?php endif; ?>
                    </select>

                    <?php elseif($setting['setting_type'] == 'checkbox'): ?>
                    <div class="d-flex flex-wrap gap-3">
                        {assign var=options value=json_decode($setting.setting_options, true)}
                        {assign var=selected_values value=explode(',', $setting.setting_value)}
                        <?php if(null !== ($options ?? null) && is_array($options)): foreach($options as $option_key => $option_value): ?>
                        <div class="form-check">
                            <input type="checkbox" name="<?php echo (isset($setting['setting_key'])) ? $setting['setting_key'] : ""; ?>[]" id="<?php echo (isset($setting['setting_key'])) ? $setting['setting_key'] : ""; ?>_<?php echo $option_key ?? ""; ?>" value="<?php echo $option_key ?? ""; ?>" <?php if(in_array($option_key, $selected_values)): ?>checked<?php endif; ?>>
                            <label class="form-check-label" for="<?php echo (isset($setting['setting_key'])) ? $setting['setting_key'] : ""; ?>_<?php echo $option_key ?? ""; ?>"><?php echo $option_value ?? ""; ?></label>
                        </div>
                        <?php endforeach; endif; ?>
                    </div>
                    <?php endif; ?>
                </div>

                <?php if(null !== ($setting ?? null) && is_array($setting) && array_key_exists('setting_description', $setting) && !empty($setting['setting_description'])): ?>
                <div class="form-help">
                    <span class="help-text"><?php echo (isset($setting['setting_description'])) ? $setting['setting_description'] : ""; ?></span>
                </div>
                <?php endif; ?>
            </div>
            <?php endforeach; endif; ?>

            <!-- 提交按钮 -->
            <div class="form-actions">
                <button type="submit" class="btn btn-primary">
                    <i class="fas fa-save"></i>
                    <span>保存设置</span>
                </button>
                <button type="reset" class="btn btn-outline">
                    <i class="fas fa-undo"></i>
                    <span>重置</span>
                </button>
            </div>
        </form>
    </div>
</div>

        </div>
        <!-- 主内容区 (结束) -->
    </div>
    <!-- wrapper (结束) -->

    <!-- jQuery (必须在Bootstrap之前加载) -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <!-- Bootstrap JavaScript -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>