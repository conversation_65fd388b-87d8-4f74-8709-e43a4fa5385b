<?php
// 定义安全常量
define('IN_BTMPS', true);

// 增强错误处理
ini_set('display_errors', 0); // 不在页面显示错误
error_reporting(E_ALL);
ini_set('log_errors', 1);
ini_set('error_log', dirname(dirname(__FILE__)) . '/data/db_backup_error.log');

// 引入公共文件
require_once('../include/common.inc.php');

// 检查管理员是否登录
if (!isset($_SESSION['admin']) || $_SESSION['admin']['is_login'] !== true) {
    header("Location: login.php");
    exit;
}

// 当前页面标识
$current_page = 'db_backup';

// 错误和成功消息
$error = '';
$message = '';

// 设置页面不超时
ignore_user_abort(true);    // 忽略客户端中断
set_time_limit(0);          // 设置脚本执行时间为不限制，但注意这仅对CLI有效，Web环境中仍受php.ini限制
ini_set('max_execution_time', 0);  // 设置最大执行时间为不限制（备用设置）
ini_set('memory_limit', '1G');     // 设置内存限制为1GB

// 创建备份基础目录
$backup_base_dir = dirname(dirname(__FILE__)) . '/data/backup/';
if (!is_dir($backup_base_dir)) {
    @mkdir($backup_base_dir, 0777, true);
    
    // 添加安全保护
    $htaccess_content = "deny from all\n";
    @file_put_contents($backup_base_dir . '.htaccess', $htaccess_content);
    
    // 添加index.html防止目录列表
    @file_put_contents($backup_base_dir . 'index.html', '<html><head><title>403 Forbidden</title></head><body><h1>403 Forbidden</h1><p>您无权访问此目录。</p></body></html>');
}

// 修改模板目录为admin/template目录
$tpl->setTemplateDir(dirname(__FILE__) . '/template/');

// 处理不同的操作
$action = isset($_GET['action']) ? $_GET['action'] : 'list';

// 每个备份目录按年月日分类存放
function get_backup_dir_by_date() {
    $year = date('Y');
    $month = date('m');
    $day = date('d');
    $time = date('His');
    
    $dir = $year . $month . $day . '_' . $time;
    return $dir;
}

switch ($action) {
    // 创建备份 - 分步执行
    case 'backup':
        // 验证CSRF令牌
        $token = isset($_POST['csrf_token']) ? $_POST['csrf_token'] : '';
        if (empty($token)) {
            $token = isset($_GET['token']) ? $_GET['token'] : '';
        }
        
        if (!verify_csrf_token($token)) {
            $error = '安全验证失败，请刷新页面重试';
            break;
        }
        
        // 获取当前步骤
        $step = isset($_GET['step']) ? intval($_GET['step']) : 0;
        $table_index = isset($_GET['table_index']) ? intval($_GET['table_index']) : 0;
        $offset = isset($_GET['offset']) ? intval($_GET['offset']) : 0;
        $backup_dir_name = isset($_GET['dir']) ? trim($_GET['dir']) : get_backup_dir_by_date();
        
        // 检查备份目录是否可写
        $backup_path = dirname(dirname(__FILE__)) . '/data/backup/' . $backup_dir_name . '/';
        if (!is_dir($backup_path)) {
            if (!@mkdir($backup_path, 0777, true)) {
                $error = '无法创建备份目录，请检查权限';
                error_log('无法创建备份目录: ' . $backup_path);
                break;
            }
        }
        
        if (!is_writable($backup_path) && !is_writable(dirname($backup_path))) {
            $error = '备份目录不可写，请检查权限';
            error_log('备份目录不可写: ' . $backup_path);
            break;
        }
        
        try {
            // 创建备份
            $result = create_backup_step($step, $table_index, $offset, $backup_dir_name);
            
            if ($result['success']) {
                if (isset($result['continue']) && $result['continue']) {
                    // 需要继续处理，跳转到下一步
                    $next_url = "db_backup.php?action=backup&step=" . $result['next_step'] . 
                               "&table_index=" . $result['next_table_index'] . 
                               "&offset=" . $result['next_offset'] . 
                               "&dir=" . urlencode($backup_dir_name) . 
                               "&token=" . urlencode($token);
                    
                    // 显示进度
                    echo '<!DOCTYPE html>
                    <html>
                    <head>
                        <meta charset="UTF-8">
                        <meta http-equiv="refresh" content="1;url=' . $next_url . '">
                        <title>备份进行中...</title>
                        <style>
                            body { font-family: Arial, sans-serif; margin: 0; padding: 20px; background: #f5f5f5; }
                            .progress-container { max-width: 800px; margin: 50px auto; background: white; padding: 30px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); border-radius: 5px; }
                            h1 { color: #333; font-size: 24px; margin-bottom: 30px; text-align: center; }
                            .progress-bar { height: 30px; background: #e9e9e9; border-radius: 15px; overflow: hidden; margin-bottom: 20px; }
                            .progress-fill { height: 100%; background: #4caf50; width: ' . min(100, floor($result['progress'])) . '%; transition: width 0.5s; }
                            .status { text-align: center; font-size: 16px; color: #555; }
                            .detail { margin-top: 20px; background: #f9f9f9; padding: 15px; border-radius: 5px; font-size: 14px; color: #777; }
                        </style>
                    </head>
                    <body>
                        <div class="progress-container">
                            <h1>数据库备份进行中</h1>
                            <div class="progress-bar">
                                <div class="progress-fill"></div>
                            </div>
                            <div class="status">已完成: ' . floor($result['progress']) . '%</div>
                            <div class="detail">
                                <p>当前进度: ' . htmlspecialchars($result['status_message']) . '</p>
                                <p>已备份: ' . $result['processed'] . ' 条记录</p>
                                <p>请勿关闭此页面，系统正在自动备份...</p>
                            </div>
                        </div>
                    </body>
                    </html>';
                    exit;
                } else {
                    // 备份完成 (continue = false)
                    $message = $result['message'];

                    // 在备份完成时添加压缩功能
                    if (true) { // 当 continue = false 时，表示备份已完成
                        // 备份已完成，压缩文件减少存储空间
                        if (class_exists('ZipArchive')) {
                            $zip = new ZipArchive();
                            $zipname = $backup_path . '../' . $backup_dir_name . '.zip';

                            if ($zip->open($zipname, ZipArchive::CREATE) === TRUE) {
                                // 添加所有备份文件到压缩包
                                $files = glob($backup_path . '*');
                                foreach ($files as $file) {
                                    if (is_file($file)) {
                                        $zip->addFile($file, basename($file));
                                    }
                                }
                                $zip->close();

                                // 可选：删除原始文件，只保留压缩包
                                // foreach ($files as $file) {
                                //     if (is_file($file)) {
                                //         @unlink($file);
                                //     }
                                // }
                            }
                        }

                        // 备份完成，重定向到列表页面显示结果
                        $_SESSION['backup_success_message'] = '数据库备份完成！备份文件：' . $backup_dir_name;

                        // 确保输出缓冲区被清空
                        if (ob_get_level()) {
                            ob_end_clean();
                        }

                        header("Location: db_backup.php");
                        exit;
                    }
                }
            } else {
                $error = $result['message'];
            }
        } catch (Exception $e) {
            error_log('备份过程发生异常: ' . $e->getMessage());
            $error = '备份过程发生错误: ' . $e->getMessage();
        }
        break;
    
    // 恢复备份
    case 'restore':
        // 延长脚本执行时间和增加内存限制
        set_time_limit(600);  // 10分钟
        ini_set('memory_limit', '512M');
        
        // 初始化变量，防止未定义错误
        $total_queries = 0;
        $success_count = 0;
        $fail_count = 0;
        
        // 确保会话有效
        if (!isset($_SESSION['admin']) || $_SESSION['admin']['is_login'] !== true) {
            header("Location: login.php");
            exit;
        }
        
        // 刷新会话，防止超时
        session_write_close();
        session_start();
        
        // 获取目录名
        $dir = isset($_GET['dir']) ? trim($_GET['dir']) : '';
        if (empty($dir) || !is_dir($backup_base_dir . $dir)) {
            $error = '无效的备份目录';
            break;
        }
        
        // 验证CSRF令牌
        $token = isset($_GET['token']) ? $_GET['token'] : '';
        if (!verify_csrf_token($token)) {
            $error = '安全验证失败，请重新操作';
            break;
        }
        
        // 获取要恢复的步骤和表
        $step = isset($_GET['step']) ? intval($_GET['step']) : 0;
        $table_index = isset($_GET['table_index']) ? intval($_GET['table_index']) : 0;
        $processed = isset($_GET['processed']) ? intval($_GET['processed']) : 0;
        
        try {
            // 恢复备份
            $result = restore_backup($dir, $step, $table_index, $processed);
            if ($result['success']) {
                if (isset($result['continue']) && $result['continue']) {
                    // 需要继续处理，跳转到下一步
                    $next_processed = isset($result['next_processed']) ? $result['next_processed'] : $result['processed'];
                    
                    $next_url = "db_backup.php?action=restore&dir=" . urlencode($dir) . 
                               "&step=" . $result['next_step'] . 
                               "&table_index=" . $result['next_table_index'] . 
                               "&processed=" . $next_processed . 
                               "&token=" . urlencode($token);
                    
                    // 使用JavaScript确保正确重定向
                    echo '<!DOCTYPE html>
                    <html>
                    <head>
                        <meta charset="UTF-8">
                        <title>恢复进行中...</title>
                        <script>
                            // 添加错误处理和重试机制
                            function redirectWithRetry() {
                                try {
                                    window.location.href = "' . $next_url . '";
                                } catch(e) {
                                    console.error("重定向失败:", e);
                                    // 3秒后重试
                                    setTimeout(redirectWithRetry, 3000);
                                }
                            }
                            // 1秒后执行重定向
                            setTimeout(redirectWithRetry, 1000);
                        </script>
                        <!-- 添加备用meta刷新,以防JavaScript失效 -->
                        <meta http-equiv="refresh" content="2;url=' . $next_url . '">
                        <style>
                            body { font-family: Arial, sans-serif; margin: 0; padding: 20px; background: #f5f5f5; }
                            .progress-container { max-width: 800px; margin: 50px auto; background: white; padding: 30px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); border-radius: 5px; }
                            h1 { color: #333; font-size: 24px; margin-bottom: 30px; text-align: center; }
                            .progress-bar { height: 30px; background: #e9e9e9; border-radius: 15px; overflow: hidden; margin-bottom: 20px; }
                            .progress-fill { height: 100%; background: #4caf50; width: ' . min(100, floor($result['progress'])) . '%; transition: width 0.5s; }
                            .status { text-align: center; font-size: 16px; color: #555; }
                            .detail { margin-top: 20px; background: #f9f9f9; padding: 15px; border-radius: 5px; font-size: 14px; color: #777; }
                        </style>
                    </head>
                    <body>
                        <div class="progress-container">
                            <h1>数据库恢复进行中</h1>
                            <div class="progress-bar">
                                <div class="progress-fill"></div>
                            </div>
                            <div class="status">已完成: ' . floor($result['progress']) . '%</div>
                            <div class="detail">
                                <p>正在恢复: ' . htmlspecialchars($result['current_table']) . '</p>
                                <p>已处理: ' . $result['processed'] . ' 条记录</p>
                                <p>请勿关闭此页面，系统正在自动恢复...</p>
                            </div>
                        </div>
                    </body>
                    </html>';
                    exit;
                } else {
                    // 恢复完成
                    $message = $result['message'];
                }
            } else {
                $error = $result['message'];
            }
        } catch (Exception $e) {
            error_log('恢复过程发生异常: ' . $e->getMessage());
            $error = '恢复过程发生错误: ' . $e->getMessage();
        }
        break;
        
    // 删除备份
    case 'delete':
        // 获取目录名
        $dir = isset($_GET['dir']) ? trim($_GET['dir']) : '';
        if (empty($dir) || !is_dir($backup_base_dir . $dir)) {
            $error = '无效的备份目录';
            break;
        }
        
        // 验证CSRF令牌
        $token = isset($_GET['token']) ? $_GET['token'] : '';
        if (!verify_csrf_token($token)) {
            $error = '安全验证失败，请重新操作';
            break;
        }
        
        try {
            // 删除备份
            $result = delete_backup($dir);
            if ($result['success']) {
                $message = $result['message'];
            } else {
                $error = $result['message'];
            }
        } catch (Exception $e) {
            error_log('删除备份时发生异常: ' . $e->getMessage());
            $error = '删除备份时发生错误: ' . $e->getMessage();
        }

        // 删除操作后重新获取备份列表
        $backups = array();
        try {
            $backups = get_backups();
        } catch (Exception $e) {
            error_log('获取备份列表发生异常: ' . $e->getMessage());
            if (empty($error)) {
                $error = '获取备份列表失败: ' . $e->getMessage();
            }
        }

        // 计算总备份大小
        $total_size = 0;
        foreach ($backups as $backup) {
            if (isset($backup['size'])) {
                $total_size += $backup['size'];
            }
        }
        $total_backup_size = format_size($total_size);

        break;
    
    // 备份列表
    case 'list':
    default:
        // 检查是否有备份成功消息
        if (isset($_SESSION['backup_success_message'])) {
            $message = $_SESSION['backup_success_message'];
            unset($_SESSION['backup_success_message']);
        }

        // 初始化备份列表
        $backups = array();

        try {
            // 获取备份列表
            $backups = get_backups();
        } catch (Exception $e) {
            error_log('获取备份列表发生异常: ' . $e->getMessage());
            $error = '获取备份列表失败: ' . $e->getMessage();
        }

        // 计算总备份大小
        $total_size = 0;
        foreach ($backups as $backup) {
            if (isset($backup['size'])) {
                $total_size += $backup['size'];
            }
        }
        $total_backup_size = format_size($total_size);



        // 确保变量总是被赋值
        $tpl->assign('backups', $backups);
        $tpl->assign('total_backup_size', $total_backup_size);
        break;
}

// 获取传递的消息
if (isset($_GET['message']) && empty($message)) {
    $message = $_GET['message'];
}

if (isset($_GET['error']) && empty($error)) {
    $error = $_GET['error'];
}

// 确保备份相关变量总是被定义
if (!isset($backups)) {
    $backups = array();
}
if (!isset($total_backup_size)) {
    $total_backup_size = '0 B';
}

// 传递数据到模板
$tpl->assign('current_page', $current_page);
$tpl->assign('breadcrumb', '数据库管理');
$tpl->assign('message', $message);
$tpl->assign('error', $error);
$tpl->assign('action', $action);
$tpl->assign('page_title', '数据库备份/恢复');
$tpl->assign('csrf_token', generate_csrf_token());
$tpl->assign('admin', $_SESSION['admin']);
$tpl->assign('backups', $backups);
$tpl->assign('total_backup_size', $total_backup_size);

// 显示模板
$tpl->display('db_backup.htm');


/**
 * 创建数据库备份 - 分步执行
 * 
 * @param int $step 当前步骤
 * @param int $table_index 当前表索引
 * @param int $offset 当前表中的偏移量
 * @param string $backup_dir_name 备份目录名
 * @return array 结果
 */
function create_backup_step($step, $table_index, $offset, $backup_dir_name) {
    global $db, $config;
    
    try {
        // 记录开始执行
        error_log("开始备份步骤: $step, 表索引: $table_index, 偏移量: $offset, 目录: $backup_dir_name");
        
        // 设置足够大的内存限制和最长执行时间
        ini_set('memory_limit', '1G');
        set_time_limit(300); // 5分钟
        
        // 生成备份路径
        $backup_path = dirname(dirname(__FILE__)) . '/data/backup/' . $backup_dir_name . '/';
        
        // 创建目录
        if (!is_dir($backup_path)) {
            if (!@mkdir($backup_path, 0777, true)) {
                throw new Exception('无法创建备份目录，请检查权限设置');
            }
            
            // 创建.htaccess文件，防止直接访问
            $htaccess = "deny from all\n";
            @file_put_contents($backup_path . '.htaccess', $htaccess);
        }
        
        // 检查目录是否可写
        if (!is_writable($backup_path)) {
            throw new Exception('备份目录不可写，请检查权限设置');
        }
        
        // 步骤1: 获取所有表并创建表结构文件
        if ($step == 0) {
            // 获取所有表
            $tables = get_all_tables();
            if (empty($tables)) {
                throw new Exception('没有找到数据表');
            }
            
            // 保存表信息到备份目录
            $tables_data = "<?php\nif (!defined('IN_BTMPS')) { exit('Access Denied'); }\nreturn " . var_export($tables, true) . ";\n";
            if (file_put_contents($backup_path . 'tables.php', $tables_data) === false) {
                throw new Exception('无法写入表信息文件');
            }
            
            // 创建表结构备份文件
            $structure_file = $backup_path . 'structure.sql.php';
            $structure_content = "<?php if (!defined('IN_BTMPS')) { exit('Access Denied'); } ?>\n";
            
            // 获取每个表的结构
            foreach ($tables as $table) {
                // 获取建表语句
                $table_structure = get_table_structure($table);
                if (empty($table_structure)) {
                    error_log("警告: 无法获取表结构: $table");
                    continue;
                }
                
                // 清理表结构数据
                $table_structure = preg_replace('/\/\*.*?\*\/\s*/s', '', $table_structure);
                $table_structure = preg_replace('/AUTO_INCREMENT=\d+\s/i', '', $table_structure);
                
                $structure_content .= "/* 表: {$table} */\n";
                $structure_content .= "DROP TABLE IF EXISTS `{$table}`;\n";
                $structure_content .= $table_structure . ";\n\n";
            }
            
            // 保存结构文件
            if (file_put_contents($structure_file, $structure_content) === false) {
                throw new Exception('无法写入表结构备份文件');
            }
            
            // 获取各表的记录数
            $table_record_counts = array();
            $total_record_estimate = 0;
            
            foreach ($tables as $table) {
                $count_result = $db->query("SELECT COUNT(*) as total FROM `{$table}`");
                if (!$count_result) {
                    error_log("警告: 无法获取表记录数: $table, 错误: " . $db->error());
                    $table_record_counts[$table] = 0;
                    continue;
                }
                
                $count_row = $db->fetch_array($count_result);
                $table_record_counts[$table] = intval($count_row['total']);
                $total_record_estimate += $table_record_counts[$table];
            }
            
            // 初始化信息文件
            $info = array(
                'start_time' => time(),
                'date' => date('Y-m-d H:i:s'),
                'tables' => $tables,
                'table_record_counts' => $table_record_counts,
                'total_records_estimate' => $total_record_estimate,
                'total_records' => 0,
                'processed_records' => 0,
                'db_name' => $config['db_name'],
                'db_version' => get_db_version(),
                'status' => 'in_progress'
            );
            
            $info_data = "<?php\nif (!defined('IN_BTMPS')) { exit('Access Denied'); }\nreturn " . var_export($info, true) . ";\n";
            if (file_put_contents($backup_path . 'info.php', $info_data) === false) {
                throw new Exception('无法写入备份信息文件');
            }
            
            // 继续下一步
            return array(
                'success' => true,
                'continue' => true,
                'next_step' => 1,
                'next_table_index' => 0,
                'next_offset' => 0,
                'progress' => 5, // 表结构占5%进度
                'status_message' => '已完成表结构备份，开始备份数据',
                'processed' => 0,
                'message' => '表结构备份完成'
            );
        }
        
        // 步骤2: 备份表数据
        if ($step == 1) {
            // 检查必要文件是否存在
            if (!file_exists($backup_path . 'tables.php') || !file_exists($backup_path . 'info.php')) {
                throw new Exception('备份文件不完整，无法继续');
            }
            
            // 获取表信息
            $tables_content = file_get_contents($backup_path . 'tables.php');
            if (empty($tables_content)) {
                throw new Exception('表信息文件为空');
            }
            
            // 安全地加载表信息
            try {
                if (!defined('IN_BTMPS')) define('IN_BTMPS', true);
                $tables = include($backup_path . 'tables.php');
                $info = include($backup_path . 'info.php');
            } catch (Exception $e) {
                throw new Exception('无法加载备份信息: ' . $e->getMessage());
            }
            
            if (!is_array($tables) || empty($tables)) {
                throw new Exception('表信息格式错误');
            }
            
            $total_tables = count($tables);
            
            // 检查是否已经处理完所有表
            if ($table_index >= $total_tables) {
                // 所有表已处理完毕
                $info['status'] = 'completed';
                $info['end_time'] = time();
                $info['total_time'] = $info['end_time'] - $info['start_time'];
                
                $info_data = "<?php\nif (!defined('IN_BTMPS')) { exit('Access Denied'); }\nreturn " . var_export($info, true) . ";\n";
                if (file_put_contents($backup_path . 'info.php', $info_data) === false) {
                    throw new Exception('无法更新备份信息文件');
                }
                
                return array(
                    'success' => true,
                    'continue' => false,
                    'message' => '备份已成功创建，共备份 ' . count($tables) . ' 个表，' . $info['total_records'] . ' 条记录',
                );
            }
            
            // 获取当前表
            $current_table = $tables[$table_index];
            error_log("处理表: $current_table, 偏移量: $offset");
            
            // 获取表中的记录总数
            if ($offset == 0) {
                try {
                    $count_result = $db->query("SELECT COUNT(*) as total FROM `{$current_table}`");
                    if (!$count_result) {
                        throw new Exception('无法获取表记录数: ' . $db->error());
                    }
                    
                    $count_row = $db->fetch_array($count_result);
                    $total_rows = intval($count_row['total']);
                    
                    // 双重检查 - 如果COUNT(*)返回0，再尝试使用LIMIT 1检查是否真的为空
                    if ($total_rows == 0) {
                        $check_result = $db->query("SELECT 1 FROM `{$current_table}` LIMIT 1");
                        if ($check_result && $db->fetch_array($check_result)) {
                            $total_rows = 1; // 至少有一条记录
                            error_log("表 $current_table 使用COUNT(*)返回0，但实际有数据");
                        }
                    }
                } catch (Exception $e) {
                    error_log("获取表 $current_table 记录数失败: " . $e->getMessage());
                    
                    // 如果计数查询失败，尝试检查表是否确实为空
                    try {
                        $check_result = $db->query("SELECT 1 FROM `{$current_table}` LIMIT 1");
                        if ($check_result && $db->fetch_array($check_result)) {
                            $total_rows = 1; // 至少有一条记录
                            error_log("表 $current_table 无法获取记录总数，但确认有数据");
                        } else {
                            $total_rows = 0;
                        }
                    } catch (Exception $e2) {
                        error_log("检查表 $current_table 数据也失败: " . $e2->getMessage());
                        $total_rows = 1; // 保险起见，假定有数据
                    }
                }
                
                // 如果表为空，跳到下一个表
                if ($total_rows == 0) {
                    error_log("表 $current_table 为空，跳过");
                    return array(
                        'success' => true,
                        'continue' => true,
                        'next_step' => 1,
                        'next_table_index' => $table_index + 1,
                        'next_offset' => 0,
                        'progress' => 5 + 95 * ($table_index + 1) / $total_tables,
                        'status_message' => '表 ' . $current_table . ' 为空，跳过',
                        'processed' => $info['processed_records'],
                        'message' => '跳过空表: ' . $current_table
                    );
                }
                
                // 创建表数据文件
                $data_file = $backup_path . $current_table . '.sql.php';
                $file_header = "<?php if (!defined('IN_BTMPS')) { exit('Access Denied'); } ?>\n";
                
                if (file_put_contents($data_file, $file_header) === false) {
                    throw new Exception('无法创建表数据文件: ' . $current_table);
                }
            } else {
                // 如果是继续处理，从之前的状态恢复
                try {
                    $count_result = $db->query("SELECT COUNT(*) as total FROM `{$current_table}`");
                    if (!$count_result) {
                        throw new Exception('无法获取表记录数: ' . $db->error());
                    }
                    
                    $count_row = $db->fetch_array($count_result);
                    $total_rows = intval($count_row['total']);
                } catch (Exception $e) {
                    error_log("获取表 $current_table 记录数失败: " . $e->getMessage());
                    $total_rows = 0;
                }
            }
            
            // 第一步优化数据查询批量大小，根据表大小动态调整
            if ($total_rows > 100000) {
                $batch_size = 5000; // 大表使用更大批次，原来是100
            } else {
                $batch_size = 2000; // 默认批量增加，原来是200
            }
            
            // 查询数据
            $sql = "SELECT * FROM `{$current_table}` LIMIT {$offset}, {$batch_size}";
            $result = $db->query($sql);
            
            if (!$result) {
                throw new Exception('查询表 ' . $current_table . ' 数据失败: ' . $db->error());
            }
            
            // 构建INSERT语句时使用批量插入
            $insert_data = '';
            $records_processed = 0;
            $memory_flush_count = 500; // 增加到500，原来是100
            $row_count = 0;
            $current_insert = '';
            
            // 使用批量插入优化
            while ($row = $db->fetch_array($result)) {
                $records_processed++;
                
                // 第一行时构建INSERT前缀
                if ($row_count == 0) {
                    $current_insert = "INSERT INTO `{$current_table}` VALUES ";
                }
                
                // 构建值部分
                $values = array();
                foreach ($row as $field => $value) {
                    if ($value === null) {
                        $values[] = 'NULL';
                    } else {
                        $values[] = "'" . addslashes($value) . "'";
                    }
                }
                
                if ($row_count > 0) {
                    $current_insert .= ", ";
                }
                $current_insert .= "(" . implode(', ', $values) . ")";
                $row_count++;
                
                // 达到50行后结束当前INSERT语句
                if ($row_count >= 50) {
                    $insert_data .= $current_insert . ";\n";
                    $row_count = 0;
                }
                
                // 定期写入文件减少内存占用
                if ($records_processed % $memory_flush_count == 0) {
                    if (file_put_contents($backup_path . $current_table . '.sql.php', $insert_data, FILE_APPEND) === false) {
                        throw new Exception('写入表数据失败: ' . $current_table);
                    }
                    $insert_data = '';
                    
                    // 强制释放内存
                    if (function_exists('gc_collect_cycles')) {
                        gc_collect_cycles();
                    }
                }
            }
            
            // 处理最后一批不满50行的数据
            if ($row_count > 0) {
                $insert_data .= $current_insert . ";\n";
            }
            
            // 确保所有数据都被写入文件
            if (!empty($insert_data)) {
                if (file_put_contents($backup_path . $current_table . '.sql.php', $insert_data, FILE_APPEND) === false) {
                    throw new Exception('写入最后一批表数据失败: ' . $current_table);
                }
                $insert_data = '';
            }
            
            // 检查文件大小确保数据已写入
            $file_size = filesize($backup_path . $current_table . '.sql.php');
            if ($file_size <= strlen("<?php if (!defined('IN_BTMPS')) { exit('Access Denied'); } ?>\n")) {
                // 文件基本上是空的，只有PHP标记
                error_log("警告: 表 $current_table 的数据文件过小 ($file_size 字节)，可能没有成功写入数据");
                
                // 确认表是否真的为空
                $check_sql = "SELECT COUNT(*) as total FROM `{$current_table}`";
                $check_result = $db->query($check_sql);
                if ($check_result) {
                    $check_row = $db->fetch_array($check_result);
                    $actual_rows = intval($check_row['total']);
                    if ($actual_rows > 0) {
                        error_log("错误: 表 $current_table 实际有 $actual_rows 条记录，但数据文件几乎为空");
                    }
                }
            }
            
            // 尝试启用DB缓存，减少重复查询（如果支持的话）
            // 在MySQL 8.0+中，查询缓存已被移除，所以我们需要安全地处理这个错误
            try {
                // 首先检查是否支持查询缓存
                $cache_check = $db->query("SHOW VARIABLES LIKE 'query_cache_type'");
                if ($cache_check && $db->num_rows($cache_check) > 0) {
                    $db->query("SET SESSION query_cache_type = 1");
                }
            } catch (Exception $e) {
                // 忽略查询缓存错误，继续执行备份
                // 这在MySQL 8.0+中是正常的，因为查询缓存功能已被移除
            }
            
            // 优化数据库连接
            $db->query("SET SESSION wait_timeout = 600");
            $db->query("SET SESSION net_read_timeout = 600");
            $db->query("SET SESSION net_write_timeout = 600");
            
            // 更新信息文件
            $info['processed_records'] += $records_processed;
            $info['total_records'] += $records_processed;
            
            $info_data = "<?php\nif (!defined('IN_BTMPS')) { exit('Access Denied'); }\nreturn " . var_export($info, true) . ";\n";
            if (file_put_contents($backup_path . 'info.php', $info_data) === false) {
                throw new Exception('无法更新备份信息文件');
            }
            
            // 计算进度百分比
            $overall_progress = 5; // 表结构占5%
            if ($info['total_records_estimate'] > 0) {
                // 基于已处理的记录数计算进度
                $data_progress = ($info['processed_records'] / $info['total_records_estimate']) * 95;
                $overall_progress += $data_progress;
            } else {
                // 如果没有总记录数估计，则使用已处理的表数量计算进度
                $overall_progress += 95 * ($table_index / $total_tables);
            }
            
            // 控制进度不超过100%
            $overall_progress = min(99, $overall_progress);
            
            // 检查是否已处理完当前表
            $new_offset = $offset + $batch_size;
            if ($new_offset >= $total_rows) {
                // 当前表已处理完毕，继续下一个表
                error_log("表 $current_table 备份完成，共 $records_processed 条记录");
                return array(
                    'success' => true,
                    'continue' => true,
                    'next_step' => 1,
                    'next_table_index' => $table_index + 1,
                    'next_offset' => 0,
                    'progress' => $overall_progress,
                    'status_message' => '表 ' . $current_table . ' 的数据已备份完成 (' . $records_processed . ' 条记录)',
                    'processed' => $info['processed_records'],
                    'message' => '表 ' . $current_table . ' 的数据已备份'
                );
            } else {
                // 继续处理当前表的下一批数据
                error_log("表 $current_table 处理中，已处理 $new_offset/$total_rows 条记录");
                return array(
                    'success' => true,
                    'continue' => true,
                    'next_step' => 1,
                    'next_table_index' => $table_index,
                    'next_offset' => $new_offset,
                    'progress' => $overall_progress,
                    'status_message' => '正在备份表 ' . $current_table . ' 的数据 (' . $new_offset . '/' . $total_rows . ')',
                    'processed' => $info['processed_records'],
                    'message' => '正在备份表 ' . $current_table . ' 的数据'
                );
            }
        }
        
        // 未知步骤
        throw new Exception('未知的备份步骤');
    } catch (Exception $e) {
        // 记录错误日志
        error_log('备份创建失败: ' . $e->getMessage() . "\n" . $e->getTraceAsString());
        
        return array(
            'success' => false,
            'message' => '备份创建失败: ' . $e->getMessage()
        );
    }
}

/**
 * 释放内存
 */
function free_memory() {
    // 清除非必要变量
    $GLOBALS['CLEANUP_GLOBALS'] = array();
    foreach ($GLOBALS as $key => $val) {
        if ($key != 'GLOBALS' && $key != '_SERVER' && $key != '_POST' && $key != '_GET' && 
            $key != '_FILES' && $key != '_COOKIE' && $key != '_SESSION' && $key != '_REQUEST' && 
            $key != '_ENV' && $key != 'db' && $key != 'tpl' && $key != 'config' && 
            $key != 'CLEANUP_GLOBALS') {
            $GLOBALS['CLEANUP_GLOBALS'][$key] = $key;
        }
    }
    foreach ($GLOBALS['CLEANUP_GLOBALS'] as $key) {
        unset($GLOBALS[$key]);
    }
    unset($GLOBALS['CLEANUP_GLOBALS']);
    
    // 强制垃圾回收
    if (function_exists('gc_collect_cycles')) {
        gc_collect_cycles();
    }
}

/**
 * 获取表结构
 * 
 * @param string $table 表名
 * @return string 建表语句
 */
function get_table_structure($table) {
    global $db;
    
    $result = $db->query("SHOW CREATE TABLE `{$table}`");
    if (!$result) {
        return '';
    }
    
    $row = $db->fetch_array($result);
    if (!$row || !isset($row['Create Table'])) {
        return '';
    }
    
    return $row['Create Table'];
}

/**
 * 获取所有表
 * 
 * @return array 表名数组
 */
function get_all_tables() {
    global $db, $config;
    
    $tables = array();
    $db_name = $config['db_name'];
    
    $result = $db->query("SHOW TABLES FROM `{$db_name}`");
    if (!$result) {
        return $tables;
    }
    
    while ($row = $db->fetch_array($result)) {
        $tables[] = $row["Tables_in_{$db_name}"];
    }
    
    return $tables;
}

/**
 * 获取数据库版本
 * 
 * @return string 数据库版本
 */
function get_db_version() {
    global $db;
    
    $result = $db->query("SELECT VERSION() as version");
    if (!$result) {
        return 'Unknown';
    }
    
    $row = $db->fetch_array($result);
    if (!$row || !isset($row['version'])) {
        return 'Unknown';
    }
    
    return $row['version'];
}

/**
 * 获取备份列表
 * 
 * @return array 备份列表
 */
function get_backups() {
    $backup_dir = dirname(dirname(__FILE__)) . '/data/backup/';
    $backups = array();



    if (!is_dir($backup_dir)) {
        error_log('备份目录不存在: ' . $backup_dir);
        return $backups;
    }
    
    $dirs = @scandir($backup_dir);
    if ($dirs === false) {
        error_log('无法读取备份目录: ' . $backup_dir);
        return $backups;
    }
    
    foreach ($dirs as $dir) {
        if ($dir == '.' || $dir == '..' || $dir == '.htaccess' || $dir == 'index.html') {
            continue;
        }
        
        $path = $backup_dir . $dir;
        
        if (is_dir($path)) {
            // 获取备份信息
            $info_file = $path . '/info.php';
            
            if (file_exists($info_file)) {
                try {
                    // 确保常量已定义
                    if (!defined('IN_BTMPS')) {
                        define('IN_BTMPS', true);
                    }

                    // 读取备份信息
                    $info = include($info_file);

                    if (!is_array($info)) {
                        error_log('备份信息格式错误: ' . $info_file);
                        continue;
                    }


                    
                    $info['dir'] = $dir;
                    $info['size'] = get_dir_size($path);
                    $info['formatted_size'] = format_size($info['size']);
                    
                    // 兼容新旧版本的时间字段
                    if (!isset($info['time']) && isset($info['start_time'])) {
                        $info['time'] = $info['start_time'];
                    } elseif (!isset($info['time']) && !isset($info['start_time'])) {
                        // 如果两个字段都不存在，使用目录名中的时间
                        if (preg_match('/^(\d{4})(\d{2})(\d{2})_(\d{2})(\d{2})(\d{2})$/', $dir, $matches)) {
                            $timestamp = mktime($matches[4], $matches[5], $matches[6], $matches[2], $matches[3], $matches[1]);
                            $info['time'] = $timestamp;
                            $info['date'] = date('Y-m-d H:i:s', $timestamp);
                        } else {
                            $info['time'] = @filemtime($path); // 使用目录修改时间作为备份时间
                            $info['date'] = date('Y-m-d H:i:s', $info['time']);
                        }
                    }
                    
                    // 确保必要的字段存在
                    if (!isset($info['db_name'])) $info['db_name'] = '-';
                    if (!isset($info['total_records'])) $info['total_records'] = 0;
                    if (!isset($info['date'])) $info['date'] = date('Y-m-d H:i:s', $info['time']);

                    // 读取表信息
                    $tables_file = $path . '/tables.php';
                    if (file_exists($tables_file)) {
                        try {
                            $tables = include($tables_file);
                            if (is_array($tables)) {
                                $info['tables'] = $tables;
                                $info['table_count'] = count($tables);
                            } else {
                                $info['tables'] = array();
                                $info['table_count'] = 0;
                            }
                        } catch (Exception $e) {
                            error_log('读取表信息文件出错: ' . $tables_file . ', 错误: ' . $e->getMessage());
                            $info['tables'] = array();
                            $info['table_count'] = 0;
                        }
                    } else {
                        $info['tables'] = array();
                        $info['table_count'] = 0;
                    }

                    // 添加格式化的日期显示
                    if (isset($info['time'])) {
                        $info['formatted_date'] = date('m-d H:i', $info['time']);
                    } else {
                        // 如果没有时间戳，尝试从日期字符串中提取
                        if (isset($info['date']) && preg_match('/(\d{4})-(\d{2})-(\d{2}) (\d{2}):(\d{2})/', $info['date'], $matches)) {
                            $info['formatted_date'] = $matches[2] . '-' . $matches[3] . ' ' . $matches[4] . ':' . $matches[5];
                        } else {
                            $info['formatted_date'] = '未知时间';
                        }
                    }
                    
                    $backups[] = $info;
                } catch (Exception $e) {
                    error_log('读取备份信息出错: ' . $info_file . ', 错误: ' . $e->getMessage());
                }
            } else {
                // 没有信息文件，尝试通过目录名解析
                if (preg_match('/^(\d{4})(\d{2})(\d{2})_(\d{2})(\d{2})(\d{2})$/', $dir, $matches)) {
                    $timestamp = mktime($matches[4], $matches[5], $matches[6], $matches[2], $matches[3], $matches[1]);
                    $info = array(
                        'time' => $timestamp,
                        'date' => date('Y-m-d H:i:s', $timestamp),
                        'formatted_date' => date('m-d H:i', $timestamp),
                        'tables' => array(),
                        'total_records' => 0,
                        'db_name' => '-',
                        'dir' => $dir,
                        'size' => get_dir_size($path),
                        'formatted_size' => format_size(get_dir_size($path))
                    );
                    $backups[] = $info;
                }
            }
        }
    }
    
    // 按时间倒序排序
    usort($backups, function($a, $b) {
        if (!isset($a['time'])) $a['time'] = 0;
        if (!isset($b['time'])) $b['time'] = 0;
        return $b['time'] - $a['time'];
    });



    return $backups;
}

/**
 * 获取目录大小
 * 
 * @param string $dir 目录路径
 * @return int 目录大小（字节）
 */
function get_dir_size($dir) {
    $size = 0;
    
    if (!is_dir($dir)) {
        return $size;
    }
    
    try {
        $files = glob(rtrim($dir, '/') . '/*', GLOB_NOSORT);
        if ($files === false) {
            return $size;
        }
        
        foreach ($files as $file) {
            if (is_file($file)) {
                $size += @filesize($file);
            } else if (is_dir($file)) {
                $size += get_dir_size($file);
            }
        }
    } catch (Exception $e) {
        error_log('获取目录大小出错: ' . $dir . ', 错误: ' . $e->getMessage());
    }
    
    return $size;
}

/**
 * 格式化文件大小
 * 
 * @param int $size 文件大小（字节）
 * @return string 格式化后的大小
 */
function format_size($size) {
    $units = array('B', 'KB', 'MB', 'GB', 'TB');
    
    $i = 0;
    while ($size >= 1024 && $i < count($units) - 1) {
        $size /= 1024;
        $i++;
    }
    
    return round($size, 2) . ' ' . $units[$i];
}

/**
 * 恢复备份
 * 
 * @param string $dir 备份目录名
 * @param int $step 当前步骤
 * @param int $table_index 当前表索引
 * @param int $processed 已处理的记录数
 * @return array 恢复结果
 */
function restore_backup($dir, $step, $table_index, $processed) {
    global $db, $config;
    
    try {
        // 记录开始执行
        error_log("开始恢复步骤: $step, 表索引: $table_index, 已处理: $processed, 目录: $dir");
        
        // 初始化变量，防止未定义错误
        $total_queries = 0;
        $success_count = 0;
        $fail_count = 0;
        
        // 设置足够大的内存限制和执行时间
        ini_set('memory_limit', '1G');
        set_time_limit(600); // 10分钟
        
        // 确保会话保持活跃
        if (session_status() === PHP_SESSION_ACTIVE) {
            session_write_close();
        }
        session_start();
        $_SESSION['last_activity'] = time();
        session_write_close();
        
        // 备份路径
        $backup_path = dirname(dirname(__FILE__)) . '/data/backup/' . $dir . '/';
        
        // 检查备份目录是否存在
        if (!is_dir($backup_path)) {
            throw new Exception('备份目录不存在: ' . $backup_path);
        }
        
        // 步骤1: 恢复表结构
        if ($step == 0) {
            // 检查表结构文件是否存在
            $structure_file = $backup_path . 'structure.sql.php';
            if (!file_exists($structure_file)) {
                throw new Exception('表结构文件不存在: ' . $structure_file);
            }
            
            // 读取表结构文件内容
            $content = file_get_contents($structure_file);
            if (empty($content)) {
                throw new Exception('表结构文件为空');
            }
            
            // 移除PHP标记
            $content = preg_replace('/\<\?php.*?\?\>/s', '', $content);
            
            // 分割SQL语句 - 使用分号加换行符为分隔符
            $queries = explode(";\n", $content);
            $queries = array_filter($queries, 'trim');
            
            // 禁用外键检查以避免约束问题
            $db->query('SET FOREIGN_KEY_CHECKS = 0');
            
            // 执行SQL语句
            $executed = 0;
            foreach ($queries as $query) {
                $query = trim($query);
                if (empty($query)) continue;
                
                try {
                    $result = $db->query($query);
                    if ($result === false) {
                        error_log('SQL执行错误: ' . $query . ' - ' . $db->error());
                    } else {
                        $executed++;
                    }
                } catch (Exception $e) {
                    error_log('执行SQL语句时发生错误: ' . $query . ' - ' . $e->getMessage());
                }
            }
            
            // 启用外键检查
            $db->query('SET FOREIGN_KEY_CHECKS = 1');
            
            error_log("表结构已恢复，共执行 $executed 条SQL语句");
            
            // 继续下一步
            return array(
                'success' => true,
                'continue' => true,
                'next_step' => 1,
                'next_table_index' => 0,
                'processed' => 0,
                'progress' => 10, // 表结构占10%
                'current_table' => '表结构',
                'status_message' => '已恢复表结构，开始恢复数据',
                'message' => '表结构已恢复'
            );
        }
        
        // 步骤2: 恢复表数据
        if ($step == 1) {
            // 检查必要文件是否存在
            if (!file_exists($backup_path . 'tables.php') || !file_exists($backup_path . 'info.php')) {
                throw new Exception('备份文件不完整，无法继续恢复');
            }
            
            // 获取表信息
            if (!defined('IN_BTMPS')) define('IN_BTMPS', true);
            $tables = include($backup_path . 'tables.php');
            $info = include($backup_path . 'info.php');
            
            if (!is_array($tables) || empty($tables)) {
                throw new Exception('表信息格式错误或为空');
            }
            
            $total_tables = count($tables);
            
            // 检查是否已经处理完所有表
            if ($table_index >= $total_tables) {
                // 所有表已处理完毕，更新恢复信息
                error_log("所有表数据已恢复完成");
                return array(
                    'success' => true,
                    'continue' => false,
                    'message' => '恢复已成功完成，共恢复 ' . count($tables) . ' 个表',
                    'progress' => 100,
                    'current_table' => '完成',
                    'processed' => $processed
                );
            }
            
            // 获取当前表
            $current_table = $tables[$table_index];
            error_log("当前处理表: $current_table");
            
            // 检查数据文件是否存在
            $data_file = $backup_path . $current_table . '.sql.php';
            if (!file_exists($data_file)) {
                error_log("表 $current_table 的数据文件不存在，跳过");
                
                // 跳到下一个表
                return array(
                    'success' => true,
                    'continue' => true,
                    'next_step' => 1,
                    'next_table_index' => $table_index + 1,
                    'processed' => $processed,
                    'progress' => 10 + 90 * (($table_index + 1) / $total_tables),
                    'current_table' => $current_table,
                    'status_message' => '表 ' . $current_table . ' 没有数据，跳过',
                    'message' => '跳过表: ' . $current_table
                );
            }
            
            // 获取SQL内容
            $sql_content = file_get_contents($data_file);
            if (empty($sql_content)) {
                error_log("表 $current_table 的数据文件为空，跳过");
                
                // 跳到下一个表
                return array(
                    'success' => true,
                    'continue' => true,
                    'next_step' => 1,
                    'next_table_index' => $table_index + 1,
                    'processed' => $processed,
                    'progress' => 10 + 90 * (($table_index + 1) / $total_tables),
                    'current_table' => $current_table,
                    'status_message' => '表 ' . $current_table . ' 没有数据，跳过',
                    'message' => '跳过表: ' . $current_table
                );
            }
            
            // 移除PHP标记
            $sql_content = preg_replace('/\<\?php.*?\?\>/s', '', $sql_content);
            
            // 尝试多种分隔符来查找SQL语句
            $queries = [];
            
            // 方法1: 使用分号加换行作为分隔符(标准方式)
            $queries_1 = explode(";\n", $sql_content);
            $queries_1 = array_filter($queries_1, 'trim');
            
            // 方法2: 使用分号作为分隔符(兼容没有换行的情况)
            $queries_2 = explode(";", $sql_content);
            $queries_2 = array_filter($queries_2, 'trim');
            
            // 使用数量较多的那一组结果
            if (count($queries_1) >= count($queries_2)) {
                $queries = $queries_1;
            } else {
                $queries = $queries_2;
            }
            
            // 检查结果中是否包含完整的INSERT语句
            $valid_sql_count = 0;
            foreach ($queries as $query) {
                if (stripos(trim($query), 'INSERT INTO') === 0) {
                    $valid_sql_count++;
                }
            }
            
            // 确保立即设置total_queries为有效的SQL语句数量
            $total_queries = $valid_sql_count > 0 ? count($queries) : 0;
            
            if ($total_queries == 0) {
                error_log("表 $current_table 没有有效的SQL语句，跳过 (文件大小: " . filesize($data_file) . " 字节)");
                
                // 跳到下一个表，但不清空当前表，避免数据丢失
                return array(
                    'success' => true,
                    'continue' => true,
                    'next_step' => 1,
                    'next_table_index' => $table_index + 1,
                    'processed' => $processed,
                    'progress' => 10 + 90 * (($table_index + 1) / $total_tables),
                    'current_table' => $current_table,
                    'status_message' => '表 ' . $current_table . ' 没有有效数据，保留原表数据',
                    'message' => '保留表: ' . $current_table
                );
            }
            
            // 开始执行 - 设置数据库环境
            $db->query('SET FOREIGN_KEY_CHECKS = 0'); // 禁用外键检查
            $db->query('SET SQL_MODE = ""'); // 使用兼容模式
            
            // 先清空表 - 只在第一次处理该表时执行
            if ($processed == 0) {
                error_log("清空表: $current_table");
                
                // 先检查表数据文件的大小和SQL数量，避免清空有数据但读取失败的表
                $file_size = filesize($data_file);
                $has_data = $file_size > 100; // 如果文件大于100字节，很可能有数据
                
                if ($has_data && $total_queries > 0) {
                    try {
                        $db->query("TRUNCATE TABLE `{$current_table}`");
                    } catch (Exception $e) {
                        error_log("TRUNCATE表失败，尝试使用DELETE: " . $e->getMessage());
                        try {
                            $db->query("DELETE FROM `{$current_table}`");
                        } catch (Exception $e2) {
                            error_log("DELETE也失败，继续尝试恢复: " . $e2->getMessage());
                        }
                    }
                } else {
                    error_log("表 $current_table 疑似有数据但检测为空，不清空表数据。文件大小: $file_size 字节, SQL语句数: $total_queries");
                }
            }
            
            // 设置批处理参数
            $batch_size = 100; // 每批处理10条SQL语句
            $start = $processed;
            $end = min($start + $batch_size, $total_queries);
            
            // 执行SQL语句 - 重置计数器
            $success_count = 0;
            $fail_count = 0;
            
            for ($i = $start; $i < $end; $i++) {
                $query = trim($queries[$i]);
                if (empty($query)) continue;
                
                try {
                    // 执行带错误检查的SQL
                    $result = $db->query($query);
                    if ($result === false) {
                        error_log("SQL执行错误 #$i: " . substr($query, 0, 150) . "... - " . $db->error());
                        $fail_count++;
                    } else {
                        $success_count++;
                    }
                } catch (Exception $e) {
                    error_log("SQL异常 #$i: " . substr($query, 0, 150) . "... - " . $e->getMessage());
                    $fail_count++;
                }
                
                // 每5条提交一次事务
                if (($i - $start) % 5 == 0) {
                    $db->query('COMMIT');
                }
                
                // 释放内存
                if (($i - $start) % 5 == 0 && function_exists('gc_collect_cycles')) {
                    gc_collect_cycles();
                }
            }
            
            // 最后提交
            $db->query('COMMIT');
            
            // 恢复数据库设置
            $db->query('SET FOREIGN_KEY_CHECKS = 1');
            
            // 在日志记录中使用已保证定义的变量
            error_log("表 $current_table 批处理完成：{$start}-{$end}/{$total_queries}，成功: {$success_count}，失败: {$fail_count}");
            
            // 检查是否完成当前表
            if ($end >= $total_queries) {
                // 当前表处理完毕，继续下一个表
                error_log("表 $current_table 恢复完成");
                return array(
                    'success' => true,
                    'continue' => true,
                    'next_step' => 1,
                    'next_table_index' => $table_index + 1,
                    'next_processed' => 0,
                    'processed' => $processed + ($end - $start),
                    'progress' => 10 + 90 * (($table_index + 1) / $total_tables),
                    'current_table' => $current_table,
                    'status_message' => '表 ' . $current_table . ' 的数据已恢复完成',
                    'message' => '表 ' . $current_table . ' 的数据已恢复'
                );
            } else {
                // 继续处理当前表的下一批数据
                error_log("表 $current_table 处理中: $end/$total_queries");
                return array(
                    'success' => true,
                    'continue' => true,
                    'next_step' => 1,
                    'next_table_index' => $table_index,
                    'next_processed' => $end,
                    'processed' => $processed + ($end - $start),
                    'progress' => 10 + 90 * (($table_index + ($end / $total_queries)) / $total_tables),
                    'current_table' => $current_table,
                    'status_message' => '正在恢复表 ' . $current_table . ' 的数据 (' . $end . '/' . $total_queries . ')',
                    'message' => '正在恢复表 ' . $current_table . ' 的数据'
                );
            }
        }
        
        // 未知步骤
        throw new Exception('未知的恢复步骤');
    } catch (Exception $e) {
        // 记录错误日志
        error_log('恢复失败: ' . $e->getMessage() . "\n" . $e->getTraceAsString());
        
        // 返回错误结果
        return array(
            'success' => false,
            'message' => '恢复失败: ' . $e->getMessage()
        );
    }
}

/**
 * 删除备份
 * 
 * @param string $dir 备份目录名
 * @return array 删除结果
 */
function delete_backup($dir) {
    try {
        // 安全检查，防止目录遍历
        if (strpos($dir, '..') !== false || strpos($dir, '/') !== false || strpos($dir, '\\') !== false) {
            throw new Exception('非法的备份目录名');
        }
        
        $backup_dir = dirname(dirname(__FILE__)) . '/data/backup/' . $dir;
        
        if (!is_dir($backup_dir)) {
            throw new Exception('备份目录不存在');
        }
        
        error_log('开始删除备份目录: ' . $backup_dir);
        
        // 递归删除目录
        if (!delete_directory($backup_dir)) {
            throw new Exception('备份目录删除失败，请检查文件权限');
        }
        
        return array(
            'success' => true,
            'message' => '备份已成功删除'
        );
    } catch (Exception $e) {
        error_log('删除备份失败: ' . $e->getMessage());
        return array(
            'success' => false,
            'message' => '备份删除失败: ' . $e->getMessage()
        );
    }
}

/**
 * 递归删除目录
 * 
 * @param string $dir 目录路径
 * @return bool 是否成功删除
 */
function delete_directory($dir) {
    if (!file_exists($dir)) {
        return true;
    }
    
    if (!is_dir($dir)) {
        return unlink($dir);
    }
    
    $items = scandir($dir);
    if ($items === false) {
        error_log('无法读取目录内容: ' . $dir);
        return false;
    }
    
    foreach ($items as $item) {
        if ($item == '.' || $item == '..') {
            continue;
        }
        
        $path = $dir . DIRECTORY_SEPARATOR . $item;
        
        if (is_dir($path)) {
            if (!delete_directory($path)) {
                error_log('无法删除子目录: ' . $path);
                return false;
            }
        } else {
            if (!@unlink($path)) {
                error_log('无法删除文件: ' . $path);
                return false;
            }
        }
    }
    
    return @rmdir($dir);
} 