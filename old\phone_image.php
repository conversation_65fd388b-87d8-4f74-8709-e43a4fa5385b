<?php
/**
 * 电话号码图片生成器
 * 将电话号码转换为图片显示，防止爬虫收集
 */

// 防止直接访问
if (!isset($_GET['phone']) || empty($_GET['phone'])) {
    header("HTTP/1.0 403 Forbidden");
    exit;
}

// 获取电话号码参数并进行基本过滤
$phone = preg_replace('/[^0-9\-\+]/', '', $_GET['phone']);

// 如果过滤后为空，返回错误
if (empty($phone)) {
    header("HTTP/1.0 400 Bad Request");
    exit;
}

// 获取字体大小参数，默认为20
$fontSize = isset($_GET['size']) ? intval($_GET['size']) : 20;
// 限制字体大小范围
$fontSize = max(12, min(30, $fontSize));

// 基本参数
$paddingLeft = 0; // 左侧无内边距
$paddingRight = 2; // 右侧少量内边距
$paddingTop = 2; // 上下内边距
$paddingBottom = 2;
$bgColor = array(255, 255, 255); // 白色背景
$textColor = array(0, 0, 0); // 黑色文字，提高对比度

// 尝试使用TTF字体文件，如果不存在则使用内置字体
$fontFile = __DIR__ . '/static/fonts/arial.ttf';
$useFont = file_exists($fontFile);

if ($useFont) {
    // 使用TTF字体计算尺寸
    $bbox = imagettfbbox($fontSize, 0, $fontFile, $phone);
    $textWidth = $bbox[4] - $bbox[0];
    $textHeight = $bbox[1] - $bbox[7];
    $width = $textWidth + $paddingLeft + $paddingRight;
    $height = abs($textHeight) + $paddingTop + $paddingBottom;
} else {
    // 使用内置字体计算尺寸
    $font = 5; // 内置字体最大值
    $textWidth = imagefontwidth($font) * strlen($phone);
    $textHeight = imagefontheight($font);
    $width = $textWidth + $paddingLeft + $paddingRight;
    $height = $textHeight + $paddingTop + $paddingBottom;
}

// 创建图像
$image = imagecreatetruecolor($width, $height);
$bg = imagecolorallocate($image, $bgColor[0], $bgColor[1], $bgColor[2]);
$text = imagecolorallocate($image, $textColor[0], $textColor[1], $textColor[2]);

// 填充背景
imagefilledrectangle($image, 0, 0, $width, $height, $bg);

// 绘制电话号码文本
if ($useFont) {
    // 使用TTF字体，文字贴左显示
    imagettftext($image, $fontSize, 0, $paddingLeft, $height - $paddingBottom, $text, $fontFile, $phone);
} else {
    // 使用内置字体，文字贴左显示
    imagestring($image, $font, $paddingLeft, $paddingTop, $phone, $text);
}

// 设置响应头
header('Content-Type: image/png');
header('Cache-Control: max-age=86400'); // 缓存24小时

// 输出图像
imagepng($image);
imagedestroy($image);
?> 