<!DOCTYPE html>
<?php
// 获取主题设置
$theme = isset($_COOKIE['site_theme']) ? $_COOKIE['site_theme'] : 'red';
$theme_class = 'theme-' . $theme;
?>
<html lang="zh-CN" class="<?php echo $theme_class; ?>">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <title>{$site_name} - 免费发布分类信息</title>
    <meta name="keywords" content="{$site_keywords|default('分类信息,免费发布信息')}">
    <meta name="description" content="{$site_description|default('提供免费发布分类信息服务的网站')}">
    <link rel="stylesheet" href="/static/font-awesome/css/all.min.css">
    <link rel="stylesheet" href="/static/css/themes.css?v=<?php echo time(); ?>">
    <link rel="stylesheet" href="/template/m/css/common.css?v=<?php echo time(); ?>">
    <link rel="stylesheet" href="/template/m/css/index.css?v=<?php echo time(); ?>">
    <script src="/static/js/common.js"></script>
    <script src="/static/js/list-item-clickable.js"></script>
    <script src="/static/js/page-stabilizer.js"></script>
    <style>
    /* 简约主题头部样式 */
    html.theme-simple header {
        background-color: #ffffff !important;
        color: #333333 !important;
        border-bottom: 1px solid #eeeeee;
    }
    
    html.theme-simple header .header-title,
    html.theme-simple header .menu-icon i,
    html.theme-simple header .header-actions a {
        color: #333333 !important;
    }
    
    html.theme-simple .header-inner,
    html.theme-simple .sticky-inner {
        background-color: #ffffff !important;
    }
    
    html.theme-simple .search-box {
        background-color: #f5f5f5 !important;
        color: #666666 !important;
    }
    </style>
  
</head>
<body>
    <!-- 普通头部 -->
    <header class="main-header">
        <div class="container">
            <div class="header-inner">
                <div class="menu-icon">
                    <i class="fas fa-bars"></i>
                </div>
                <div class="search-box" onclick="toggleSearch()">
                    <i class="fas fa-search"></i>
                    <span class="search-placeholder">找工作 找房子 找服务</span>
                </div>
                <div class="header-actions">
                    <a href="/post.php" class="add-btn">
                        <i class="fas fa-plus"></i>
                    </a>
                </div>
            </div>
        </div>
    </header>
    
    <!-- 固定搜索头部 -->
    <header class="sticky-header" id="stickyHeader">
        <div class="container">
            <div class="sticky-inner">
                <div class="menu-icon">
                    <i class="fas fa-bars"></i>
                </div>
                <div class="search-box" onclick="toggleSearch()">
                    <i class="fas fa-search"></i>
                    <span class="search-placeholder">搜索信息...</span>
                </div>
                <div class="header-actions">
                    <a href="/post.php" class="add-btn">
                        <i class="fas fa-plus"></i>
                    </a>
                </div>
            </div>
        </div>
    </header>

    <!-- 搜索层 -->
    <div class="search-layer" id="searchLayer">
        <div class="search-header">
            <a href="javascript:void(0);" class="search-back" onclick="toggleSearch()">
                <i class="fas fa-chevron-left"></i>
            </a>
            <form action="/search.php" method="get" class="search-form" id="home-search-form">
                <i class="fas fa-search search-icon" id="search-icon"></i>
                <i class="fas fa-spinner fa-spin search-loading" id="search-loading-icon" style="display: none;"></i>
                <input type="text" name="keyword" class="search-input" placeholder="搜索工作、房子、手机..." autocomplete="off" id="home-search-input">
            </form>
            <button type="button" class="search-cancel" onclick="toggleSearch()">取消</button>
        </div>
        <div class="search-content">
            <div class="search-history">
                <div class="search-section-title">
                    <span>搜索历史</span>
                    <button type="button" class="search-clear" onclick="clearSearchHistory()">
                        <i class="fas fa-trash-alt"></i> 清空
                    </button>
                </div>
                <div class="search-tags" id="searchHistoryTags"></div>
            </div>
            <div class="search-hot">
                <div class="search-section-title">
                    <span>热门搜索</span>
                </div>
                <div class="search-tags">
                    <a href="javascript:void(0);" class="search-tag" onclick="searchTag(this)">招聘</a>
                    <a href="javascript:void(0);" class="search-tag" onclick="searchTag(this)">兼职</a>
                    <a href="javascript:void(0);" class="search-tag" onclick="searchTag(this)">房屋出租</a>
                    <a href="javascript:void(0);" class="search-tag" onclick="searchTag(this)">二手车</a>
                    <a href="javascript:void(0);" class="search-tag" onclick="searchTag(this)">手机</a>
                </div>
            </div>
        </div>
    </div>

    <div class="container">
        <div class="category-section">
            <div class="category-scroll">
                <div class="category-grid-container">
                    <div class="category-grid">
                        <!-- 第一页：前10个分类 -->
                        <!-- 动态显示所有分类 -->
                    <?php
                    $totalCategories = count($categories);
                    $firstPageCount = min(10, $totalCategories); // 第一页最多显示10个分类
                    
                    // 显示第一页分类
                    for ($i = 0; $i < $firstPageCount; $i++) {
                        $category = $categories[$i];
                        $iconClass = !empty($category['icon']) ? $category['icon'] : 'fa-tag';
                        
                        // 根据分类名称确定CSS类
                        $cssClass = getCategoryCssClass($category['name']);
                        
                        echo '<a href="/' . $category['pinyin'] . '/" class="category-item">';
                        echo '<div class="category-icon ' . $cssClass . '">';
                        echo '<i class="fas ' . $iconClass . '"></i>';
                        echo '</div>';
                        echo '<div class="category-name">' . $category['name'] . '</div>';
                        echo '</a>';
                    }
                    
                    // 辅助函数 - 根据分类名称获取CSS类
                    function getCategoryCssClass($name) {
                        $mappings = [
                            '房' => 'house',
                            '屋' => 'house',
                            '租' => 'house',
                            '职' => 'job',
                            '聘' => 'job',
                            '工作' => 'job',
                            '车' => 'car',
                            '教育' => 'edu',
                            '培训' => 'edu',
                            '服务' => 'service',
                            '宠物' => 'pet',
                            '手机' => 'phone',
                            '数码' => 'phone',
                            '家居' => 'furniture',
                            '家具' => 'furniture',
                            '维修' => 'repair',
                            '食' => 'food',
                            '餐' => 'food'
                        ];
                        
                        foreach ($mappings as $keyword => $class) {
                            if (mb_strpos($name, $keyword) !== false) {
                                return $class;
                            }
                        }
                        
                        return 'more'; // 默认样式
                    }
                    ?>
                    </div>
                    
                    <!-- 其他分类，通过滑动查看 -->
                    <div class="category-grid" style="margin-left: 15px;">
                        <a href="/ershou/" class="category-item">
                            <div class="category-icon more">
                                <i class="fas fa-shopping-bag"></i>
                            </div>
                            <div class="category-name">二手物品</div>
                        </a>
                        <a href="/qiche/" class="category-item">
                            <div class="category-icon car">
                                <i class="fas fa-car-side"></i>
                            </div>
                            <div class="category-name">汽车服务</div>
                        </a>
                        <a href="/jianli/" class="category-item">
                            <div class="category-icon job">
                                <i class="fas fa-file-alt"></i>
                            </div>
                            <div class="category-name">简历资料</div>
                        </a>
                        <a href="/huazhuang/" class="category-item">
                            <div class="category-icon more">
                                <i class="fas fa-spa"></i>
                            </div>
                            <div class="category-name">美妆护肤</div>
                        </a>
                        <a href="/yule/" class="category-item">
                            <div class="category-icon more">
                                <i class="fas fa-film"></i>
                            </div>
                            <div class="category-name">休闲娱乐</div>
                        </a>
                        <a href="/lvyou/" class="category-item">
                            <div class="category-icon more">
                                <i class="fas fa-plane"></i>
                            </div>
                            <div class="category-name">旅游出行</div>
                        </a>
                        <a href="/muying/" class="category-item">
                            <div class="category-icon more">
                                <i class="fas fa-baby"></i>
                            </div>
                            <div class="category-name">母婴亲子</div>
                        </a>
                        <a href="/jiadian/" class="category-item">
                            <div class="category-icon more">
                                <i class="fas fa-tv"></i>
                            </div>
                            <div class="category-name">家用电器</div>
                        </a>
                        <a href="/baojie/" class="category-item">
                            <div class="category-icon more">
                                <i class="fas fa-broom"></i>
                            </div>
                            <div class="category-name">保洁清洗</div>
                        </a>
                        <a href="/category.php" class="category-item">
                            <div class="category-icon more">
                                <i class="fas fa-ellipsis-h"></i>
                            </div>
                            <div class="category-name">更多分类</div>
                        </a>
                    </div>
                </div>
            </div>
        </div>

        <div class="tab-container">
            <div class="tab-header">
                <div class="tab-title">最新信息</div>
            </div>
            
            <div id="tab-text" class="tab-content">
                <div class="simple-list">
                    {if !empty($topPosts)}
                    {foreach $topPosts as $post}
                    <?php
                    // 计算有效期
                    if (!empty($post['expired_at'])) {
                        $days = getRemainingDaysInt($post['expired_at']);
                        $validity = $days > 0 ? $days.'天' : '已过期';
                    } else {
                        $validity = '长期';
                    }
                    ?>
                    <div class="list-item is-top">
                        <div class="item-row">
                            <div class="item-left">
                                <div class="item-title">
                                    <?php if ($validity == '已过期'): ?>
                                    <a href="/{$post.category_pinyin}/{$post.id}.html" class="expired">{$post.title}<span class="top-tag">顶</span></a>
                                    <?php else: ?>
                                    <a href="/{$post.category_pinyin}/{$post.id}.html">{$post.title}<span class="top-tag">顶</span></a>
                                    <?php endif; ?>
                                </div>
                            </div>
                            <div class="item-right">
                                <div class="item-time">
                                    {$post.updated_at|friendlyTime}
                                </div>
                            </div>
                        </div>
                    </div>
                    {/foreach}
                    {/if}
                    {if !empty($normalPosts)}
                    {foreach $normalPosts as $post}
                    <?php
                    // 计算有效期
                    if (!empty($post['expired_at'])) {
                        $days = getRemainingDaysInt($post['expired_at']);
                        $validity = $days > 0 ? $days.'天' : '已过期';
                    } else {
                        $validity = '长期';
                    }
                    ?>
                    <div class="list-item">
                        <div class="item-row">
                            <div class="item-left">
                                <div class="item-title">
                                    <?php if ($validity == '已过期'): ?>
                                    <a href="/{$post.category_pinyin}/{$post.id}.html" class="expired">{$post.title}</a>
                                    <?php else: ?>
                                    <a href="/{$post.category_pinyin}/{$post.id}.html">{$post.title}</a>
                                    <?php endif; ?>
                                </div>
                            </div>
                            <div class="item-right">
                                <div class="item-time">
                                    {$post.updated_at|friendlyTime}
                                </div>
                            </div>
                        </div>
                    </div>
                {/foreach}
                {/if}
            </div>
        </div>


        </div>
    </div>

    {include file="navbar.htm"}

    {include file="footer.htm"}

    <script>
    // 处理后端数据，为模板准备数据
    // 兼容性加载处理，防止FOUC
    document.documentElement.classList.add('no-fouc');
    
    document.addEventListener('DOMContentLoaded', function() {
        // 确保页面已加载完成
        // 默认显示文本列表视图
        document.getElementById('tab-text').classList.add('active');
        
        // 预设关键元素高度
        document.querySelectorAll('.tab-content').forEach(function(content) {
            if (!content.style.minHeight) {
                content.style.minHeight = (window.innerHeight * 0.4) + 'px';
            }
        });
        
        // 移除FOUC类以显示内容
        setTimeout(function() {
            document.documentElement.classList.remove('no-fouc');
            document.body.classList.add('content-loaded');
        }, 50);

        // 加载搜索历史
        renderSearchHistory();

        // 点击外部关闭搜索层
        document.addEventListener('click', function(event) {
            const searchLayer = document.getElementById('searchLayer');
            if (searchLayer.classList.contains('active') && 
                !event.target.closest('.search-layer') && 
                !event.target.closest('.search-box')) {
                toggleSearch();
            }
        });
        
        // 处理滚动事件，显示/隐藏固定头部
        const stickyHeader = document.getElementById('stickyHeader');
        const mainHeaderHeight = document.querySelector('.main-header').offsetHeight;
        let lastScrollY = window.scrollY;
        let scrollTimer = null;
        let isHeaderVisible = false;

        function updateStickyHeader() {
            const currentScrollY = window.scrollY;

            // 只要滚动超过主头部高度就显示固定头部，无论向上还是向下滚动
            if (currentScrollY > mainHeaderHeight) {
                if (!isHeaderVisible) {
                    stickyHeader.classList.add('visible');
                    isHeaderVisible = true;
                }
            } else {
                // 滚动没有超过主头部高度，隐藏固定头部
                if (isHeaderVisible) {
                    stickyHeader.classList.remove('visible');
                    isHeaderVisible = false;
                }
            }

            lastScrollY = currentScrollY;
        }

        window.addEventListener('scroll', function() {
            // 防抖处理，避免频繁触发动画
            if (scrollTimer) {
                clearTimeout(scrollTimer);
            }

            scrollTimer = setTimeout(updateStickyHeader, 16); // 约60fps
        });
    });
    


    // 搜索层相关函数
    function toggleSearch() {
        const searchLayer = document.getElementById('searchLayer');
        if (searchLayer.classList.contains('active')) {
            searchLayer.style.transform = 'translateY(-10px)';
            searchLayer.style.opacity = '0';
            setTimeout(() => {
                searchLayer.classList.remove('active');
            }, 250);
        } else {
            searchLayer.classList.add('active');
            setTimeout(() => {
                searchLayer.style.transform = 'translateY(0)';
                searchLayer.style.opacity = '1';
                document.querySelector('.search-input').focus();
            }, 10);
        }
    }

    // 搜索标签点击
    function searchTag(element) {
        const keyword = element.innerText;
        addSearchHistory(keyword);
        showSearchLoading();
        window.location.href = '/search.php?keyword=' + encodeURIComponent(keyword);
    }

    // 显示搜索加载状态
    function showSearchLoading() {
        const searchForm = document.querySelector('.search-form');
        if (searchForm) {
            searchForm.classList.add('loading');
        }
    }

    // 隐藏搜索加载状态
    function hideSearchLoading() {
        const searchForm = document.querySelector('.search-form');
        if (searchForm) {
            searchForm.classList.remove('loading');
        }
    }

    // 获取搜索历史
    function getSearchHistory() {
        const history = localStorage.getItem('searchHistory');
        return history ? JSON.parse(history) : [];
    }

    // 添加搜索历史
    function addSearchHistory(keyword) {
        if (!keyword.trim()) return;
        let history = getSearchHistory();
        // 移除重复项
        history = history.filter(item => item !== keyword);
        // 添加到开头
        history.unshift(keyword);
        // 保留最新10条
        if (history.length > 10) {
            history = history.slice(0, 10);
        }
        localStorage.setItem('searchHistory', JSON.stringify(history));
        renderSearchHistory();
    }

    // 清空搜索历史
    function clearSearchHistory() {
        localStorage.removeItem('searchHistory');
        renderSearchHistory();
    }

    // 渲染搜索历史标签
    function renderSearchHistory() {
        const historyTags = document.getElementById('searchHistoryTags');
        const history = getSearchHistory();
        historyTags.innerHTML = '';
        
        if (history.length === 0) {
            historyTags.innerHTML = '<div style="color:#999;padding:10px 0;">暂无搜索历史</div>';
            return;
        }
        
        history.forEach(item => {
            const tag = document.createElement('a');
            tag.href = 'javascript:void(0);';
            tag.className = 'search-tag';
            tag.innerText = item;
            tag.onclick = function() {
                searchTag(this);
            };
            historyTags.appendChild(tag);
        });
    }

    // 页面加载时，如果有keyword参数，将其添加到历史记录
    document.addEventListener('DOMContentLoaded', function() {
        renderSearchHistory();

        const urlParams = new URLSearchParams(window.location.search);
        const keyword = urlParams.get('keyword');
        if (keyword) {
            addSearchHistory(keyword);
        }

        // 绑定首页搜索表单提交事件
        const homeSearchForm = document.getElementById('home-search-form');
        if (homeSearchForm) {
            homeSearchForm.addEventListener('submit', function(e) {
                const input = document.getElementById('home-search-input');
                const keyword = input.value.trim();

                if (keyword) {
                    addSearchHistory(keyword);
                    showSearchLoading();
                }
            });
        }
    });
    </script>
</body>
</html> 