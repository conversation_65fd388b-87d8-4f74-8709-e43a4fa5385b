<?php
/**
 * 新闻缓存管理类
 * 专门处理新闻相关的缓存操作
 */

if (!defined('IN_BTMPS')) {
    exit('Access Denied');
}

class NewsCache {
    private $cache;
    private $cachePrefix = 'news_';
    private $defaultExpire = 3600; // 1小时

    public function __construct() {
        // 使用全局缓存实例
        $this->cache = $GLOBALS['file_cache'];
    }

    /**
     * 从配置获取缓存时效
     */
    private function getCacheExpire($type) {
        global $config;

        // 检查缓存是否启用
        $cache_enable = isset($config['cache_enable']) ? intval($config['cache_enable']) : 1;
        if (!$cache_enable) {
            return 0; // 缓存未启用
        }

        switch ($type) {
            case 'home':
                // 新闻首页缓存时间，优先使用news_home配置，否则使用cache_index
                return isset($config['cache_news_home']) ? intval($config['cache_news_home']) :
                       (isset($config['cache_index']) ? intval($config['cache_index']) : 1800);

            case 'list':
                // 新闻列表缓存时间，优先使用news_list配置，否则使用cache_list
                return isset($config['cache_news_list']) ? intval($config['cache_news_list']) :
                       (isset($config['cache_list']) ? intval($config['cache_list']) : 1800);

            case 'detail':
                // 新闻详情缓存时间，优先使用news_detail配置，否则使用cache_post
                return isset($config['cache_news_detail']) ? intval($config['cache_news_detail']) :
                       (isset($config['cache_post']) ? intval($config['cache_post']) : 86400);

            case 'categories':
                // 新闻分类缓存时间
                return isset($config['cache_news_category']) ? intval($config['cache_news_category']) :
                       (isset($config['cache_category']) ? intval($config['cache_category']) : 21600);

            case 'hot':
            case 'recommend':
                // 热门和推荐新闻缓存时间
                return isset($config['cache_news_hot']) ? intval($config['cache_news_hot']) : 7200;

            default:
                return $this->defaultExpire;
        }
    }
    
    /**
     * 生成缓存键名 - 优化命名规范，便于识别和管理
     */
    private function getCacheKey($type, $params = []) {
        $key = $this->cachePrefix . $type;

        if (!empty($params)) {
            // 根据不同类型生成更清晰的缓存键名
            switch ($type) {
                case 'home':
                    // 新闻首页缓存：news_home_p1_10_rec1_top0_hash
                    $key .= '_p' . (isset($params['page']) ? $params['page'] : 1);
                    $key .= '_' . (isset($params['perpage']) ? $params['perpage'] : 10);
                    $key .= '_rec' . (isset($params['filter_recommend']) ? $params['filter_recommend'] : 0);
                    $key .= '_top' . (isset($params['filter_top']) ? $params['filter_top'] : 0);
                    $key .= '_' . substr(md5(serialize($params)), 0, 8);
                    break;

                case 'list':
                    // 新闻列表缓存：news_list_cat7_p1_10_rec1_top0_hash
                    $key .= '_cat' . (isset($params['catid']) ? $params['catid'] : 0);
                    $key .= '_p' . (isset($params['page']) ? $params['page'] : 1);
                    $key .= '_' . (isset($params['perpage']) ? $params['perpage'] : 10);
                    $key .= '_rec' . (isset($params['filter_recommend']) ? $params['filter_recommend'] : 0);
                    $key .= '_top' . (isset($params['filter_top']) ? $params['filter_top'] : 0);
                    $key .= '_' . substr(md5(serialize($params)), 0, 8);
                    break;

                case 'detail':
                    // 新闻详情缓存：news_detail_19650
                    $key .= '_' . (isset($params['id']) ? $params['id'] : 0);
                    break;

                case 'hot':
                    // 热门新闻缓存：news_hot_10
                    $key .= '_' . (isset($params['limit']) ? $params['limit'] : 10);
                    break;

                case 'recommend':
                    // 推荐新闻缓存：news_recommend_10
                    $key .= '_' . (isset($params['limit']) ? $params['limit'] : 10);
                    break;

                default:
                    // 其他类型保持原有方式
                    $key .= '_' . md5(serialize($params));
                    break;
            }
        }

        return $key;
    }
    
    /**
     * 获取新闻首页缓存
     */
    public function getHomeCache($params = []) {
        $key = $this->getCacheKey('home', $params);
        return $this->cache->get($key);
    }
    
    /**
     * 设置新闻首页缓存
     */
    public function setHomeCache($data, $params = [], $expire = null) {
        $key = $this->getCacheKey('home', $params);
        $expire = $expire ?: $this->getCacheExpire('home');
        return $this->cache->set($key, $data, $expire);
    }
    
    /**
     * 获取新闻列表缓存
     */
    public function getListCache($params = []) {
        $key = $this->getCacheKey('list', $params);
        return $this->cache->get($key);
    }
    
    /**
     * 设置新闻列表缓存
     */
    public function setListCache($data, $params = [], $expire = null) {
        $key = $this->getCacheKey('list', $params);
        $expire = $expire ?: $this->getCacheExpire('list');
        return $this->cache->set($key, $data, $expire);
    }
    
    /**
     * 获取新闻详情缓存
     */
    public function getDetailCache($newsId) {
        $key = $this->getCacheKey('detail', ['id' => $newsId]);
        return $this->cache->get($key);
    }
    
    /**
     * 设置新闻详情缓存
     */
    public function setDetailCache($newsId, $data, $expire = null) {
        $key = $this->getCacheKey('detail', ['id' => $newsId]);
        $expire = $expire ?: $this->getCacheExpire('detail');
        return $this->cache->set($key, $data, $expire);
    }
    
    /**
     * 获取新闻分类缓存
     */
    public function getCategoryCache() {
        $key = $this->getCacheKey('categories');
        return $this->cache->get($key);
    }
    
    /**
     * 设置新闻分类缓存
     */
    public function setCategoryCache($data, $expire = null) {
        $key = $this->getCacheKey('categories');
        $expire = $expire ?: $this->getCacheExpire('categories');
        return $this->cache->set($key, $data, $expire);
    }
    
    /**
     * 获取热门新闻缓存
     */
    public function getHotNewsCache($limit = 10) {
        $key = $this->getCacheKey('hot', ['limit' => $limit]);
        return $this->cache->get($key);
    }
    
    /**
     * 设置热门新闻缓存
     */
    public function setHotNewsCache($data, $limit = 10, $expire = null) {
        $key = $this->getCacheKey('hot', ['limit' => $limit]);
        $expire = $expire ?: $this->getCacheExpire('hot');
        return $this->cache->set($key, $data, $expire);
    }
    
    /**
     * 获取推荐新闻缓存
     */
    public function getRecommendCache($limit = 10) {
        $key = $this->getCacheKey('recommend', ['limit' => $limit]);
        return $this->cache->get($key);
    }
    
    /**
     * 设置推荐新闻缓存
     */
    public function setRecommendCache($data, $limit = 10, $expire = null) {
        $key = $this->getCacheKey('recommend', ['limit' => $limit]);
        $expire = $expire ?: $this->getCacheExpire('recommend');
        return $this->cache->set($key, $data, $expire);
    }
    
    /**
     * 清理所有新闻缓存
     */
    public function clearAllNewsCache() {
        $cacheDir = $this->cache->getCacheDir();
        $pattern = $cacheDir . $this->cachePrefix . '*.cache';
        $files = glob($pattern);
        $count = 0;
        
        foreach ($files as $file) {
            if (@unlink($file)) {
                $count++;
            }
        }
        
        return $count;
    }
    
    /**
     * 清理首页缓存
     */
    public function clearHomeCache() {
        $cacheDir = $this->cache->getCacheDir();
        $pattern = $cacheDir . $this->cachePrefix . 'home*.cache';
        $files = glob($pattern);
        $count = 0;
        
        foreach ($files as $file) {
            if (@unlink($file)) {
                $count++;
            }
        }
        
        return $count;
    }
    
    /**
     * 清理列表页缓存
     */
    public function clearListCache() {
        $cacheDir = $this->cache->getCacheDir();
        $pattern = $cacheDir . $this->cachePrefix . 'list*.cache';
        $files = glob($pattern);
        $count = 0;
        
        foreach ($files as $file) {
            if (@unlink($file)) {
                $count++;
            }
        }
        
        return $count;
    }
    
    /**
     * 清理详情页缓存
     */
    public function clearDetailCache($newsId = null) {
        $cacheDir = $this->cache->getCacheDir();
        
        if ($newsId) {
            // 清理指定新闻的详情缓存
            $key = $this->getCacheKey('detail', ['id' => $newsId]);
            return $this->cache->delete($key) ? 1 : 0;
        } else {
            // 清理所有详情缓存
            $pattern = $cacheDir . $this->cachePrefix . 'detail*.cache';
            $files = glob($pattern);
            $count = 0;
            
            foreach ($files as $file) {
                if (@unlink($file)) {
                    $count++;
                }
            }
            
            return $count;
        }
    }
    
    /**
     * 清理分类缓存
     */
    public function clearCategoryCache() {
        $key = $this->getCacheKey('categories');
        return $this->cache->delete($key) ? 1 : 0;
    }
    
    /**
     * 清理热门和推荐缓存
     */
    public function clearHotAndRecommendCache() {
        $cacheDir = $this->cache->getCacheDir();
        $patterns = [
            $cacheDir . $this->cachePrefix . 'hot*.cache',
            $cacheDir . $this->cachePrefix . 'recommend*.cache'
        ];
        
        $count = 0;
        foreach ($patterns as $pattern) {
            $files = glob($pattern);
            foreach ($files as $file) {
                if (@unlink($file)) {
                    $count++;
                }
            }
        }
        
        return $count;
    }
    
    /**
     * 获取新闻缓存统计信息
     */
    public function getCacheStats() {
        $cacheDir = $this->cache->getCacheDir();
        $pattern = $cacheDir . $this->cachePrefix . '*.cache';
        $files = glob($pattern);
        
        $stats = [
            'total_files' => 0,
            'total_size' => 0,
            'types' => [
                'home' => 0,
                'list' => 0,
                'detail' => 0,
                'categories' => 0,
                'hot' => 0,
                'recommend' => 0
            ]
        ];
        
        foreach ($files as $file) {
            if (is_file($file)) {
                $stats['total_files']++;
                $stats['total_size'] += filesize($file);
                
                $filename = basename($file, '.cache');
                if (strpos($filename, $this->cachePrefix . 'home') === 0) {
                    $stats['types']['home']++;
                } elseif (strpos($filename, $this->cachePrefix . 'list') === 0) {
                    $stats['types']['list']++;
                } elseif (strpos($filename, $this->cachePrefix . 'detail') === 0) {
                    $stats['types']['detail']++;
                } elseif (strpos($filename, $this->cachePrefix . 'categories') === 0) {
                    $stats['types']['categories']++;
                } elseif (strpos($filename, $this->cachePrefix . 'hot') === 0) {
                    $stats['types']['hot']++;
                } elseif (strpos($filename, $this->cachePrefix . 'recommend') === 0) {
                    $stats['types']['recommend']++;
                }
            }
        }
        
        return $stats;
    }
}

// 创建全局新闻缓存实例
$GLOBALS['news_cache'] = new NewsCache();

/**
 * 新闻缓存辅助函数
 */
function news_cache_get_home($params = []) {
    return $GLOBALS['news_cache']->getHomeCache($params);
}

function news_cache_set_home($data, $params = [], $expire = null) {
    return $GLOBALS['news_cache']->setHomeCache($data, $params, $expire);
}

function news_cache_get_list($params = []) {
    return $GLOBALS['news_cache']->getListCache($params);
}

function news_cache_set_list($data, $params = [], $expire = null) {
    return $GLOBALS['news_cache']->setListCache($data, $params, $expire);
}

function news_cache_get_detail($newsId) {
    return $GLOBALS['news_cache']->getDetailCache($newsId);
}

function news_cache_set_detail($newsId, $data, $expire = null) {
    return $GLOBALS['news_cache']->setDetailCache($newsId, $data, $expire);
}

function news_cache_clear_all() {
    return $GLOBALS['news_cache']->clearAllNewsCache();
}

function news_cache_clear_home() {
    return $GLOBALS['news_cache']->clearHomeCache();
}

function news_cache_clear_list() {
    return $GLOBALS['news_cache']->clearListCache();
}

function news_cache_clear_detail($newsId = null) {
    return $GLOBALS['news_cache']->clearDetailCache($newsId);
}
