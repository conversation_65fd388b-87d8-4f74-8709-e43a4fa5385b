-- 手机号每日发布统计表
CREATE TABLE IF NOT EXISTS `mobile_daily_stats` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '统计ID',
  `mobile` varchar(20) NOT NULL COMMENT '手机号码',
  `date` date NOT NULL COMMENT '统计日期',
  `post_count` int(11) DEFAULT 0 COMMENT '当日发布数量',
  `created_at` int(10) NOT NULL COMMENT '创建时间',
  `updated_at` int(10) NOT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `unique_mobile_date` (`mobile`, `date`),
  KEY `idx_mobile` (`mobile`),
  KEY `idx_date` (`date`),
  KEY `idx_created_at` (`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='手机号每日发布统计表';
