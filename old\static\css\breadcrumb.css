/**
 * 简洁专业的面包屑导航样式
 */
.breadcrumb {
    background-color: #fff !important;
    margin-bottom: 10px;
    font-size: 13px;
    white-space: nowrap;
    overflow-x: auto;
    position: relative;
    padding: 10px 15px;
    border-bottom: 1px solid #eee !important;
}

.breadcrumb::-webkit-scrollbar {
    display: none;
}

.breadcrumb .container {
    display: flex;
    align-items: center;
    padding: 0;
}

.breadcrumb a {
    color: #666 !important;
    text-decoration: none;
    transition: color 0.2s;
}

.breadcrumb a:hover {
    color: #666 !important;
    text-decoration: underline;
}

.breadcrumb .separator {
    margin: 0 8px;
    color: #ccc !important;
}

.breadcrumb .separator:after {
    content: '>';
    font-size: 12px;
}

.breadcrumb .current {
    color: #333 !important;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    max-width: 150px;
}

/* 添加发光效果当滚动到最右侧时 */
.breadcrumb::after {
    content: "";
    position: absolute;
    top: 0;
    right: 0;
    height: 100%;
    width: 25px;
    background: linear-gradient(to right, rgba(255,255,255,0), rgba(255,255,255,1)) !important;
    pointer-events: none;
}

/* 响应式：在小屏幕上调整尺寸 */
@media (max-width: 360px) {
    .breadcrumb {
        font-size: 12px;
        padding: 8px 12px;
    }
    
    .breadcrumb .separator {
        margin: 0 6px;
    }
    
    .breadcrumb .current {
        max-width: 120px;
    }
}