/**
 * UEditor 自定义样式
 * 解决编辑器宽度和布局问题
 */

/* 确保编辑器宽度适应容器 */
.edui-default .edui-editor {
    width: 100% !important;
    max-width: 100% !important;
    border: 1px solid #ddd;
    border-radius: 6px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.edui-default .edui-editor-toolbarbox,
.edui-default .edui-editor-iframeholder,
.edui-default .edui-editor-bottomContainer {
    width: 100% !important;
}

/* 编辑器容器样式 */
.editor-container {
    width: 100%;
    max-width: none; /* 移除最大宽度限制，让编辑器充分利用可用空间 */
    margin: 0; /* 移除居中，让编辑器占满容器 */
}

/* 在表单中的编辑器容器 */
.form-field .editor-container {
    width: 100%;
    max-width: 100%;
}

/* 重写表单字段的最大宽度限制，特别是对于编辑器 */
.form-group.full-width .form-field {
    max-width: none !important;
    width: 100% !important;
}

/* 确保编辑器所在的表单组占满宽度 */
.form-group.full-width {
    width: 100%;
}

/* 覆盖卡片容器的宽度限制 */
.card-body-narrow {
    max-width: none !important;
}

/* 强制编辑器相关容器占满宽度 */
.card-body {
    width: 100% !important;
}

/* 确保表单容器不限制宽度 */
form {
    width: 100%;
}

/* 强制编辑器获得最大宽度 - 最高优先级 */
.edui-default .edui-editor,
.edui-default .edui-editor-toolbarbox,
.edui-default .edui-editor-iframeholder,
.edui-default .edui-editor-bottomContainer {
    width: 100% !important;
    max-width: none !important;
    min-width: 600px !important; /* 设置最小宽度确保编辑器不会太小 */
}

/* 编辑器初始化时的宽度设置 */
#editor {
    width: 100% !important;
}

/* 工具栏样式优化 */
.edui-default .edui-editor-toolbarbox {
    background: #f8f9fa;
    border-bottom: 1px solid #e9ecef;
    border-radius: 6px 6px 0 0;
}

/* 编辑区域样式 */
.edui-default .edui-editor-iframeholder {
    border-radius: 0 0 6px 6px;
    background: #fff;
}

/* 修正全屏模式的z-index，确保在最顶层 */
.edui-default.edui-editor-popup, 
.edui-default.edui-for-fullscreen {
    z-index: 9999 !important;
}

/* 确保全屏时没有边距 */
.edui-editor.edui-default.edui-for-fullscreen {
    top: 0 !important;
    left: 0 !important;
    right: 0 !important;
    bottom: 0 !important;
    width: 100% !important;
    height: 100% !important;
    position: fixed !important;
    border-radius: 0 !important;
}

/* PC端固定布局，移除响应式设计 */

/* 编辑器内容区域样式 */
.edui-default .edui-editor-iframeholder iframe {
    background: #fff;
}

/* 状态栏样式 */
.edui-default .edui-editor-bottomContainer {
    background: #f8f9fa;
    border-top: 1px solid #e9ecef;
    padding: 5px 10px;
    font-size: 12px;
    color: #6c757d;
}

/* 按钮样式优化 */
.edui-default .edui-button {
    border-radius: 3px;
    transition: all 0.2s ease;
}

.edui-default .edui-button:hover {
    background-color: #e9ecef;
}

.edui-default .edui-button.edui-state-active {
    background-color: #007bff;
    color: #fff;
}

/* 下拉菜单样式 */
.edui-default .edui-menu {
    border-radius: 4px;
    box-shadow: 0 4px 12px rgba(0,0,0,0.15);
    border: 1px solid #e9ecef;
}

/* 对话框样式 */
.edui-default .edui-dialog {
    border-radius: 6px;
    box-shadow: 0 8px 24px rgba(0,0,0,0.2);
}

.edui-default .edui-dialog-titlebar {
    background: #f8f9fa;
    border-bottom: 1px solid #e9ecef;
    border-radius: 6px 6px 0 0;
}

.edui-default .edui-dialog-body {
    background: #fff;
}

.edui-default .edui-dialog-foot {
    background: #f8f9fa;
    border-top: 1px solid #e9ecef;
    border-radius: 0 0 6px 6px;
}

/* ========== 编辑器内容区域图片样式控制 ========== */

/* 编辑器内图片响应式控制 - 防止溢出 */
.edui-default .edui-editor-iframeholder iframe {
    background: #fff;
}

/* 通过CSS注入到编辑器iframe内部的样式 */
.edui-default .edui-editor-body img {
    max-width: 100% !important;
    height: auto !important;
    display: block;
    margin: 10px auto;
    border-radius: 4px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    cursor: pointer;
    transition: all 0.3s ease;
}

/* 图片悬停效果 */
.edui-default .edui-editor-body img:hover {
    box-shadow: 0 4px 16px rgba(0,0,0,0.15);
    transform: scale(1.02);
}

/* 图片对齐样式 */
.edui-default .edui-editor-body img[align="left"] {
    float: left;
    margin: 10px 15px 10px 0;
}

.edui-default .edui-editor-body img[align="right"] {
    float: right;
    margin: 10px 0 10px 15px;
}

.edui-default .edui-editor-body img[align="center"] {
    display: block;
    margin: 10px auto;
    float: none;
}

/* 清除浮动 */
.edui-default .edui-editor-body p:after {
    content: "";
    display: table;
    clear: both;
}

/* 图片容器样式 */
.edui-default .edui-editor-body .image-container {
    max-width: 100%;
    overflow: hidden;
    text-align: center;
    margin: 15px 0;
}

/* 图片标题样式 */
.edui-default .edui-editor-body .image-title {
    font-size: 12px;
    color: #666;
    text-align: center;
    margin-top: 5px;
    font-style: italic;
}
