#!/bin/bash

# 缓存预热定时任务设置脚本
# 使用方法：chmod +x setup_cron.sh && ./setup_cron.sh

echo "=== 缓存预热定时任务设置 ==="

# 获取当前脚本所在目录
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_DIR="$(dirname "$SCRIPT_DIR")"
CACHE_WARMUP_SCRIPT="$SCRIPT_DIR/cache_warmup.php"

echo "项目目录: $PROJECT_DIR"
echo "缓存预热脚本: $CACHE_WARMUP_SCRIPT"

# 检查PHP路径
PHP_PATH=$(which php)
if [ -z "$PHP_PATH" ]; then
    echo "错误: 未找到PHP可执行文件"
    exit 1
fi
echo "PHP路径: $PHP_PATH"

# 检查缓存预热脚本是否存在
if [ ! -f "$CACHE_WARMUP_SCRIPT" ]; then
    echo "错误: 缓存预热脚本不存在: $CACHE_WARMUP_SCRIPT"
    exit 1
fi

# 创建日志目录
LOG_DIR="$PROJECT_DIR/data/logs"
mkdir -p "$LOG_DIR"
chmod 755 "$LOG_DIR"

echo "日志目录: $LOG_DIR"

# 生成crontab条目
CRON_ENTRY_1="# 缓存预热 - 每6小时执行一次"
CRON_ENTRY_2="0 */6 * * * $PHP_PATH $CACHE_WARMUP_SCRIPT >> $LOG_DIR/cron_cache_warmup.log 2>&1"

CRON_ENTRY_3="# 缓存预热 - 每天凌晨2点执行（备选方案）"
CRON_ENTRY_4="0 2 * * * $PHP_PATH $CACHE_WARMUP_SCRIPT >> $LOG_DIR/cron_cache_warmup.log 2>&1"

echo ""
echo "=== 推荐的Crontab配置 ==="
echo ""
echo "方案1: 每6小时执行一次（推荐用于高流量网站）"
echo "$CRON_ENTRY_1"
echo "$CRON_ENTRY_2"
echo ""
echo "方案2: 每天凌晨2点执行一次（推荐用于中低流量网站）"
echo "$CRON_ENTRY_3"
echo "$CRON_ENTRY_4"
echo ""

# 询问用户选择
echo "请选择要安装的定时任务方案："
echo "1) 每6小时执行一次"
echo "2) 每天凌晨2点执行一次"
echo "3) 手动配置（显示配置信息但不自动安装）"
echo "4) 退出"
echo ""
read -p "请输入选择 (1-4): " choice

case $choice in
    1)
        SELECTED_CRON="$CRON_ENTRY_2"
        DESCRIPTION="每6小时执行一次"
        ;;
    2)
        SELECTED_CRON="$CRON_ENTRY_4"
        DESCRIPTION="每天凌晨2点执行一次"
        ;;
    3)
        echo ""
        echo "=== 手动配置说明 ==="
        echo "请手动执行以下命令来编辑crontab："
        echo "crontab -e"
        echo ""
        echo "然后添加以下任意一行："
        echo "$CRON_ENTRY_2"
        echo "或"
        echo "$CRON_ENTRY_4"
        echo ""
        echo "保存并退出即可。"
        exit 0
        ;;
    4)
        echo "退出安装"
        exit 0
        ;;
    *)
        echo "无效选择，退出"
        exit 1
        ;;
esac

# 安装定时任务
echo ""
echo "正在安装定时任务: $DESCRIPTION"

# 备份现有的crontab
crontab -l > /tmp/crontab_backup_$(date +%Y%m%d_%H%M%S) 2>/dev/null

# 检查是否已存在相同的任务
if crontab -l 2>/dev/null | grep -q "$CACHE_WARMUP_SCRIPT"; then
    echo "警告: 检测到已存在相关的缓存预热任务"
    read -p "是否要替换现有任务? (y/n): " replace
    if [ "$replace" != "y" ] && [ "$replace" != "Y" ]; then
        echo "取消安装"
        exit 0
    fi
    
    # 移除现有任务
    crontab -l 2>/dev/null | grep -v "$CACHE_WARMUP_SCRIPT" | crontab -
fi

# 添加新任务
(crontab -l 2>/dev/null; echo "$SELECTED_CRON") | crontab -

if [ $? -eq 0 ]; then
    echo "✓ 定时任务安装成功！"
    echo ""
    echo "=== 安装信息 ==="
    echo "任务描述: $DESCRIPTION"
    echo "执行命令: $SELECTED_CRON"
    echo "日志文件: $LOG_DIR/cron_cache_warmup.log"
    echo ""
    echo "=== 验证安装 ==="
    echo "当前的crontab任务："
    crontab -l | grep -A1 -B1 "$CACHE_WARMUP_SCRIPT"
    echo ""
    echo "=== 测试运行 ==="
    echo "您可以手动测试缓存预热脚本："
    echo "$PHP_PATH $CACHE_WARMUP_SCRIPT"
    echo ""
    echo "=== 监控日志 ==="
    echo "查看执行日志："
    echo "tail -f $LOG_DIR/cron_cache_warmup.log"
    echo "tail -f $LOG_DIR/cache_warmup_$(date +%Y-%m-%d).log"
else
    echo "✗ 定时任务安装失败！"
    exit 1
fi

echo ""
echo "=== 其他配置建议 ==="
echo ""
echo "1. 修改缓存预热脚本中的安全密钥："
echo "   编辑 $CACHE_WARMUP_SCRIPT"
echo "   找到 \$secret_key = 'your_secret_key_here';"
echo "   修改为您自己的密钥"
echo ""
echo "2. 根据服务器性能调整缓存时间："
echo "   编辑 $PROJECT_DIR/config/config.inc.php"
echo "   调整各种缓存时间设置"
echo ""
echo "3. 监控缓存预热效果："
echo "   - 查看网站访问速度是否提升"
echo "   - 检查服务器负载是否合理"
echo "   - 观察缓存命中率"
echo ""
echo "安装完成！"
