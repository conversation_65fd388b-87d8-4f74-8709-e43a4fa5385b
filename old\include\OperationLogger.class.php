<?php
/**
 * 操作日志记录类
 * 用于记录用户的各种操作行为
 */

if (!defined('IN_BTMPS')) {
    exit('Access Denied');
}

class OperationLogger
{
    private $db;
    private $config;
    
    public function __construct($database, $config = null)
    {
        $this->db = $database;
        $this->config = $config;
    }
    
    /**
     * 记录操作日志
     * 
     * @param array $params 日志参数
     * @return bool 是否记录成功
     */
    public function log($params)
    {
        $start_time = microtime(true);
        
        try {
            // 获取客户端信息
            $client_info = $this->getClientInfo();
            
            // 合并参数
            $log_data = array_merge([
                'user_id' => 0,
                'user_type' => 'guest',
                'username' => '',
                'operation_type' => '',
                'target_type' => '',
                'target_id' => 0,
                'target_title' => '',
                'operation_desc' => '',
                'old_data' => '',
                'new_data' => '',
                'status' => 1,
                'error_message' => '',
                'created_at' => time()
            ], $params, $client_info);
            
            // 计算执行时间
            $execution_time = round((microtime(true) - $start_time) * 1000, 3);
            $log_data['execution_time'] = $execution_time;
            
            // 构建SQL - 使用现有MySQL类的方法
            $fields = [];
            $values = [];

            foreach ($log_data as $field => $value) {
                $fields[] = "`{$field}`";
                if (is_string($value)) {
                    $values[] = "'" . $this->db->escape($value) . "'";
                } elseif (is_null($value)) {
                    $values[] = 'NULL';
                } else {
                    $values[] = $value;
                }
            }

            $sql = "INSERT INTO operation_logs (" . implode(', ', $fields) . ") VALUES (" . implode(', ', $values) . ")";

            // 执行插入
            $result = $this->db->query($sql);
            return $result !== false;
            
        } catch (Exception $e) {
            // 记录日志失败时，可以写入文件日志
            error_log("OperationLogger Error: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * 记录创建操作
     */
    public function logCreate($target_type, $target_id, $target_title, $new_data = null, $user_info = null)
    {
        return $this->log([
            'user_id' => $user_info['user_id'] ?? 0,
            'user_type' => $user_info['user_type'] ?? 'guest',
            'username' => $user_info['username'] ?? '',
            'operation_type' => 'create',
            'target_type' => $target_type,
            'target_id' => $target_id,
            'target_title' => $target_title,
            'operation_desc' => "创建{$target_type}：{$target_title}",
            'new_data' => $new_data ? json_encode($new_data, JSON_UNESCAPED_UNICODE) : ''
        ]);
    }
    
    /**
     * 记录更新操作
     */
    public function logUpdate($target_type, $target_id, $target_title, $old_data = null, $new_data = null, $user_info = null)
    {
        return $this->log([
            'user_id' => $user_info['user_id'] ?? 0,
            'user_type' => $user_info['user_type'] ?? 'guest',
            'username' => $user_info['username'] ?? '',
            'operation_type' => 'update',
            'target_type' => $target_type,
            'target_id' => $target_id,
            'target_title' => $target_title,
            'operation_desc' => "修改{$target_type}：{$target_title}",
            'old_data' => $old_data ? json_encode($old_data, JSON_UNESCAPED_UNICODE) : '',
            'new_data' => $new_data ? json_encode($new_data, JSON_UNESCAPED_UNICODE) : ''
        ]);
    }
    
    /**
     * 记录删除操作
     */
    public function logDelete($target_type, $target_id, $target_title, $old_data = null, $user_info = null)
    {
        return $this->log([
            'user_id' => $user_info['user_id'] ?? 0,
            'user_type' => $user_info['user_type'] ?? 'guest',
            'username' => $user_info['username'] ?? '',
            'operation_type' => 'delete',
            'target_type' => $target_type,
            'target_id' => $target_id,
            'target_title' => $target_title,
            'operation_desc' => "删除{$target_type}：{$target_title}",
            'old_data' => $old_data ? json_encode($old_data, JSON_UNESCAPED_UNICODE) : ''
        ]);
    }
    
    /**
     * 记录登录操作
     */
    public function logLogin($username, $user_id, $user_type = 'admin', $status = 1, $error_message = '')
    {
        return $this->log([
            'user_id' => $user_id,
            'user_type' => $user_type,
            'username' => $username,
            'operation_type' => 'login',
            'target_type' => 'auth',
            'operation_desc' => $status ? "用户登录成功" : "用户登录失败",
            'status' => $status,
            'error_message' => $error_message
        ]);
    }
    
    /**
     * 记录登出操作
     */
    public function logLogout($username, $user_id, $user_type = 'admin')
    {
        return $this->log([
            'user_id' => $user_id,
            'user_type' => $user_type,
            'username' => $username,
            'operation_type' => 'logout',
            'target_type' => 'auth',
            'operation_desc' => "用户登出"
        ]);
    }
    
    /**
     * 获取客户端信息
     */
    private function getClientInfo()
    {
        $info = [
            'ip_address' => $this->getClientIP(),
            'port' => $_SERVER['REMOTE_PORT'] ?? 0,
            'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? '',
            'referer' => $_SERVER['HTTP_REFERER'] ?? '',
            'request_method' => $_SERVER['REQUEST_METHOD'] ?? 'GET',
            'request_uri' => $_SERVER['REQUEST_URI'] ?? '',
            'session_id' => session_id() ?? ''
        ];
        
        return $info;
    }
    
    /**
     * 获取客户端真实IP
     */
    private function getClientIP()
    {
        $ip_keys = ['HTTP_X_FORWARDED_FOR', 'HTTP_X_REAL_IP', 'HTTP_CLIENT_IP', 'REMOTE_ADDR'];
        
        foreach ($ip_keys as $key) {
            if (!empty($_SERVER[$key])) {
                $ips = explode(',', $_SERVER[$key]);
                $ip = trim($ips[0]);
                
                // 验证IP格式
                if (filter_var($ip, FILTER_VALIDATE_IP, FILTER_FLAG_NO_PRIV_RANGE | FILTER_FLAG_NO_RES_RANGE)) {
                    return $ip;
                }
            }
        }
        
        return $_SERVER['REMOTE_ADDR'] ?? '127.0.0.1';
    }
    
    /**
     * 获取日志列表
     */
    public function getLogs($params = [])
    {
        $where_conditions = [];

        // 构建查询条件
        if (!empty($params['user_id'])) {
            $where_conditions[] = "user_id = " . intval($params['user_id']);
        }

        if (!empty($params['operation_type'])) {
            $where_conditions[] = "operation_type = '" . $this->db->escape($params['operation_type']) . "'";
        }

        if (!empty($params['target_type'])) {
            $where_conditions[] = "target_type = '" . $this->db->escape($params['target_type']) . "'";
        }

        if (!empty($params['ip_address'])) {
            $where_conditions[] = "ip_address = '" . $this->db->escape($params['ip_address']) . "'";
        }

        if (!empty($params['start_time'])) {
            $where_conditions[] = "created_at >= " . intval($params['start_time']);
        }

        if (!empty($params['end_time'])) {
            $where_conditions[] = "created_at <= " . intval($params['end_time']);
        }

        if (!empty($params['keyword'])) {
            $keyword = $this->db->escape($params['keyword']);
            $where_conditions[] = "(target_title LIKE '%{$keyword}%' OR operation_desc LIKE '%{$keyword}%' OR username LIKE '%{$keyword}%')";
        }

        // 构建SQL
        $where_sql = empty($where_conditions) ? '' : 'WHERE ' . implode(' AND ', $where_conditions);

        $page = max(1, intval($params['page'] ?? 1));
        $page_size = max(1, min(100, intval($params['page_size'] ?? 20)));

        // 获取总数
        $count_sql = "SELECT COUNT(*) as total FROM operation_logs {$where_sql}";
        $count_result = $this->db->query($count_sql);
        $total = 0;
        if ($count_result) {
            $count_row = $this->db->fetch_array($count_result);
            $total = $count_row['total'] ?? 0;
        }

        // 计算总页数
        $total_pages = max(1, ceil($total / $page_size));

        // 确保页码在有效范围内
        $page = min($page, $total_pages);

        $offset = ($page - 1) * $page_size;

        // 获取数据
        $data_sql = "SELECT * FROM operation_logs {$where_sql} ORDER BY created_at DESC, id DESC LIMIT {$offset}, {$page_size}";
        $data_result = $this->db->query($data_sql);
        $logs = [];

        if ($data_result) {
            while ($row = $this->db->fetch_array($data_result)) {
                $logs[] = $row;
            }
        }

        return [
            'total' => $total,
            'page' => $page,
            'page_size' => $page_size,
            'total_pages' => $total_pages,
            'data' => $logs
        ];
    }
    
    /**
     * 批量删除日志
     */
    public function deleteLogs($ids)
    {
        if (empty($ids) || !is_array($ids)) {
            return false;
        }

        // 转换为安全的整数数组
        $safe_ids = array_map('intval', $ids);
        $safe_ids = array_filter($safe_ids, function($id) { return $id > 0; });

        if (empty($safe_ids)) {
            return false;
        }

        $ids_str = implode(',', $safe_ids);
        $sql = "DELETE FROM operation_logs WHERE id IN ({$ids_str})";

        $result = $this->db->query($sql);
        if ($result) {
            $affected_rows = $this->db->affectedRows();

            // 记录删除操作
            $this->log([
                'user_id' => $_SESSION['admin']['id'] ?? 0,
                'user_type' => 'admin',
                'username' => $_SESSION['admin']['username'] ?? 'admin',
                'operation_type' => 'delete',
                'target_type' => 'operation_logs',
                'operation_desc' => "批量删除 {$affected_rows} 条操作日志"
            ]);

            return true;
        }

        return false;
    }
    
    /**
     * 清理旧日志
     */
    public function cleanOldLogs($days = 90)
    {
        $cutoff_time = time() - ($days * 24 * 3600);
        $sql = "DELETE FROM operation_logs WHERE created_at < " . intval($cutoff_time);

        $result = $this->db->query($sql);
        if ($result) {
            $affected_rows = $this->db->affectedRows();

            // 记录清理操作
            $this->log([
                'user_id' => 0,
                'user_type' => 'system',
                'username' => 'system',
                'operation_type' => 'delete',
                'target_type' => 'operation_logs',
                'operation_desc' => "自动清理 {$affected_rows} 条超过 {$days} 天的日志记录"
            ]);

            return $affected_rows;
        }

        return false;
    }
}
