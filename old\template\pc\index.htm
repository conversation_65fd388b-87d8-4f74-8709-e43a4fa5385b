<!DOCTYPE html>
<html>
	<head>
		<meta charset="utf-8">
		<title>{if $site_title}{$site_title}{else}{$site_name} - 免费发布分类信息{/if}</title>
                <meta name="keywords" content="{if $site_keywords}{$site_keywords}{else}分类信息,免费发布,信息平台{/if}" />
                <meta name="description" content="{if $site_description}{$site_description}{else}提供免费发布分类信息服务的网站{/if}" />

				<link rel="stylesheet" href="/template/pc/css/common.css">
               <link rel="stylesheet" href="/template/pc/css/index.css?<?php echo time(); ?>">
               <script type="text/javascript" src="/template/pc/js/m.js"></script>
		
	</head>
	<body>
		{include file="header.htm"}
		<div class="mo-box yui-1200">
			<a href="https://www.botou.net/aboutus/tuiguang.html" target="_blank" title="泊头生活网广告招商"><img src="template/pc/images/2022112309104176804697_1240_90.png"> </a>
    	</div>
		<div class="yui-clear"></div>
		<!-- 主区域 -->
		<div class="yui-content yui-1200">
			<!-- 最新信息列表 -->
			<div class="latest-posts">

			
			</div>
			<!-- 页面顶部信息列表 -->
			<div class="yui-index">
				<!-- 左侧内容区域 -->
				<div class="yui-index-l">
					<!-- 左侧顶部区域 -->
					<div class="left-top-section">
						<div class="gonggao-wrap">
							<div class="flash-img bg-blue">
								<div id="slider-wrap">
									<ul id="slider">
										<li><img src="/template/pc/images/botou1.png" width="302" height="200" alt="泊头生活网" /></li>
										<li><img src="/template/pc/images/botou2.png" width="302" height="200" alt="泊头信息网" /></li>
										<li><img src="/template/pc/images/botou3.png" width="302" height="200" alt="泊头新闻" /></li>
									</ul>
									<!--controls-->
									<div class="btns" id="next">></div>
									<div class="btns" id="previous"><</div>
									<div id="pagination-wrap">
										<ul></ul>
									</div>
								</div>
							</div>
							<div class="gonggao mt10 bg-white">
								<div class="yui-h-title">
									<h3>网站公告</h3><span><a href="https://www.botou.net/gonggao/">更多</a></span>
								</div>
								<div class="yui-small-list">
									<ul>
										<li><a href="https://www.botou.net/gonggao/50.html" target="_blank">泊头市防范电信诈骗小课堂</a></li>
										<li><a href="https://www.botou.nethttps://www.botou.net/help/tiaoli.html" target="_blank">发布信息请遵守以下内容</a></li>
										<li><a href="https://www.botou.net/gonggao/23.html" target="_blank">四步轻松帮您初识虚假信息</a></li>
										<li><a href="https://www.botou.net/gonggao/23.html" target="_blank">四步轻松帮您初识虚假信息</a></li>
									</ul>
								</div>
							</div>
						</div>
						<div class="news-wrap bg-white">
							<div class="yui-index-topnews">
								<div class="yui-top-img yui-left">
									<img src="/template/pc/images/top-news-bg.png" />

								</div>
								<div class="yui-top-text yui-right">
									<a href="https://www.botou.net/news/114.html" target="_blank">泊头市开展乡村振兴驻村干部能力素质培训...</a>
									<p>              &amp;ensp;&amp;ensp;&amp;ensp;&amp;ensp;为进一步提升驻村干部的政策水平和业务能力，8月2日，泊头市举办乡村振兴驻村干部能力素质培训班，各乡镇分管班子成员、驻村工作队全体成员共计100余人参加培训。副市长姚晓雨出席</p>
								</div>

							</div>
							<div class="yui-clear"></div>
							<div class="yui-index-news">
								<ul>
									<li><span class="yui-lm-name">泊头新闻</span><a href="https://www.botou.net/news/120.html" title="河北省家电焕新潮！看看泊头有哪些适用门店"
											target="_blank">河北省家电焕新潮！看看泊头有哪些适用门店</a><span class="yui-post-time">2024-09-27</span></li>
									<li><span class="yui-lm-name">泊头新闻</span><a href="https://www.botou.net/news/119.html" title="泊头市举行2024年网络安全宣传周启动仪式"
											target="_blank">泊头市举行2024年网络安全宣传周启动仪式</a><span class="yui-post-time">2024-09-23</span></li>
									<li><span class="yui-lm-name">泊头新闻</span><a href="https://www.botou.net/news/118.html" title="泊头市召开《河北省安全生产条例》专题培训会"
											target="_blank">泊头市召开《河北省安全生产条例》专题培训会</a><span class="yui-post-time">2024-09-07</span></li>
									<li><span class="yui-lm-name">泊头新闻</span><a href="https://www.botou.net/news/117.html" title="泊头市王武庄镇走访慰问见义勇为司机赵广阔"
											target="_blank">泊头市王武庄镇走访慰问见义勇为司机赵广阔</a><span class="yui-post-time">2024-08-27</span></li>
									<li><span class="yui-lm-name">泊头新闻</span><a href="https://www.botou.net/news/116.html" title="泊头市医院举办青年医生科普演讲大赛暨医师节表彰大会"
											target="_blank">泊头市医院举办青年医生科普演讲大赛暨医师节表彰大会</a><span class="yui-post-time">2024-08-22</span></li>
									<li><span class="yui-lm-name">泊头新闻</span><a href="https://www.botou.net/news/113.html" title="泊头市召开深化" 五个对接"政企恳谈会"
											target="_blank">泊头市召开深化"五个对接"政企恳谈会</a><span class="yui-post-time">2024-08-04</span></li><li><span class="yui-lm-name">泊头新闻</span><a href="https://www.botou.net/news/113.html" title="泊头市召开深化" 五个对接"政企恳谈会"
											target="_blank">泊头市召开深化"五个对接"政企恳谈会</a><span class="yui-post-time">2024-08-04</span></li><li><span class="yui-lm-name">泊头新闻</span><a href="https://www.botou.net/news/113.html" title="泊头市召开深化" 五个对接"政企恳谈会"
												target="_blank">泊头市召开深化"五个对接"政企恳谈会</a><span class="yui-post-time">2024-08-04</span></li>
								</ul></div>

					</div>
				</div>
				<div class="index-new-list-left bg-white pd10">
					<div class="index-new-title">
						<h3>分类信息MESSAGE</h3><a href="/help/tiaoli.html" target="_blank">信息为何不能审核通过</a>
					</div>
					<div class="info-list mt20">
						<ul class="ding">
							{foreach $topPosts as $post}
							<li>{if $post.category_name}<a href="/{$post.category_pinyin}/" style="color: #999; text-decoration: none; margin-right: 5px;">[{$post.category_name}]</a>{/if}<a href="/{$post.category_pinyin}/{$post.id}.html">{$post.title}</a><span class="ding">顶</span><span class="text-dot">今天</span></li>
							{/foreach}
						</ul>
						<ul>
							{foreach $normalPosts as $post}
							<li>{if $post.category_name}<a href="/{$post.category_pinyin}/" style="color: #999; text-decoration: none; margin-right: 5px;">[{$post.category_name}]</a>{/if}<a href="/{$post.category_pinyin}/{$post.id}.html" title="{$post.title}" target="_blank">{$post.title}</a><span class="text-dot">{$post.updated_at|friendlyTime}</span></li>
							{/foreach}
						</ul>
					</div>
				</div>
			</div>

			<!-- 右侧内容区域 -->
			<div class="yui-index-r">
				<div class="bianmin pd10">
					<div class="yui-h-title">
						<h3>快速导航</h3><span><a href="#">更多</a></span>
					</div>
					<div class="yui-small-list">
						<ul>
							<li><a target="_blank" href="http://www.botou.gov.cn/botou/c100818/listCollect.shtml">泊头概况</a></li>
							<li><a target="_blank" href="http://www.botou.gov.cn/botou/c100821/202204/3b198500b7614957b919ae6d29826e6c.shtml">城市简介</a></li>
							<li><a target="_blank" href="http://czbt.hbzwfw.gov.cn/">政务服务</a></li>
							<li><a target="_blank" href="https://www.botou.net/gongjiao/">泊头公交</a></li>
							<li><a target="_blank" href="https://www.botou.net/gongjiao/">泊头公交</a></li>
							<li><a target="_blank" href="https://www.botou.net/gongjiao/">泊头公交</a></li>
						</ul>
					</div>
				</div>
				<div class="zhuanti">
					<div class="yui-h-title">
						<h3>快速导航</h3><span><a href="#">更多</a></span>
					</div>
					<div class="zhuanti-content mt10">
					
						<div class="zhuanti-item">
							<div class="zhuanti-game">
								<a href="#">
									<div class="game-logo"></div>
									<h4>青少年活动中心</h4>
									<p>丰富多彩的青少年暑期活动</p>
									<div class="people-count">已有2589人参与</div>
								</a>
							</div>
						</div>
					</div>
				</div>

				<!-- 热门信息模块 -->
				<div class="bbs-hot">
					<div class="yui-h-title">
						<h3>热门信息</h3><span><a href="/">更多</a></span>
					</div>
					<div class="yui-small-list">
						<ul>
							{if !empty($hotPosts)}
							{foreach $hotPosts as $post}
							<li>
								<a href="/{$post.category_pinyin}/{$post.id}.html" title="{$post.title}">
									{php}
									$title = $post['title'];
									if (mb_strlen($title) > 30) {
										echo mb_substr($title, 0, 30) . '...';
									} else {
										echo $title;
									}
									{/php}
								</a>
								<span class="view-count">({$post.view_count}次浏览)</span>
							</li>
							{/foreach}
							{else}
							<li><a href="#">暂无热门信息</a></li>
							{/if}
						</ul>
					</div>
				</div>

				<div class="bbs-hot">
					<div class="yui-h-title">
						<h3>资讯索引</h3><span><a href="/news">更多</a></span>
					</div>
					<div class="yui-small-list">
						<ul>
							{if !empty($latestNews)}
							{foreach $latestNews as $news}
							<li>
								<a href="/news/{$news.id}.html" title="{$news.title}">
									{php}
									$title = $news['title'];
									if (mb_strlen($title) > 35) {
										echo mb_substr($title, 0, 35) . '...';
									} else {
										echo $title;
									}
									{/php}
								</a>
								<span class="news-date">({$news.formatted_time})</span>
							</li>
							{/foreach}
							{else}
							<li><a href="#">暂无最新资讯</a></li>
							{/if}
						</ul>
					</div>
				</div>
			</div>
			<div class="yui-clear"></div>
		</div>
	</div>
		<!-- 分类信息区域 end -->
	</div>
	<!-- 主区域 yui-content end-->
	<div class="yui-clear"></div>

	<!-- 常用信息查询 -->
	<div class="yui-1200">
		<div class="bianmin-query bg-white mt10">
			<div class="yui-h-title">
				<h3>常用信息查询</h3>
			</div>
			<div class="query-list">
				<ul>
					<li><a href="https://www.12306.cn" target="_blank">火车票查询</a></li>
					<li><a href="https://www.weather.com.cn" target="_blank">天气预报</a></li>
					<li><a href="https://www.95306.cn" target="_blank">物流查询</a></li>
					<li><a href="http://www.botou.gov.cn/botou/c100821/traffic.shtml" target="_blank">公交线路</a></li>
					<li><a href="http://www.botou.gov.cn/botou/c100821/hospital.shtml" target="_blank">医院导航</a></li>
					<li><a href="http://www.botou.gov.cn/botou/c100821/school.shtml" target="_blank">学校信息</a></li>
					<li><a href="http://www.botou.gov.cn/botou/c100821/market.shtml" target="_blank">商场超市</a></li>
					<li><a href="http://www.botou.gov.cn/botou/c100821/bank.shtml" target="_blank">银行网点</a></li>
					<li><a href="http://www.botou.gov.cn/botou/c100821/express.shtml" target="_blank">快递网点</a></li>
					<li><a href="http://www.botou.gov.cn/botou/c100821/hotel.shtml" target="_blank">宾馆住宿</a></li>
					<li><a href="http://www.botou.gov.cn/botou/c100821/restaurant.shtml" target="_blank">餐饮美食</a></li>
					<li><a href="http://www.botou.gov.cn/botou/c100821/entertainment.shtml" target="_blank">休闲娱乐</a></li>
				</ul>
			</div>
		</div>
	</div>
	<!-- 常用信息查询 end -->

	<!-- 友情链接 -->
	<div class="yui-1200">
		<div class="bianmin-tel bg-white mt10">
			<div class="yui-h-title">
				<h3>便民电话</h3>
			</div>
			<div class="tel-list">
				<div class="tel-item">
					<div class="tel-name">火警</div>
					<div class="tel-number">119</div>
				</div>
				<div class="tel-item">
					<div class="tel-name">报警</div>
					<div class="tel-number">110</div>
				</div>
				<div class="tel-item">
					<div class="tel-name">急救</div>
					<div class="tel-number">120</div>
				</div>
				<div class="tel-item">
					<div class="tel-name">交通事故</div>
					<div class="tel-number">122</div>
				</div>
				<div class="tel-item">
					<div class="tel-name">市长热线</div>
					<div class="tel-number">12345</div>
				</div>
				<div class="tel-item">
					<div class="tel-name">天气预报</div>
					<div class="tel-number">12121</div>
				</div>
				<div class="tel-item">
					<div class="tel-name">供电服务</div>
					<div class="tel-number">95598</div>
				</div>
				<div class="tel-item">
					<div class="tel-name">燃气服务</div>
					<div class="tel-number">96577</div>
				</div>
				<div class="tel-item">
					<div class="tel-name">水务服务</div>
					<div class="tel-number">96585</div>
				</div>
			</div>
		</div>
		<div class="friend-links bg-white pd10">
			<div class="yui-h-title">
				<h3>友情链接</h3><span><a href="#">更多</a></span>
			</div>
			<div class="yui-small-list">
				{php}
				if (function_exists('getCachedFriendLinks')) {
					$friend_links = getCachedFriendLinks(20); // 获取最多20个友情链接
					$this->assign('friend_links', $friend_links);
				}
				{/php}
				
				{if $friend_links}
				<ul>{loop $friend_links $link}
					<li><a href="{$link.url}" target="_blank" >{$link.name}</a></li>
					{/loop}
				</ul>
				{/if}
			</div>
		</div>

		

	</div>
	<!-- 友情链接 end -->

	{include file="footer.htm"}
    <script type="text/javascript" src="/template/pc/js/jquery.min.js"></script>
	<script type="text/javascript" src="/template/pc/js/slide.js"></script>
	<script type="text/javascript" src="/template/pc/js/common.js"></script>
	<script>
		$(function() {
			// 初始化轮播图
			$("#slider-wrap").slider();
		});
	</script>
	</body>
</html>
