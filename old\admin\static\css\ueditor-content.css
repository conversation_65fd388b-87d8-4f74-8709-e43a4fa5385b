/**
 * UEditor 编辑器内容区域样式
 * 此文件会被注入到编辑器的iframe中，控制编辑器内容的显示样式
 */

/* ========== 图片响应式控制 ========== */

/* 所有图片的基础样式 - 防止溢出 */
img {
    max-width: 100% !important;
    height: auto !important;
    display: block;
    margin: 10px auto;
    border-radius: 4px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    cursor: pointer;
    transition: all 0.3s ease;
    /* 防止图片被拖拽时出现虚线边框 */
    outline: none;
    border: none;
}

/* 图片悬停效果 */
img:hover {
    box-shadow: 0 4px 16px rgba(0,0,0,0.15);
    transform: scale(1.02);
}

/* 图片选中状态 */
img.focus {
    border: 2px solid #1b68ff !important;
    box-shadow: 0 0 10px rgba(27, 104, 255, 0.3) !important;
}

/* ========== 图片对齐样式 ========== */

/* 居中对齐图片 - 默认样式 */
img {
    margin: 15px auto !important;
    display: block !important;
}

/* 段落中居中对齐的图片 */
p[style*="text-align: center"] img,
p[style*="text-align:center"] img,
div[style*="text-align: center"] img,
div[style*="text-align:center"] img {
    margin: 15px auto !important;
    display: block !important;
}

/* 左对齐图片 */
img[align="left"],
img.image-left,
img[style*="float: left"],
img[style*="float:left"] {
    float: left !important;
    margin: 10px 15px 10px 0 !important;
    display: inline !important;
}

/* 右对齐图片 */
img[align="right"],
img.image-right,
img[style*="float: right"],
img[style*="float:right"] {
    float: right !important;
    margin: 10px 0 10px 15px !important;
    display: inline !important;
}

/* 居中对齐图片 */
img[align="center"],
img.image-center {
    display: block !important;
    margin: 15px auto !important;
    float: none !important;
    text-align: center;
}

/* 无对齐（默认） */
img[align="none"],
img.image-none {
    display: block !important;
    margin: 15px auto !important;
    float: none !important;
}

/* ========== 段落和容器样式 ========== */

/* 段落样式 - 确保清除浮动 */
p {
    line-height: 1.6;
    margin: 10px 0;
    word-wrap: break-word;
    overflow-wrap: break-word;
}

/* 清除浮动 */
p:after {
    content: "";
    display: table;
    clear: both;
}

/* 包含图片的段落 */
p img {
    vertical-align: top;
}

/* ========== 表格中的图片 ========== */

/* 表格中的图片样式 */
table img {
    max-width: 100% !important;
    height: auto !important;
    margin: 5px auto;
    display: block;
}

/* 表格单元格样式 */
td, th {
    word-wrap: break-word;
    overflow-wrap: break-word;
}

/* ========== 视频和媒体元素 ========== */

/* 视频响应式 */
video {
    max-width: 100% !important;
    height: auto !important;
    display: block;
    margin: 15px auto;
}

/* iframe响应式（用于嵌入视频等） */
iframe {
    max-width: 100% !important;
    margin: 15px auto;
    display: block;
}

/* ========== 其他媒体元素 ========== */

/* 音频元素 */
audio {
    width: 100%;
    max-width: 500px;
    margin: 15px auto;
    display: block;
}

/* ========== 编辑器整体样式 ========== */

/* 编辑器主体样式 */
body {
    font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
    font-size: 14px;
    line-height: 1.6;
    color: #333;
    background: #fff;
    margin: 10px;
    padding: 0;
    word-wrap: break-word;
    overflow-wrap: break-word;
}

/* 防止内容溢出 */
* {
    max-width: 100%;
    box-sizing: border-box;
}

/* ========== 图片工具栏样式 ========== */

/* UEditor图片工具栏 */
.edui-image-toolbar {
    background: rgba(0,0,0,0.8);
    border-radius: 4px;
    padding: 5px;
}

.edui-image-toolbar .edui-button {
    background: transparent;
    border: none;
    color: #fff;
    margin: 0 2px;
    padding: 5px;
    border-radius: 3px;
}

.edui-image-toolbar .edui-button:hover {
    background: rgba(255,255,255,0.2);
}

/* ========== 响应式断点 ========== */

/* 移动端适配 */
@media (max-width: 768px) {
    img {
        margin: 8px auto !important;
    }
    
    img[align="left"],
    img[align="right"] {
        float: none !important;
        display: block !important;
        margin: 10px auto !important;
    }
    
    body {
        font-size: 13px;
        margin: 5px;
    }
}

/* 小屏幕设备 */
@media (max-width: 480px) {
    img {
        margin: 5px auto !important;
        border-radius: 2px;
    }
    
    body {
        font-size: 12px;
        margin: 3px;
    }
}

/* ========== 打印样式 ========== */

@media print {
    img {
        max-width: 100% !important;
        height: auto !important;
        page-break-inside: avoid;
        margin: 10px 0;
        box-shadow: none;
        border-radius: 0;
    }
    
    img:hover {
        transform: none;
        box-shadow: none;
    }
}
