<?php
/**
 * 缓存状态检查脚本
 * 用于检查缓存系统运行状态和性能指标
 * 
 * 使用方法：
 * 1. 命令行：php cache_status.php
 * 2. Web访问：http://yourdomain.com/scripts/cache_status.php?key=your_secret_key
 */

// 定义安全常量
define('IN_BTMPS', true);

// 获取项目根目录
$root_path = dirname(__DIR__) . '/';
define('ROOT_PATH', $root_path);
define('INCLUDE_PATH', ROOT_PATH . 'include/');
define('DATA_PATH', ROOT_PATH . 'data/');

// 加载必要的文件
require_once(ROOT_PATH . 'config/config.db.php');
require_once(ROOT_PATH . 'config/config.inc.php');
require_once(INCLUDE_PATH . 'cache.class.php');

/**
 * 缓存状态检查类
 */
class CacheStatus {
    private $config;
    
    public function __construct() {
        global $config;
        $this->config = $config;
    }
    
    /**
     * 获取缓存统计信息
     */
    public function getCacheStats() {
        $cache_dir = DATA_PATH . 'cache/';
        $stats = [
            'total_files' => 0,
            'total_size' => 0,
            'valid_files' => 0,
            'expired_files' => 0,
            'cache_types' => [],
            'oldest_cache' => null,
            'newest_cache' => null,
            'avg_file_size' => 0
        ];
        
        if (!is_dir($cache_dir)) {
            return $stats;
        }
        
        $files = glob($cache_dir . '*.cache');
        $stats['total_files'] = count($files);
        
        $file_sizes = [];
        $cache_times = [];
        
        foreach ($files as $file) {
            $file_size = filesize($file);
            $stats['total_size'] += $file_size;
            $file_sizes[] = $file_size;
            
            // 分析缓存类型
            $filename = basename($file, '.cache');
            $cache_type = $this->getCacheType($filename);
            if (!isset($stats['cache_types'][$cache_type])) {
                $stats['cache_types'][$cache_type] = 0;
            }
            $stats['cache_types'][$cache_type]++;
            
            // 检查缓存是否过期
            $cache_data = @include $file;
            if (is_array($cache_data) && isset($cache_data['expire'])) {
                $cache_times[] = $cache_data['expire'];
                
                if (time() > $cache_data['expire']) {
                    $stats['expired_files']++;
                } else {
                    $stats['valid_files']++;
                }
            }
        }
        
        // 计算平均文件大小
        if (count($file_sizes) > 0) {
            $stats['avg_file_size'] = array_sum($file_sizes) / count($file_sizes);
        }
        
        // 找出最老和最新的缓存
        if (!empty($cache_times)) {
            $stats['oldest_cache'] = min($cache_times);
            $stats['newest_cache'] = max($cache_times);
        }
        
        return $stats;
    }
    
    /**
     * 根据文件名判断缓存类型
     */
    private function getCacheType($filename) {
        if (strpos($filename, 'index_page') !== false) {
            return 'index';
        } elseif (strpos($filename, 'category_posts') !== false) {
            return 'category';
        } elseif (strpos($filename, 'region_posts') !== false) {
            return 'region';
        } elseif (strpos($filename, 'post_detail') !== false) {
            return 'post';
        } elseif (strpos($filename, 'categories_all') !== false) {
            return 'categories_data';
        } elseif (strpos($filename, 'regions_all') !== false) {
            return 'regions_data';
        } elseif (strpos($filename, 'search_') !== false) {
            return 'search';
        } else {
            return 'other';
        }
    }
    
    /**
     * 检查缓存配置
     */
    public function getCacheConfig() {
        return [
            'cache_enable' => $this->config['cache_enable'] ?? 0,
            'cache_index' => $this->config['cache_index'] ?? 0,
            'cache_list' => $this->config['cache_list'] ?? 0,
            'cache_post' => $this->config['cache_post'] ?? 0,
            'cache_category' => $this->config['cache_category'] ?? 0,
            'cache_region' => $this->config['cache_region'] ?? 0,
            'cache_search' => $this->config['cache_search'] ?? 0,
            'cache_data' => $this->config['cache_data'] ?? 0,
            'cache_compress' => $this->config['cache_compress'] ?? 0,
            'clear_cache_time' => $this->config['clear_cache_time'] ?? 24,
        ];
    }
    
    /**
     * 检查最近的预热日志
     */
    public function getRecentWarmupStatus() {
        $log_dir = DATA_PATH . 'logs/';
        $today_log = $log_dir . 'cache_warmup_' . date('Y-m-d') . '.log';
        $yesterday_log = $log_dir . 'cache_warmup_' . date('Y-m-d', strtotime('-1 day')) . '.log';
        
        $status = [
            'last_execution' => null,
            'last_status' => 'unknown',
            'last_duration' => null,
            'last_stats' => null,
            'today_executions' => 0,
            'recent_errors' => []
        ];
        
        // 检查今天的日志
        if (file_exists($today_log)) {
            $content = file_get_contents($today_log);
            $status = $this->parseWarmupLog($content, $status);
        }
        
        // 如果今天没有执行记录，检查昨天的
        if ($status['last_execution'] === null && file_exists($yesterday_log)) {
            $content = file_get_contents($yesterday_log);
            $status = $this->parseWarmupLog($content, $status);
        }
        
        return $status;
    }
    
    /**
     * 解析预热日志
     */
    private function parseWarmupLog($content, $status) {
        $lines = explode("\n", $content);
        
        foreach ($lines as $line) {
            if (empty(trim($line))) continue;
            
            // 提取时间戳
            if (preg_match('/\[(\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2})\]/', $line, $matches)) {
                $timestamp = strtotime($matches[1]);
                
                if (strpos($line, '缓存预热任务开始执行') !== false) {
                    $status['today_executions']++;
                    $status['last_execution'] = $timestamp;
                }
                
                if (strpos($line, '缓存预热完成') !== false) {
                    $status['last_status'] = 'success';
                }
                
                if (strpos($line, '执行时间:') !== false) {
                    if (preg_match('/执行时间:\s*([\d.]+)\s*秒/', $line, $time_matches)) {
                        $status['last_duration'] = floatval($time_matches[1]);
                    }
                }
                
                if (strpos($line, '总页面数:') !== false || 
                    strpos($line, '成功页面:') !== false || 
                    strpos($line, '失败页面:') !== false) {
                    if (!$status['last_stats']) {
                        $status['last_stats'] = [];
                    }
                    
                    if (preg_match('/(总页面数|成功页面|失败页面):\s*(\d+)/', $line, $stat_matches)) {
                        $status['last_stats'][$stat_matches[1]] = intval($stat_matches[2]);
                    }
                }
                
                // 检查错误信息
                if (strpos($line, '失败') !== false || strpos($line, '错误') !== false) {
                    $status['recent_errors'][] = [
                        'time' => $timestamp,
                        'message' => trim(preg_replace('/\[.*?\]/', '', $line))
                    ];
                }
            }
        }
        
        return $status;
    }
    
    /**
     * 格式化字节数
     */
    public function formatBytes($bytes, $precision = 2) {
        $units = array('B', 'KB', 'MB', 'GB', 'TB');
        
        for ($i = 0; $bytes > 1024 && $i < count($units) - 1; $i++) {
            $bytes /= 1024;
        }
        
        return round($bytes, $precision) . ' ' . $units[$i];
    }
    
    /**
     * 输出状态报告
     */
    public function generateReport($format = 'text') {
        $cache_stats = $this->getCacheStats();
        $cache_config = $this->getCacheConfig();
        $warmup_status = $this->getRecentWarmupStatus();
        
        if ($format === 'json') {
            return json_encode([
                'cache_stats' => $cache_stats,
                'cache_config' => $cache_config,
                'warmup_status' => $warmup_status,
                'timestamp' => time()
            ], JSON_PRETTY_PRINT);
        }
        
        // 文本格式报告
        $report = [];
        $report[] = "=== 缓存系统状态报告 ===";
        $report[] = "生成时间: " . date('Y-m-d H:i:s');
        $report[] = "";
        
        // 缓存统计
        $report[] = "=== 缓存统计 ===";
        $report[] = "总文件数: " . $cache_stats['total_files'];
        $report[] = "总大小: " . $this->formatBytes($cache_stats['total_size']);
        $report[] = "有效缓存: " . $cache_stats['valid_files'];
        $report[] = "过期缓存: " . $cache_stats['expired_files'];
        $report[] = "平均文件大小: " . $this->formatBytes($cache_stats['avg_file_size']);
        
        if ($cache_stats['oldest_cache']) {
            $report[] = "最老缓存: " . date('Y-m-d H:i:s', $cache_stats['oldest_cache']);
        }
        if ($cache_stats['newest_cache']) {
            $report[] = "最新缓存: " . date('Y-m-d H:i:s', $cache_stats['newest_cache']);
        }
        
        $report[] = "";
        $report[] = "=== 缓存类型分布 ===";
        foreach ($cache_stats['cache_types'] as $type => $count) {
            $report[] = ucfirst($type) . ": " . $count . " 个文件";
        }
        
        $report[] = "";
        $report[] = "=== 缓存配置 ===";
        $report[] = "缓存启用: " . ($cache_config['cache_enable'] ? '是' : '否');
        $report[] = "首页缓存时间: " . $cache_config['cache_index'] . " 秒";
        $report[] = "列表页缓存时间: " . $cache_config['cache_list'] . " 秒";
        $report[] = "详情页缓存时间: " . $cache_config['cache_post'] . " 秒";
        $report[] = "分类页缓存时间: " . $cache_config['cache_category'] . " 秒";
        $report[] = "地区页缓存时间: " . $cache_config['cache_region'] . " 秒";
        $report[] = "搜索缓存时间: " . $cache_config['cache_search'] . " 秒";
        $report[] = "数据缓存时间: " . $cache_config['cache_data'] . " 秒";
        
        $report[] = "";
        $report[] = "=== 预热状态 ===";
        if ($warmup_status['last_execution']) {
            $report[] = "最后执行: " . date('Y-m-d H:i:s', $warmup_status['last_execution']);
            $report[] = "执行状态: " . ($warmup_status['last_status'] === 'success' ? '成功' : '未知');
            if ($warmup_status['last_duration']) {
                $report[] = "执行时长: " . $warmup_status['last_duration'] . " 秒";
            }
            $report[] = "今日执行次数: " . $warmup_status['today_executions'];
            
            if ($warmup_status['last_stats']) {
                $report[] = "最后统计:";
                foreach ($warmup_status['last_stats'] as $key => $value) {
                    $report[] = "  " . $key . ": " . $value;
                }
            }
        } else {
            $report[] = "未找到预热执行记录";
        }
        
        if (!empty($warmup_status['recent_errors'])) {
            $report[] = "";
            $report[] = "=== 最近错误 ===";
            foreach (array_slice($warmup_status['recent_errors'], -5) as $error) {
                $report[] = date('Y-m-d H:i:s', $error['time']) . ": " . $error['message'];
            }
        }
        
        return implode("\n", $report);
    }
}

// 安全检查
$secret_key = 'your_secret_key_here'; // 请修改为您的密钥
$is_cli = php_sapi_name() === 'cli';
$is_authorized = false;

if ($is_cli) {
    // 命令行模式直接允许
    $is_authorized = true;
} elseif (isset($_GET['key']) && $_GET['key'] === $secret_key) {
    // Web模式需要密钥验证
    $is_authorized = true;
}

if (!$is_authorized) {
    http_response_code(403);
    die('Access Denied');
}

// 获取输出格式
$format = $_GET['format'] ?? 'text';
if (!in_array($format, ['text', 'json'])) {
    $format = 'text';
}

// 设置响应头
if (!$is_cli) {
    if ($format === 'json') {
        header('Content-Type: application/json; charset=utf-8');
    } else {
        header('Content-Type: text/plain; charset=utf-8');
    }
}

// 生成并输出报告
try {
    $status_checker = new CacheStatus();
    $report = $status_checker->generateReport($format);
    echo $report;
} catch (Exception $e) {
    if ($format === 'json') {
        echo json_encode(['error' => $e->getMessage()]);
    } else {
        echo "错误: " . $e->getMessage();
    }
    exit(1);
}
?>
