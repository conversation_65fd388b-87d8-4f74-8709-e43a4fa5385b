<!-- 简约风格头部模板 -->
<!-- 使用时传入title参数 -->
<header class="simple-header">
    <div class="header-inner">
        <div class="header-left">
            <a href="javascript:history.back();" class="header-back">
                <i class="fas fa-chevron-left"></i>
            </a>
        </div>
        <div class="header-title">{$title|default:'页面标题'}</div>
        <div class="header-right">
            <div class="header-menu-btn">
                <i class="fas fa-bars"></i>
            </div>
        </div>
    </div>
</header>

<style>
    .simple-header {
        background-color: #FFFFFF; 
        color: #333;
        border-bottom: 1px solid #eeeeee;
        box-shadow: 0 1px 3px rgba(0,0,0,0.05);
    }
    
    .simple-header .header-back,
    .simple-header .header-menu-btn {
        color: #333;
    }
    
    .simple-header .header-title {
        color: #333;
    }
    
    .simple-header .header-back:active,
    .simple-header .header-menu-btn:active {
        background-color: rgba(0,0,0,0.05);
    }
</style>