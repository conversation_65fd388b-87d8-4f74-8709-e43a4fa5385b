<?php
define('IN_BTMPS', true);
// 记录页面开始执行的时间
$startTime = microtime(true);

// 引入公共文件
require_once './include/common.inc.php';

// 获取首页配置的展示数量
$indexSize = isset($config['index_size']) ? intval($config['index_size']) : 10;
$hotPostsCount = isset($config['hot_posts_count']) ? intval($config['hot_posts_count']) : 8;
$newsIndexCount = isset($config['news_index_count']) ? intval($config['news_index_count']) : 10;

// 获取缓存设置
$cache_enable = isset($config['cache_enable']) ? intval($config['cache_enable']) : 1;
$cache_index_time = isset($config['cache_index']) ? intval($config['cache_index']) : 3600;

// 构建缓存键名（区分不同端）
$template_suffix = TEMPLATE_DIR; // pc, m, wx, app
$cache_key = "index_page_{$template_suffix}";

$cached_data = false;
if ($cache_enable && $cache_index_time > 0) {
    // 尝试从缓存获取首页数据
    $cached_data = cache_get($cache_key);
}

if ($cached_data !== false) {
    // 从缓存中获取数据
    $categories = $cached_data['categories'];
    $topPosts = $cached_data['topPosts'];
    $normalPosts = $cached_data['normalPosts'];
    $hotPosts = isset($cached_data['hotPosts']) ? $cached_data['hotPosts'] : array();
    $latestNews = isset($cached_data['latestNews']) ? $cached_data['latestNews'] : array();
} else {
    // 缓存不存在，从数据库获取数据

    // 使用全局缓存的分类数据
    $categories = $GLOBALS['cached_categories'];

    // 获取首页置顶信息
    $topPosts = getTopPosts(10);

    // 获取首页最新信息（非置顶）
    $normalPosts = getNormalPosts($indexSize);

    // 获取热门信息（按浏览次数排序）
    $hotPosts = getHotPosts($hotPostsCount);

    // 获取最新新闻（用于资讯索引）
    $latestNews = getLatestNews($newsIndexCount);

    // 如果启用缓存，将数据缓存起来
    if ($cache_enable && $cache_index_time > 0) {
        $cache_data = array(
            'categories' => $categories,
            'topPosts' => $topPosts,
            'normalPosts' => $normalPosts,
            'hotPosts' => $hotPosts,
            'latestNews' => $latestNews
        );
        cache_set($cache_key, $cache_data, $cache_index_time);
    }
}

// 分配变量到模板
$tpl->assign(array(
    'categories' => $categories,
    'topPosts' => $topPosts,
    'normalPosts' => $normalPosts,
    'hotPosts' => $hotPosts,
    'latestNews' => $latestNews,
    'settings' => $GLOBALS['settings'],
    'navList' => $GLOBALS['navList'],
    'current_page' => 'index'
));

// 显示模板
$tpl->display('index.htm');

// 计算页面执行耗时
$endTime = microtime(true);
$executionTime = round(($endTime - $startTime) * 1000, 2); // 转换为毫秒并保留2位小数

// 显示执行时间（可选）
echo "<div style='text-align:center;color:#999;padding:10px 0;'>页面执行时间: {$executionTime}ms</div>";




