<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <title>刷新信息确认 - <?php echo $site_name; ?></title>
    <link rel="stylesheet" href="/static/m/css/style.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css">
    <style>
        :root {
            --primary-color: #ff6600;
            --primary-light: #fff0e8;
            --secondary-color: #4a89dc;
            --danger-color: #e53935;
            --success-color: #4caf50;
            --text-dark: #333;
            --text-medium: #666;
            --text-light: #999;
            --border-color: #eee;
            --bg-color: #f5f5f5;
            --animation-time: 0.3s;
        }
        
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            background-color: #f8f9fa;
            font-family: 'Microsoft YaHei', -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
            color: var(--text-dark);
            line-height: 1.5;
            -webkit-font-smoothing: antialiased;
            -moz-osx-font-smoothing: grayscale;
        }
        
        .header {
            background-color: var(--primary-color);
            padding: 15px 0;
            text-align: center;
            color: white;
            position: relative;
            width: 100%;
        }
        
        .header h1 {
            margin: 0;
            font-size: 18px;
            font-weight: bold;
        }
        
        .back-btn {
            position: absolute;
            left: 15px;
            top: 50%;
            transform: translateY(-50%);
            color: white;
            text-decoration: none;
            font-size: 16px;
        }
        
        .refresh-note {
            background-color: var(--primary-light);
            border-left: 3px solid var(--primary-color);
            padding: 15px;
            font-size: 14px;
            color: var(--text-medium);
            margin-bottom: 0;
            line-height: 1.5;
            width: 100%;
        }
        
        .refresh-note i {
            margin-right: 5px;
            color: var(--primary-color);
        }
        
        .info-panel {
            background-color: white;
            padding: 15px;
            margin: 0;
            width: 100%;
        }
        
        .panel-title {
            margin-top: 0;
            margin-bottom: 15px;
            font-size: 16px;
            color: var(--text-dark);
            font-weight: 600;
        }
        
        .info-item {
            margin-bottom: 12px;
            display: flex;
            border-bottom: 1px solid var(--border-color);
            padding-bottom: 12px;
        }
        
        .info-item:last-child {
            border-bottom: none;
            margin-bottom: 0;
            padding-bottom: 0;
        }
        
        .info-label {
            font-weight: 500;
            width: 80px;
            color: var(--text-medium);
            font-size: 14px;
        }
        
        .info-value {
            flex: 1;
            font-size: 14px;
            color: var(--text-dark);
        }
        
        .action-section {
            background-color: white;
            padding: 15px;
            margin-top: 10px;
            width: 100%;
        }
        
        .btn-row {
            display: flex;
        }
        
        .btn {
            flex: 1;
            padding: 14px 0;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-size: 16px;
            text-align: center;
            text-decoration: none;
            font-weight: bold;
            transition: all var(--animation-time) ease;
        }
        
        .btn:active {
            transform: translateY(1px);
        }
        
        .btn-primary {
            background-color: var(--primary-color);
            color: white;
            margin-right: 10px;
            box-shadow: 0 2px 6px rgba(255, 102, 0, 0.2);
        }
        
        .btn-primary:hover {
            background-color: #e55c00;
        }
        
        .btn-secondary {
            background-color: #f0f0f0;
            color: var(--text-medium);
            border: 1px solid #ddd;
        }
        
        .btn-secondary:hover {
            background-color: #e8e8e8;
        }
        
        .bottom-nav {
            position: fixed;
            bottom: 0;
            left: 0;
            width: 100%;
            background: white;
            display: flex;
            box-shadow: 0 -2px 10px rgba(0,0,0,0.08);
            z-index: 100;
            height: 56px;
        }
        
        .nav-item {
            flex: 1;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            color: var(--text-medium);
            text-decoration: none;
            font-size: 12px;
            font-weight: 500;
            transition: color var(--animation-time) ease;
            position: relative;
        }
        
        .nav-item.active {
            color: var(--primary-color);
            font-weight: 600;
        }
        
        .nav-item i {
            font-size: 18px;
            margin-bottom: 4px;
        }
        
        .nav-item.active::after {
            content: '';
            position: absolute;
            bottom: 0;
            width: 20px;
            height: 3px;
            background-color: var(--primary-color);
            border-radius: 10px 10px 0 0;
        }
        
        .nav-left, .nav-right {
            display: flex;
            align-items: center;
            padding: 0 10px;
        }
        
        .nav-left {
            justify-content: flex-start;
        }
        
        .nav-right {
            justify-content: flex-end;
        }
        
        .nav-btn {
            padding: 8px 14px;
            background-color: var(--primary-color);
            color: white;
            border-radius: 20px;
            text-decoration: none;
            font-size: 14px;
            font-weight: 600;
            margin: 0 5px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
            transition: all var(--animation-time) ease;
        }
        
        .nav-btn:active {
            transform: translateY(1px);
        }
        
        .nav-btn.call {
            background-color: var(--success-color);
        }
    </style>
</head>
<body>
    <div class="header">
        <a href="javascript:history.back()" class="back-btn"><i class="fas fa-arrow-left"></i></a>
        <h1>刷新信息确认</h1>
    </div>

    <div class="refresh-note">
        <i class="fas fa-info-circle"></i> 刷新后，该信息将更新发布时间为当前时间，并显示在列表顶部。频繁刷新可能会被系统判定为违规操作。
    </div>
    
    <div class="info-panel">
        <h3 class="panel-title">您即将刷新以下信息：</h3>
        
        <div class="info-item">
            <div class="info-label">标题：</div>
            <div class="info-value"><?php echo $post['title']; ?></div>
        </div>
        
        <div class="info-item">
            <div class="info-label">分类：</div>
            <div class="info-value"><?php echo $post['category_name']; ?></div>
        </div>
        
        <div class="info-item">
            <div class="info-label">发布时间：</div>
            <div class="info-value"><?php echo date('Y-m-d H:i', strtotime($post['created_at'])); ?></div>
        </div>
        
        <div class="info-item">
            <div class="info-label">联系人：</div>
            <div class="info-value"><?php echo $post['contact_name']; ?></div>
        </div>
    </div>
    
    <div class="action-section">
        <div class="btn-row">
            <form action="/manage.php" method="get" style="flex: 1; margin-right: 10px;">
                <input type="hidden" name="id" value="<?php echo $post['id']; ?>">
                <input type="hidden" name="action" value="refresh">
                <input type="hidden" name="confirm" value="1">
                <?php if (!empty($password)): ?>
                <input type="hidden" name="password" value="<?php echo $password; ?>">
                <?php endif; ?>
                <button type="submit" class="btn btn-primary">确认刷新</button>
            </form>
            <a href="/<?php echo $post['category_pinyin']; ?>/<?php echo $post['id']; ?>.html" class="btn btn-secondary">取消</a>
        </div>
    </div>
    
    <div style="height: 65px;"></div><!-- 底部导航占位 -->
    
    <div class="bottom-nav">
        <a href="/" class="nav-item">
            <i class="fas fa-home"></i>
            <span>首页</span>
        </a>
        <a href="/<?php echo $post['category_pinyin']; ?>/" class="nav-item">
            <i class="fas fa-th-large"></i>
            <span>分类</span>
        </a>
        <a href="/post.php" class="nav-item">
            <i class="fas fa-plus-circle"></i>
            <span>发布</span>
        </a>
        <a href="/search.php" class="nav-item">
            <i class="fas fa-search"></i>
            <span>搜索</span>
        </a>
        <a href="/manage.php?id=<?php echo $post['id']; ?>" class="nav-item active">
            <i class="fas fa-cog"></i>
            <span>管理</span>
        </a>
    </div>
</body>
</html>