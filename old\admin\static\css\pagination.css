/* 分页样式 */
.pagination-wrapper {
    margin-top: 20px;
    padding-top: 15px;
    border-top: 1px solid #e9ecef;
}

.pagination-container {
    display: flex;
    align-items: center;
    justify-content: center;
}

.pagination {
    display: flex;
    align-items: center;
    list-style: none;
    padding: 0;
    margin: 0;
}

.pagination li {
    margin: 0 2px;
}

.pagination li a, 
.pagination li span {
    display: inline-block;
    padding: 6px 12px;
    border: 1px solid #dee2e6;
    border-radius: 4px;
    color: #1b68ff;
    background-color: #fff;
    text-decoration: none;
    transition: all 0.2s ease;
}

.pagination li a:hover {
    color: #0045ce;
    background-color: #e9ecef;
    border-color: #dee2e6;
}

.pagination li.active span {
    background-color: #1b68ff;
    color: #fff;
    border-color: #1b68ff;
}

.pagination li.disabled span {
    color: #6c757d;
    background-color: #fff;
    border-color: #dee2e6;
    cursor: not-allowed;
}

.pagination-total {
    margin-left: 15px;
    color: #6c757d;
    font-size: 14px;
}

/* 标签样式 */
.label {
    display: inline-block;
    padding: 3px 6px;
    font-size: 12px;
    font-weight: 600;
    line-height: 1;
    text-align: center;
    white-space: nowrap;
    vertical-align: baseline;
    border-radius: 4px;
    margin-left: 5px;
}

.label-danger {
    background-color: #f82f58;
    color: #fff;
}

.label-success {
    background-color: #3ad29f;
    color: #fff;
}

.label-warning {
    background-color: #eea303;
    color: #fff;
}

/* 表格样式增强 */
.content-table {
    width: 100%;
    border-collapse: collapse;
}

.content-table th,
.content-table td {
    padding: 12px;
    text-align: left;
    border-bottom: 1px solid #dee2e6;
}

.content-table th {
    background-color: #f8f9fa;
    font-weight: 600;
    color: #343a40;
}

.content-table tr:hover {
    background-color: #f8f9fa;
}

.col-id {
    width: 80px;
    text-align: center;
}

.col-category {
    width: 150px;
}

.col-time {
    width: 160px;
}

.col-clicks {
    width: 80px;
    text-align: center;
}

.col-actions {
    width: 180px;
    text-align: right;
}

.info-actions {
    display: flex;
    justify-content: flex-end;
    gap: 5px;
} 