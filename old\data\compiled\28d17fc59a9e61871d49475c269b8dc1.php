<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php if(null !== ($page_title ?? null)): ?><?php echo $page_title ?? ""; ?> - <?php endif; ?>分类信息网站后台管理</title>
    <link href="../static/font-awesome/css/all.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="static/css/admin_clean.css?v=<?php echo time(); ?>" rel="stylesheet">
    <link href="static/css/pagination.css" rel="stylesheet">
    <link href="../static/css/image-compress.css" rel="stylesheet">
</head>
<body>
    <div class="wrapper" id="wrapper">
        <!-- 侧边栏 -->
        <div class="sidebar" id="sidebar">
            <div class="sidebar-header">
                <a href="index.php" class="logo">
                    <i class="fas fa-cube"></i>
                    <span>管理系统</span>
                </a>
            </div>
            <!-- 侧边栏菜单 -->
<div class="menu-item <?php if($current_page == 'index'): ?>active<?php endif; ?>">
    <a href="index.php">
        <i class="fas fa-home"></i>
        <span>控制面板</span>
    </a>
</div>

<div class="menu-item <?php if($current_page == 'category'): ?>active<?php endif; ?>">
    <a href="category.php">
        <i class="fas fa-list"></i>
        <span>分类管理</span>
    </a>
</div>

<div class="menu-item <?php if($current_page == 'region'): ?>active<?php endif; ?>">
    <a href="region.php">
        <i class="fas fa-map-marker-alt"></i>
        <span>区域管理</span>
    </a>
</div>

<div class="menu-item <?php if($current_page == 'info'): ?>active<?php endif; ?>">
    <a href="info.php">
        <i class="fas fa-file-alt"></i>
        <span>信息管理</span>
    </a>
</div>

<div class="menu-item <?php if($current_page == 'news_category'): ?>active<?php endif; ?>">
    <a href="news_category.php">
        <i class="fas fa-newspaper"></i>
        <span>新闻栏目</span>
    </a>
</div>

<div class="menu-item <?php if($current_page == 'news'): ?>active<?php endif; ?>">
    <a href="news.php">
        <i class="fas fa-edit"></i>
        <span>新闻管理</span>
    </a>
</div>

<div class="menu-item <?php if($current_page == 'pages'): ?>active<?php endif; ?>">
    <a href="pages.php">
        <i class="fas fa-file-alt"></i>
        <span>单页管理</span>
    </a>
</div>

<div class="menu-item <?php if($current_page == 'links'): ?>active<?php endif; ?>">
    <a href="links.php">
        <i class="fas fa-link"></i>
        <span>友情链接</span>
    </a>
</div>

<div class="menu-item <?php if($current_page == 'report'): ?>active<?php endif; ?>">
    <a href="report.php">
        <i class="fas fa-flag"></i>
        <span>举报管理</span>
    </a>
</div>

<div class="menu-item <?php if($current_page == 'admin'): ?>active<?php endif; ?>">
    <a href="admin.php">
        <i class="fas fa-user-shield"></i>
        <span>管理员管理</span>
    </a>
</div>

<div class="menu-item <?php if($current_page == 'operation_logs'): ?>active<?php endif; ?>">
    <a href="operation_logs.php">
        <i class="fas fa-history"></i>
        <span>操作日志</span>
    </a>
</div>

<div class="menu-item <?php if($current_page == 'mobile_security'): ?>active<?php endif; ?>">
    <a href="mobile_security.php">
        <i class="fas fa-shield-alt"></i>
        <span>手机号安全</span>
    </a>
</div>

<div class="menu-item <?php if($current_page == 'setting'): ?>active<?php endif; ?>">
    <a href="setting.php">
        <i class="fas fa-cog"></i>
        <span>系统设置</span>
    </a>
</div>

<div class="menu-item <?php if($current_page == 'cache_manager'): ?>active<?php endif; ?>">
    <a href="cache_manager.php">
        <i class="fas fa-memory"></i>
        <span>缓存管理</span>
    </a>
</div>



<div class="menu-item <?php if($current_page == 'db_backup'): ?>active<?php endif; ?>">
    <a href="db_backup.php">
        <i class="fas fa-database"></i>
        <span>数据库备份</span>
    </a>
</div>
        </div>

        <!-- 顶部导航 -->
<div class="top-nav">
    <div class="nav-left">
        <div class="toggle-sidebar" id="toggle-sidebar">
            <i class="fas fa-bars"></i>
        </div>
        <div class="breadcrumb">
            <span class="admin-badge"><?php echo (isset($admin['username'])) ? $admin['username'] : ""; ?></span>
            <i class="fas fa-chevron-right"></i>
            <span>控制台</span>
            <?php if(null !== ($breadcrumb ?? null)): ?>
            <i class="fas fa-chevron-right"></i>
            <span><?php echo $breadcrumb ?? ""; ?></span>
            <?php endif; ?>
        </div>
    </div>
    <div class="nav-right">
        <div class="nav-item" id="clear-cache-btn" title="清理所有缓存">
            <i class="fas fa-trash-alt"></i>
        </div>
        <div class="nav-item" title="前台首页">
            <a href="../" target="_blank" style="color:inherit;text-decoration:none;">
                <i class="fas fa-home"></i>
            </a>
        </div>
        <div class="user-item">
            <div class="user-avatar"><i class="fas fa-user"></i></div>
            <span class="user-name"><?php echo (isset($admin['username'])) ? $admin['username'] : ""; ?></span>
            <a href="logout.php" class="logout-link" title="退出登录">
                <i class="fas fa-sign-out-alt"></i>
            </a>
        </div>
    </div>
</div>

<!-- 清理缓存功能的遮罩层和对话框 -->
<div id="cache-overlay" style="display:none; position:fixed; top:0; left:0; width:100%; height:100%; background:rgba(0,0,0,0.5); z-index:2000;"></div>
<div id="cache-modal" style="display:none; position:fixed; top:50%; left:50%; transform:translate(-50%,-50%); background:#fff; border-radius:8px; box-shadow:0 4px 20px rgba(0,0,0,0.2); width:300px; padding:20px; z-index:2001;">
    <div style="margin-bottom:15px; font-size:16px; font-weight:600;">确认清理缓存</div>
    <p style="margin-bottom:20px; font-size:14px; color:#666;">此操作将清理所有缓存，包括：</p>
    <ul style="margin-bottom:20px; padding-left:20px; font-size:14px; color:#666;">
        <li>页面缓存</li>
        <li>数据缓存</li>
        <li>模板编译文件</li>
    </ul>
    <div style="display:flex; justify-content:flex-end; gap:10px;">
        <button id="cancel-clear-cache" style="padding:8px 16px; border:1px solid #ddd; background:#fff; border-radius:4px; cursor:pointer;">取消</button>
        <button id="confirm-clear-cache" style="padding:8px 16px; border:none; background:#dc3545; color:#fff; border-radius:4px; cursor:pointer;">确认清理</button>
    </div>
</div>

<!-- 成功提示框 -->
<div id="success-toast" style="display:none; position:fixed; top:50%; left:50%; transform:translate(-50%,-50%); background:#28a745; color:#fff; padding:15px 25px; border-radius:6px; box-shadow:0 4px 12px rgba(0,0,0,0.15); z-index:2002; font-size:14px;">
    <i class="fas fa-check-circle" style="margin-right:8px;"></i>
    缓存清理成功！
</div>

<script>
    document.addEventListener('DOMContentLoaded', function() {
        const clearCacheBtn = document.getElementById('clear-cache-btn');
        const cacheOverlay = document.getElementById('cache-overlay');
        const cacheModal = document.getElementById('cache-modal');
        const cancelClearCache = document.getElementById('cancel-clear-cache');
        const confirmClearCache = document.getElementById('confirm-clear-cache');
        const successToast = document.getElementById('success-toast');
        
        if (clearCacheBtn && cacheOverlay && cacheModal) {
            clearCacheBtn.addEventListener('click', function() {
                cacheOverlay.style.display = 'block';
                cacheModal.style.display = 'block';
            });
            
            cancelClearCache.addEventListener('click', function() {
                cacheOverlay.style.display = 'none';
                cacheModal.style.display = 'none';
            });
            
            cacheOverlay.addEventListener('click', function() {
                cacheOverlay.style.display = 'none';
                cacheModal.style.display = 'none';
            });
            
            confirmClearCache.addEventListener('click', function() {
                // 发送清理缓存请求
                const xhr = new XMLHttpRequest();
                xhr.open('POST', 'cache_manager.php', true);
                xhr.setRequestHeader('Content-Type', 'application/x-www-form-urlencoded');
                
                confirmClearCache.innerHTML = '清理中...';
                confirmClearCache.disabled = true;
                
                xhr.onreadystatechange = function() {
                    if (xhr.readyState === 4) {
                        cacheOverlay.style.display = 'none';
                        cacheModal.style.display = 'none';
                        
                        if (xhr.status === 200) {
                            // 显示成功提示
                            successToast.style.display = 'block';
                            successToast.style.transform = 'translate(-50%, -50%) scale(0.8)';
                            
                            setTimeout(function() {
                                successToast.style.transform = 'translate(-50%, -50%) scale(1)';
                            }, 100);
                            
                            setTimeout(function() {
                                successToast.style.transform = 'translate(-50%, -50%) scale(0.8)';
                                setTimeout(function() {
                                    successToast.style.display = 'none';
                                    successToast.style.transform = 'translate(-50%, -50%) scale(1)';
                                }, 300);
                            }, 2000);
                        }
                        
                        confirmClearCache.disabled = false;
                        confirmClearCache.innerHTML = '确认清理';
                    }
                };
                
                xhr.send('action=clear_cache&type=all');
            });
        }
    });
</script>


        <!-- 主内容区 (开始) -->
        <div class="main-content">

<script>
    document.addEventListener('DOMContentLoaded', function() {
        // 侧边栏折叠功能
        const toggleSidebar = document.getElementById('toggle-sidebar');
        const sidebar = document.getElementById('sidebar');
        const wrapper = document.getElementById('wrapper');
        
        if (toggleSidebar && sidebar && wrapper) {
            toggleSidebar.addEventListener('click', function() {
                sidebar.classList.toggle('collapsed');
                wrapper.classList.toggle('collapsed');
                localStorage.setItem('sidebar_collapsed', sidebar.classList.contains('collapsed'));
            });
            
            // 恢复侧边栏状态
            const isCollapsed = localStorage.getItem('sidebar_collapsed') === 'true';
            if (isCollapsed) {
                sidebar.classList.add('collapsed');
                wrapper.classList.add('collapsed');
            }
        }
        
        // 设置当前页面的菜单项为激活状态
        const currentPath = window.location.pathname;
        const menuItems = document.querySelectorAll('.menu-item a');
        
        menuItems.forEach(function(item) {
            const href = item.getAttribute('href');
            if (href) {
                // 检查完整路径匹配
                if (currentPath.endsWith(href)) {
                    const menuItem = item.closest('.menu-item');
                    if (menuItem) {
                        menuItem.classList.add('active');
                    }
                    
                    foundActive = true;
                }
            }
        });
        
        // 如果没有找到完全匹配的，尝试部分匹配
        if (!foundActive) {
            const pathParts = currentPath.split('/');
            const filename = pathParts[pathParts.length - 1];
            
            if (filename) {
                menuItems.forEach(function(item) {
                    const href = item.getAttribute('href');
                    if (href && href.includes(filename.split('.')[0])) {
                        const menuItem = item.closest('.menu-item');
                        if (menuItem) {
                            menuItem.classList.add('active');
                        }
                    }
                });
            }
        }
        
        // 保存当前激活的菜单项
        menuItems.forEach(function(item) {
            item.addEventListener('click', function() {
                const href = this.getAttribute('href');
                if (href) {
                    localStorage.setItem('active_menu_item', href);
                }
            });
        });
    });

    // 处理URL参数中的错误和成功消息
    function handleUrlMessages() {
        const urlParams = new URLSearchParams(window.location.search);
        const message = urlParams.get('message');
        const error = urlParams.get('error');

        if (message) {
            showSuccessMessage(message);
            // 清除URL中的message参数
            clearUrlParameter('message');
        }

        if (error) {
            showErrorMessage(error);
            // 清除URL中的error参数
            clearUrlParameter('error');
        }
    }

    // 显示成功消息
    function showSuccessMessage(message) {
        const alertHtml = `
            <div class="alert alert-success alert-dismissible fade show" role="alert" style="position: fixed; top: 70px; right: 20px; z-index: 9999; min-width: 300px; box-shadow: 0 4px 12px rgba(0,0,0,0.15);">
                <i class="fas fa-check-circle me-2"></i>
                <span>${message}</span>
                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
            </div>
        `;
        document.body.insertAdjacentHTML('beforeend', alertHtml);

        // 5秒后自动消失
        setTimeout(() => {
            const alert = document.querySelector('.alert-success');
            if (alert) {
                alert.remove();
            }
        }, 5000);
    }

    // 显示错误消息
    function showErrorMessage(message) {
        const alertHtml = `
            <div class="alert alert-danger alert-dismissible fade show" role="alert" style="position: fixed; top: 70px; right: 20px; z-index: 9999; min-width: 300px; box-shadow: 0 4px 12px rgba(0,0,0,0.15);">
                <i class="fas fa-exclamation-circle me-2"></i>
                <span>${message}</span>
                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
            </div>
        `;
        document.body.insertAdjacentHTML('beforeend', alertHtml);

        // 8秒后自动消失（错误消息显示时间稍长）
        setTimeout(() => {
            const alert = document.querySelector('.alert-danger');
            if (alert) {
                alert.remove();
            }
        }, 8000);
    }

    // 清除URL参数
    function clearUrlParameter(param) {
        const url = new URL(window.location);
        url.searchParams.delete(param);
        window.history.replaceState({}, document.title, url.toString());
    }

    // 页面加载完成后处理URL消息
    document.addEventListener('DOMContentLoaded', function() {
        handleUrlMessages();
    });
</script>


<div class="page-title">
    <h1>管理员管理</h1>
    <small style="margin-left: 15px; color: var(--text-secondary); font-weight: normal; font-size: 13px;">
        <i class="fas fa-users" style="color: var(--primary-color);"></i> <?php echo (isset($pagination['total_items'])) ? $pagination['total_items'] : ""; ?> 个管理员
    </small>
</div>

<style>
.table-responsive {
    overflow-x: auto;
}
.info-actions {
    display: flex;
    flex-wrap: nowrap;
    justify-content: flex-end;
    gap: 5px;
    min-width: 120px;
}
.info-actions .btn {
    padding: 0.25rem 0.5rem;
    font-size: 0.875rem;
    white-space: nowrap;
    text-decoration: none !important;
}
.info-actions .btn-group {
    display: inline-block;
}
a, a:hover, a:focus, a:active {
    text-decoration: none !important;
}
.nav-link {
    text-decoration: none !important;
}
.nav-link.active {
    background-color: #3490dc;
    color: #fff;
}

/* 淡色按钮样式 */
.btn-light-primary {
    background-color: #e6f0ff;
    color: #1b68ff;
    border: 1px solid #cce0ff;
}
.btn-light-primary:hover {
    background-color: #d1e3ff;
    color: #0056b3;
}
.btn-light-warning {
    background-color: #fff8e6;
    color: #ffa500;
    border: 1px solid #ffe6b3;
}
.btn-light-warning:hover {
    background-color: #fff0d1;
    color: #cc8400;
}
.btn-light-danger {
    background-color: #ffe6e6;
    color: #ff3333;
    border: 1px solid #ffb3b3;
}
.btn-light-danger:hover {
    background-color: #ffd1d1;
    color: #cc0000;
}
.btn-light-info {
    background-color: #e6f7ff;
    color: #00aaff;
    border: 1px solid #b3e0ff;
}
.btn-light-info:hover {
    background-color: #d1f0ff;
    color: #0088cc;
}
.btn-light-success {
    background-color: #e6ffe6;
    color: #00aa00;
    border: 1px solid #b3ffb3;
}
.btn-light-success:hover {
    background-color: #d1ffd1;
    color: #008800;
}
.btn-light-secondary {
    background-color: #f0f0f0;
    color: #666666;
    border: 1px solid #dddddd;
}
.btn-light-secondary:hover {
    background-color: #e0e0e0;
    color: #444444;
}

/* 分页样式 */
.simple-pagination {
    display: flex;
    justify-content: center;
    align-items: center;
    flex-wrap: wrap;
    gap: 5px;
}
.pagination-btn {
    display: inline-block;
    padding: 5px 12px;
    background: #fff;
    border: 1px solid #ddd;
    color: #333;
    text-decoration: none;
    border-radius: 3px;
    transition: all 0.2s;
}
.pagination-btn:hover {
    background: #f8f9fa;
    border-color: #ccc;
}
.pagination-btn.active {
    background: #1b68ff;
    color: white;
    border-color: #1b68ff;
}
.pagination-btn.disabled {
    color: #aaa;
    background: #f8f8f8;
    cursor: not-allowed;
}

/* 固定表格列宽 */
.table {
    width: 100%;
    table-layout: fixed;
    white-space: nowrap;
}
.table th {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}
.table td {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}
/* 设置每列的固定宽度 */
.table .col-id { width: 60px; }
.table .col-username { width: 120px; }
.table .col-realname { width: 120px; }
.table .col-role { width: 120px; }
.table .col-login-time { width: 150px; }
.table .col-status { width: 80px; }
.table .col-actions { width: 200px; text-align: right; }
</style>

<!-- 消息提示 -->
<?php if($message): ?>
<div class="alert alert-success">
    <i class="fas fa-check-circle"></i>
    <div>
        <p><?php echo $message ?? ""; ?></p>
    </div>
</div>
<?php endif; ?>
<?php if($error): ?>
<div class="alert alert-danger">
    <i class="fas fa-exclamation-circle"></i>
    <div>
        <p><?php echo $error ?? ""; ?></p>
    </div>
</div>
<?php endif; ?>

<!-- 操作栏 -->
<div class="section">
    <div class="card">
        <div class="card-title" style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 16px;">
            <span>管理系统账号，控制登录权限</span>
            <a href="admin.php?action=add" class="btn btn-primary">
                <i class="fas fa-plus"></i>
                添加管理员
            </a>
        </div>

        <!-- 筛选表单 -->
        <div class="filter-form-expanded">
            <form method="get" action="admin.php">
                <div class="filter-row-expanded">
                    <div class="filter-item-expanded">
                        <label>关键词:</label>
                        <input type="text" name="keyword" value="<?php echo $keyword ?? ""; ?>" placeholder="搜索用户名或真实姓名..." class="form-control">
                    </div>

                    <div class="filter-buttons-expanded">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-search"></i>
                            筛选
                        </button>
                        <a href="admin.php" class="btn btn-outline">
                            <i class="fas fa-undo"></i>
                            重置
                        </a>
                    </div>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- 管理员列表 -->
<div class="section">
    <div class="card">
        <div class="table-responsive">
            <table class="table table-vcenter table-bordered table-hover">
                <thead>
                    <tr>
                        <th class="col-id">ID</th>
                        <th class="col-username">用户名</th>
                        <th class="col-realname">真实姓名</th>
                        <th class="col-role">角色</th>
                        <th class="col-login-time">最后登录时间</th>
                        <th class="col-status">状态</th>
                        <th class="col-actions">操作</th>
                    </tr>
                </thead>
                <tbody>
                    <?php if(!$admins): ?>
                    <tr>
                        <td colspan="7" class="text-center">暂无管理员数据</td>
                    </tr>
                    <?php else: ?>
                    <?php if(null !== ($admins ?? null) && is_array($admins)): foreach($admins as $item): ?>
                    <tr>
                        <td><?php echo (isset($item['id'])) ? $item['id'] : ""; ?></td>
                        <td><?php echo (isset($item['username'])) ? $item['username'] : ""; ?></td>
                        <td><?php echo null !== ((null !== ($item ?? null)) ? ($item['realname']) : null) && ((null !== ($item ?? null)) ? ($item['realname']) : null) !== "" ? (null !== ($item ?? null)) ? ($item['realname']) : null : '--'; ?></td>
                        <td>
                            <?php if($item['role'] == 'admin'): ?>
                                <span class="badge badge-primary">管理员</span>
                            <?php elseif($item['role'] == 'super_admin'): ?>
                                <span class="badge badge-danger">超级管理员</span>
                            <?php else: ?>
                                <span class="badge badge-secondary"><?php echo (isset($item['role'])) ? $item['role'] : ""; ?></span>
                            <?php endif; ?>
                        </td>
                        <td><?php echo (isset($item['last_login_formatted'])) ? $item['last_login_formatted'] : ""; ?></td>
                        <td>
                            <?php if($item['status'] == 1): ?>
                                <span class="badge badge-success">正常</span>
                            <?php else: ?>
                                <span class="badge badge-danger">禁用</span>
                            <?php endif; ?>
                        </td>
                        <td>
                            <div class="info-actions">
                                <a href="admin.php?action=edit&id=<?php echo (isset($item['id'])) ? $item['id'] : ""; ?>" class="btn btn-sm btn-light-primary">编辑</a>
                                <?php if($admin['id'] != $item['id']): ?>
                                    <a href="admin.php?action=toggle_status&id=<?php echo (isset($item['id'])) ? $item['id'] : ""; ?>" class="btn btn-sm <?php if($item['status'] == 1): ?>btn-light-warning<?php else: ?>btn-light-success<?php endif; ?>" onclick="return confirm('<?php if($item['status'] == 1): ?>禁用<?php else: ?>启用<?php endif; ?>管理员？')"><?php if($item['status'] == 1): ?>禁用<?php else: ?>启用<?php endif; ?></a>
                                    <a href="admin.php?action=delete&id=<?php echo (isset($item['id'])) ? $item['id'] : ""; ?>" class="btn btn-sm btn-light-danger" onclick="return confirm('确定要删除这个管理员账号吗？此操作不可撤销！')">删除</a>
                                <?php else: ?>
                                    <span class="btn btn-sm btn-light-secondary" disabled>当前账号</span>
                                <?php endif; ?>
                            </div>
                        </td>
                    </tr>
                    <?php endforeach; endif; ?>
                    <?php endif; ?>
                </tbody>
            </table>
        </div>
        
        <!-- 分页 -->
        <?php if($pagination['total_pages'] > 1): ?>
        <div style="margin-top: 20px; display: flex; justify-content: space-between; align-items: center; flex-wrap: wrap;">
            <div class="simple-pagination" style="justify-content: flex-end;">
                <?php if($pagination['current_page'] > 1): ?>
                <a href="<?php echo (isset($pagination['previous_link'])) ? $pagination['previous_link'] : ""; ?>" class="pagination-btn">上一页</a>
                <?php else: ?>
                <span class="pagination-btn disabled">上一页</span>
                <?php endif; ?>
                
                <?php if(null !== ($pagination ?? null) && is_array($pagination['page_links'])): foreach($pagination['page_links'] as $page => $link): ?>
                <a href="<?php echo $link ?? ""; ?>" class="pagination-btn <?php if($page == $pagination['current_page']): ?>active<?php endif; ?>"><?php echo $page ?? ""; ?></a>
                <?php endforeach; endif; ?>
                
                <?php if($pagination['current_page'] < $pagination['total_pages']): ?>
                <a href="<?php echo (isset($pagination['next_link'])) ? $pagination['next_link'] : ""; ?>" class="pagination-btn">下一页</a>
                <?php else: ?>
                <span class="pagination-btn disabled">下一页</span>
                <?php endif; ?>
                
                <span style="margin-left: 10px; color: #6c757d; font-size: 14px;">共 <?php echo (isset($pagination['total_pages'])) ? $pagination['total_pages'] : ""; ?> 页</span>
            </div>
        </div>
        <?php endif; ?>
        </div>
    </div>
</div>

        </div>
        <!-- 主内容区 (结束) -->
    </div>
    <!-- wrapper (结束) -->

    <!-- jQuery (必须在Bootstrap之前加载) -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <!-- Bootstrap JavaScript -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>