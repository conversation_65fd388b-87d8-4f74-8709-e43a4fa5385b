<?php
/**
 * 新闻栏目管理
 */

// 定义入口常量
define('IN_BTMPS', true);

require_once dirname(__FILE__) . '/../include/common.inc.php';

// 获取数据库表前缀
$db_prefix = isset($config['db_prefix']) ? $config['db_prefix'] : '';

// 引入后台通用函数
require_once dirname(__FILE__) . '/include/admin.fun.php';

// 检查管理员是否登录
if (!isset($_SESSION['admin']) || $_SESSION['admin']['is_login'] !== true) {
    header("Location: login.php");
    exit;
}

// 操作类型
$op = isset($_REQUEST['op']) ? trim($_REQUEST['op']) : 'list';

// 当前页面
$current_page = 'news_category';

// 消息提示
$message = '';
$error = '';

// 获取URL中的消息参数
if (isset($_GET['message'])) {
    $message = $_GET['message'];
}
if (isset($_GET['error'])) {
    $error = $_GET['error'];
}

// 设置页面标题和面包屑导航
$page_title = '新闻栏目管理';
$breadcrumb = '新闻栏目管理';

// 使用系统自带的Template类而不是Smarty
// $tpl变量已在common.inc.php中初始化
// 设置当前模板目录为hao/template
$tpl->setTemplateDir(dirname(__FILE__) . '/template/');

// 设置公共变量
$tpl->assign('current_page', $current_page);
$tpl->assign('page_title', $page_title);
$tpl->assign('breadcrumb', $breadcrumb);
$tpl->assign('message', $message);
$tpl->assign('error', $error);

// 根据不同操作类型处理
switch ($op) {
    case 'list': // 栏目列表
        // 分页参数
        $page = isset($_REQUEST['page']) ? intval($_REQUEST['page']) : 1;
        $perpage = 15; // 每页显示数量
        
        // 获取当前显示类型和父分类ID
        $parent_id = isset($_REQUEST['parent_id']) ? intval($_REQUEST['parent_id']) : -1; // 默认只显示一级分类(-1)
        $view_mode = isset($_REQUEST['view_mode']) ? trim($_REQUEST['view_mode']) : 'parent_only';
        $keyword = isset($_REQUEST['keyword']) ? trim($_REQUEST['keyword']) : '';
        $status = isset($_REQUEST['status']) ? intval($_REQUEST['status']) : -1; // -1:全部, 0:禁用, 1:启用
        
        // 构建查询条件
        $where = "1=1";
        if ($parent_id > 0) {
            // 显示特定父分类下的子分类
            $where .= " AND parentid=$parent_id";
        } elseif ($parent_id == -1) {
            // 只显示一级分类
            $where .= " AND parentid=0";
        } elseif ($parent_id == -2) {
            // 只显示二级分类
            $where .= " AND parentid>0";
        }
        
        if (!empty($keyword)) {
            $where .= " AND catname LIKE '%" . $db->escape($keyword) . "%'";
        }

        // 状态筛选
        if ($status != -1) {
            $where .= " AND is_show = $status";
        }
        
        // 获取栏目总数
        $sql = "SELECT COUNT(*) as count FROM `news_category` WHERE $where";
        $result = $db->query($sql);
        $row = $db->fetch_array($result);
        $total = $row['count'];
        
        // 计算开始位置
        $start = ($page - 1) * $perpage;
        
        // 查询栏目列表
        $sql = "SELECT c.*, 
                (SELECT COUNT(*) FROM `{$db_prefix}news_category` WHERE parentid = c.catid) as has_children 
                FROM `{$db_prefix}news_category` c 
                WHERE 1=1";
        
        if ($parent_id == -1) {
            $sql .= " AND c.parentid = 0";
        } elseif ($parent_id == -2) {
            $sql .= " AND c.parentid > 0";
        } elseif ($parent_id > 0) {
            $sql .= " AND c.parentid = $parent_id";
        }
        
        if (!empty($keyword)) {
            $sql .= " AND c.catname LIKE '%" . $db->escape($keyword) . "%'";
        }

        // 状态筛选
        if ($status != -1) {
            $sql .= " AND c.is_show = $status";
        }

        $sql .= " ORDER BY c.sort_order ASC, c.catid ASC";
        $result = $db->query($sql);
        $categories = [];
        while ($row = $db->fetch_array($result)) {
            $row['level'] = $row['parentid'] == 0 ? 1 : 2;
            $categories[] = $row;
        }
        
        // 获取所有一级栏目，用于筛选下拉框
        $top_sql = "SELECT catid, catname FROM `news_category` WHERE parentid=0 ORDER BY sort_order ASC";
        $top_result = $db->query($top_sql);
        $parent_categories = [];
        while ($row = $db->fetch_array($top_result)) {
            $parent_categories[] = $row;
        }
        
        // 生成分页数据
        $base_url = 'news_category.php?';
        $params = [];
        if ($parent_id !== -1) {
            $params['parent_id'] = $parent_id;
        }
        if (!empty($keyword)) {
            $params['keyword'] = $keyword;
        }
        if (!empty($view_mode)) {
            $params['view_mode'] = $view_mode;
        }
        if ($status != -1) {
            $params['status'] = $status;
        }
        
        $pagination = generate_pagination($total, $page, $perpage, $base_url, $params);
        
        // 生成分页HTML代码
        $pagination_html = pagination_html($pagination, true); // 使用简单分页样式
        
        // 设置当前父栏目名称
        $current_parent_name = '';
        if ($parent_id > 0) {
            $parent_sql = "SELECT catname FROM `news_category` WHERE catid=$parent_id LIMIT 1";
            $parent_result = $db->query($parent_sql);
            $parent_row = $db->fetch_array($parent_result);
            if ($parent_row) {
                $current_parent_name = $parent_row['catname'];
            }
        }
        
        // 设置模板变量
        $tpl->assign('categories', $categories);
        $tpl->assign('parentCategories', $parent_categories);
        $tpl->assign('parent_id', $parent_id);
        $tpl->assign('current_parent_name', $current_parent_name);
        $tpl->assign('keyword', $keyword);
        $tpl->assign('view_mode', $view_mode);
        $tpl->assign('status', $status);
        $tpl->assign('pagination', $pagination);
        $tpl->assign('pagination_html', $pagination_html);
        $tpl->assign('page', $pagination['page']);
        $tpl->assign('total_page', $pagination['total_page']);
        $tpl->assign('total', $pagination['total']);
        $tpl->assign('perpage', $perpage);
        $tpl->assign('current_menu', 'news_category');
        $tpl->assign('current_page', $current_page);
        $tpl->assign('admin', $_SESSION['admin']);
        $tpl->assign('breadcrumb', '新闻栏目管理');

        $tpl->display('news_category.htm');
        break;
        
    case 'add': // 添加栏目
        // 获取父级栏目ID
        $parent_id = isset($_REQUEST['parent_id']) ? intval($_REQUEST['parent_id']) : 0;
        
        if ($_SERVER['REQUEST_METHOD'] == 'POST') {
            $catname = isset($_POST['catname']) ? trim($_POST['catname']) : '';
            $pinyin = isset($_POST['pinyin']) ? trim($_POST['pinyin']) : '';
            $parentid = isset($_POST['parentid']) ? intval($_POST['parentid']) : 0;
            $sort_order = isset($_POST['sort_order']) ? intval($_POST['sort_order']) : 50;
            $is_show = isset($_POST['is_show']) ? intval($_POST['is_show']) : 1;

            if (empty($catname)) {
                show_message('栏目名称不能为空！', 'news_category.php?op=add' . ($parent_id > 0 ? '&parent_id=' . $parent_id : ''));
            }

            // 验证拼音格式
            if (!empty($pinyin)) {
                if (!preg_match('/^[a-z0-9-]+$/', $pinyin)) {
                    show_message('栏目拼音只能包含小写字母、数字和连字符！', 'news_category.php?op=add' . ($parent_id > 0 ? '&parent_id=' . $parent_id : ''));
                }

                // 检查拼音是否已存在
                $check_sql = "SELECT COUNT(*) as count FROM `news_category` WHERE `pinyin` = '" . $db->escape($pinyin) . "'";
                $check_result = $db->query($check_sql);
                $check_row = $db->fetch_array($check_result);
                if ($check_row['count'] > 0) {
                    show_message('该拼音已被使用，请使用其他拼音！', 'news_category.php?op=add' . ($parent_id > 0 ? '&parent_id=' . $parent_id : ''));
                }
            }

            // 添加栏目
            $sql = "INSERT INTO `news_category`
                    (`catname`, `pinyin`, `parentid`, `sort_order`, `is_show`, `addtime`, `update_time`)
                    VALUES
                    ('" . $db->escape($catname) . "', '" . $db->escape($pinyin) . "', $parentid, $sort_order, $is_show, " . time() . ", " . time() . ")";
            $result = $db->query($sql);
            
            if ($result) {
                // 如果是添加子栏目，则返回到对应的父栏目列表视图
                if ($parentid > 0) {
                    header("Location: news_category.php?message=" . urlencode('添加栏目成功！') . "&parent_id=" . $parentid);
                    exit;
                } else {
                    // 如果是添加一级栏目，返回到一级栏目列表
                    header("Location: news_category.php?message=" . urlencode('添加栏目成功！'));
                    exit;
                }
            } else {
                show_message('添加栏目失败！', 'news_category.php?op=add' . ($parent_id > 0 ? '&parent_id=' . $parent_id : ''));
            }
        } else {
            // 获取所有父级栏目
            $sql = "SELECT `catid`, `catname`, `sort_order` FROM `news_category` WHERE `parentid` = 0 ORDER BY `sort_order` ASC, `catid` ASC";
            $result = $db->query($sql);
            $parent_cats = [];
            while ($row = $db->fetch_array($result)) {
                $parent_cats[] = $row;
            }
            
            $tpl->assign('parent_cats', $parent_cats);
            $tpl->assign('selected_parent_id', $parent_id); // 设置默认选中的父栏目
            $tpl->assign('current_menu', 'news_category');
            $tpl->assign('current_page', $current_page);
            $tpl->assign('admin', $_SESSION['admin']);
            $tpl->assign('breadcrumb', '添加新闻栏目');

            $tpl->display('news_category_add.htm');
        }
        break;
        
    case 'edit': // 编辑栏目
        $catid = isset($_REQUEST['catid']) ? intval($_REQUEST['catid']) : 0;
        // 获取返回的父栏目ID参数
        $return_parent_id = isset($_REQUEST['return_parent_id']) ? intval($_REQUEST['return_parent_id']) : -1;
        
        if ($catid <= 0) {
            show_message('栏目ID无效！', 'news_category.php');
        }
        
        // 获取栏目信息，使用索引字段catid查询，只获取必要的字段
        $sql = "SELECT `catid`, `catname`, `pinyin`, `parentid`, `sort_order`, `is_show` FROM `{$db_prefix}news_category` WHERE `catid` = $catid LIMIT 1";
        $result = $db->query($sql);
        $category = $db->fetch_array($result);
        
        if (!$category) {
            show_message('栏目不存在！', 'news_category.php');
        }
        
        // 标记当前编辑的栏目是否为一级栏目
        $is_top_level = ($category['parentid'] == 0);
        
        if ($_SERVER['REQUEST_METHOD'] == 'POST') {
            $catname = isset($_POST['catname']) ? trim($_POST['catname']) : '';
            $pinyin = isset($_POST['pinyin']) ? trim($_POST['pinyin']) : '';
            $parentid = isset($_POST['parentid']) ? intval($_POST['parentid']) : 0;
            $sort_order = isset($_POST['sort_order']) ? intval($_POST['sort_order']) : 50;
            $is_show = isset($_POST['is_show']) ? intval($_POST['is_show']) : 1;

            if (empty($catname)) {
                show_message('栏目名称不能为空！', "news_category.php?op=edit&catid=$catid" . ($return_parent_id != -1 ? "&return_parent_id=$return_parent_id" : ""));
            }

            // 验证拼音格式
            if (!empty($pinyin)) {
                if (!preg_match('/^[a-z0-9-]+$/', $pinyin)) {
                    show_message('栏目拼音只能包含小写字母、数字和连字符！', "news_category.php?op=edit&catid=$catid" . ($return_parent_id != -1 ? "&return_parent_id=$return_parent_id" : ""));
                }

                // 检查拼音是否已存在（排除当前栏目）
                $check_sql = "SELECT COUNT(*) as count FROM `news_category` WHERE `pinyin` = '" . $db->escape($pinyin) . "' AND `catid` != $catid";
                $check_result = $db->query($check_sql);
                $check_row = $db->fetch_array($check_result);
                if ($check_row['count'] > 0) {
                    show_message('该拼音已被使用，请使用其他拼音！', "news_category.php?op=edit&catid=$catid" . ($return_parent_id != -1 ? "&return_parent_id=$return_parent_id" : ""));
                }
            }
            
            // 检查是否将自己设为自己的父级
            if ($parentid == $catid) {
                show_message('不能将当前栏目设为自己的父级！', "news_category.php?op=edit&catid=$catid" . ($return_parent_id != -1 ? "&return_parent_id=$return_parent_id" : ""));
            }
            
            // 检查是否将当前栏目设为其子栏目的父级（避免循环嵌套）
            if ($parentid > 0) {
                $check_sql = "SELECT COUNT(*) as count FROM `{$db_prefix}news_category` WHERE `catid` = $parentid AND `parentid` = $catid";
                $check_result = $db->query($check_sql);
                $check_row = $db->fetch_array($check_result);
                if ($check_row['count'] > 0) {
                    show_message('不能将当前栏目设为其子栏目的父级！', "news_category.php?op=edit&catid=$catid" . ($return_parent_id != -1 ? "&return_parent_id=$return_parent_id" : ""));
                }
            }
            
            // 开始事务
            $db->beginTransaction();
            
            try {
                // 更新栏目
                $sql = "UPDATE `{$db_prefix}news_category` SET
                        `catname` = '" . $db->escape($catname) . "',
                        `pinyin` = '" . $db->escape($pinyin) . "',
                        `parentid` = $parentid,
                        `sort_order` = $sort_order,
                        `is_show` = $is_show,
                        `update_time` = " . time() . "
                        WHERE `catid` = $catid";
                $result = $db->query($sql);
                
                if (!$result) {
                    throw new Exception("更新栏目失败");
                }
                
                // 提交事务
                $db->commit();
                
                // 根据栏目级别和设置，决定返回到哪个视图
                if ($return_parent_id > 0) {
                    // 返回到特定父栏目的子栏目列表
                    header("Location: news_category.php?message=" . urlencode('编辑栏目成功！') . "&parent_id=$return_parent_id");
                    exit;
                } else if ($is_top_level) {
                    // 当前编辑的是一级栏目，返回一级栏目列表
                    header("Location: news_category.php?message=" . urlencode('编辑栏目成功！'));
                    exit;
                } else {
                    // 当前编辑的是二级栏目，返回到其父栏目的子栏目列表
                    header("Location: news_category.php?message=" . urlencode('编辑栏目成功！') . "&parent_id={$category['parentid']}");
                    exit;
                }
            } catch (Exception $e) {
                // 回滚事务
                $db->rollback();
                show_message('编辑栏目失败：' . $e->getMessage(), "news_category.php?op=edit&catid=$catid" . ($return_parent_id != -1 ? "&return_parent_id=$return_parent_id" : ""));
            }
        } else {
            // 查询可用作父栏目的栏目列表，排除自身及其子栏目
            $exclude_ids = get_child_categories($catid);
            $exclude_ids[] = $catid;
            $exclude_where = count($exclude_ids) > 0 ? " AND catid NOT IN (" . implode(',', $exclude_ids) . ")" : "";
            
            // 只获取顶级栏目ID和名称
            $sql = "SELECT `catid`, `catname` FROM `{$db_prefix}news_category` WHERE `parentid` = 0 $exclude_where ORDER BY `sort_order` ASC, `catid` ASC";
            $result = $db->query($sql);
            $topCategories = [];
            while ($row = $db->fetch_array($result)) {
                $topCategories[] = $row;
            }
            
            $tpl->assign('category', $category);
            $tpl->assign('topCategories', $topCategories);
            $tpl->assign('is_top_level', $is_top_level);
            $tpl->assign('return_parent_id', $return_parent_id);
            $tpl->assign('current_menu', 'news_category');
            $tpl->assign('current_page', $current_page);
            $tpl->assign('admin', $_SESSION['admin']);
            $tpl->assign('breadcrumb', '编辑新闻栏目');

            $tpl->display('news_category_edit.htm');
        }
        break;
        
    case 'delete': // 删除栏目
        $catid = isset($_REQUEST['catid']) ? intval($_REQUEST['catid']) : 0;
        
        // 获取当前parent_id，用于操作后返回相同视图
        $return_parent_id = isset($_REQUEST['return_parent_id']) ? intval($_REQUEST['return_parent_id']) : -1;
        
        if ($catid <= 0) {
            show_message('栏目ID无效！', 'news_category.php' . ($return_parent_id > 0 ? "?parent_id=$return_parent_id" : ""));
        }
        
        // 检查是否有子栏目
        $sql = "SELECT COUNT(*) as count FROM `news_category` WHERE `parentid` = $catid";
        $result = $db->query($sql);
        $row = $db->fetch_array($result);
        
        if ($row['count'] > 0) {
            show_message('该栏目下有子栏目，请先删除子栏目！', 'news_category.php' . ($return_parent_id > 0 ? "?parent_id=$return_parent_id" : ""));
        }
        
        // 检查栏目下是否有文章
        $sql = "SELECT COUNT(*) as count FROM `news` WHERE `catid` = $catid";
        $result = $db->query($sql);
        $row = $db->fetch_array($result);
        
        if ($row['count'] > 0) {
            show_message('该栏目下有文章，请先删除或移动文章！', 'news_category.php' . ($return_parent_id > 0 ? "?parent_id=$return_parent_id" : ""));
        }
        
        // 删除栏目
        $sql = "DELETE FROM `news_category` WHERE `catid` = $catid";
        $result = $db->query($sql);
        
        if ($result) {
            header("Location: news_category.php?message=" . urlencode('删除栏目成功！') . ($return_parent_id > 0 ? "&parent_id=$return_parent_id" : ""));
            exit;
        } else {
            show_message('删除栏目失败！', 'news_category.php' . ($return_parent_id > 0 ? "?parent_id=$return_parent_id" : ""));
        }
        break;
        
    case 'view_front': // 查看前台
        $catid = isset($_REQUEST['catid']) ? intval($_REQUEST['catid']) : 0;
        
        if ($catid <= 0) {
            show_message('栏目ID无效！', 'news_category.php');
        }
        
        // 获取栏目信息
        $sql = "SELECT * FROM `news_category` WHERE `catid` = $catid LIMIT 1";
        $result = $db->query($sql);
        $category = $db->fetch_array($result);
        
        if (!$category) {
            show_message('栏目不存在！', 'news_category.php');
        }
        
        // 重定向到前台栏目页面
        header("Location: " . $site_url . "/news.php?catid=" . $catid);
        exit;
        break;
        
    case 'toggle_status': // 切换栏目状态
        $catid = isset($_REQUEST['catid']) ? intval($_REQUEST['catid']) : 0;
        $return_parent_id = isset($_REQUEST['return_parent_id']) ? intval($_REQUEST['return_parent_id']) : -1;
        
        if ($catid <= 0) {
            show_message('栏目ID无效！', 'news_category.php' . ($return_parent_id > 0 ? "?parent_id=$return_parent_id" : ""));
        }
        
        // 获取当前状态
        $sql = "SELECT `is_show` FROM `news_category` WHERE `catid` = $catid LIMIT 1";
        $result = $db->query($sql);
        $row = $db->fetch_array($result);
        
        if (!$row) {
            show_message('栏目不存在！', 'news_category.php' . ($return_parent_id > 0 ? "?parent_id=$return_parent_id" : ""));
        }
        
        // 切换状态
        $new_status = $row['is_show'] == 1 ? 0 : 1;
        $sql = "UPDATE `news_category` SET `is_show` = $new_status, `update_time` = " . time() . " WHERE `catid` = $catid";
        $result = $db->query($sql);
        
        if ($result) {
            show_message('栏目状态已更新！', 'news_category.php' . ($return_parent_id > 0 ? "?parent_id=$return_parent_id" : ""), 3);
        } else {
            show_message('更新栏目状态失败！', 'news_category.php' . ($return_parent_id > 0 ? "?parent_id=$return_parent_id" : ""));
        }
        break;
        
    case 'batch_delete': // 批量删除
        // 检查是否有选中的栏目ID
        if (!isset($_POST['category_ids']) || !is_array($_POST['category_ids']) || empty($_POST['category_ids'])) {
            show_message('未选择要删除的栏目！', 'news_category.php');
        }
        
        // 获取当前parent_id，用于操作后返回相同视图
        $return_parent_id = isset($_POST['return_parent_id']) ? intval($_POST['return_parent_id']) : -1;
        
        $category_ids = array_map('intval', $_POST['category_ids']);
        $success_count = 0;
        $error_count = 0;
        $has_children_count = 0;
        $has_news_count = 0;
        
        foreach ($category_ids as $id) {
            // 检查是否有子栏目
            $sql = "SELECT COUNT(*) as count FROM `news_category` WHERE `parentid` = $id";
            $result = $db->query($sql);
            $row = $db->fetch_array($result);
            
            if ($row['count'] > 0) {
                $has_children_count++;
                continue;
            }
            
            // 检查是否有文章
            $sql = "SELECT COUNT(*) as count FROM `news` WHERE `catid` = $id";
            $result = $db->query($sql);
            $row = $db->fetch_array($result);
            
            if ($row['count'] > 0) {
                $has_news_count++;
                continue;
            }
            
            // 删除栏目
            $sql = "DELETE FROM `news_category` WHERE `catid` = $id";
            $result = $db->query($sql);
            
            if ($result) {
                $success_count++;
            } else {
                $error_count++;
            }
        }
        
        // 生成提示信息
        if ($success_count > 0) {
            $message = "成功删除 {$success_count} 个栏目";
            if ($has_children_count > 0) {
                $message .= "，{$has_children_count} 个栏目有子栏目未删除";
            }
            if ($has_news_count > 0) {
                $message .= "，{$has_news_count} 个栏目有文章未删除";
            }
            if ($error_count > 0) {
                $message .= "，{$error_count} 个栏目删除失败";
            }
        } else {
            if ($has_children_count > 0) {
                $message = "{$has_children_count} 个栏目有子栏目，请先删除子栏目";
            } else if ($has_news_count > 0) {
                $message = "{$has_news_count} 个栏目有文章，请先删除或移动文章";
            } else {
                $message = "删除失败";
            }
        }
        
        // 重定向到之前的视图
        $redirect_url = 'news_category.php';
        if ($return_parent_id > 0) {
            $redirect_url .= "?parent_id=$return_parent_id&message=" . urlencode($message);
        } else {
            $redirect_url .= "?message=" . urlencode($message);
        }
        
        header("Location: $redirect_url");
        exit;
        break;
        
    default:
        show_message('无效的操作！', 'news_category.php');
        break;
}

/**
 * 获取栏目的所有子栏目ID（递归）
 */
function get_child_categories($parent_id) {
    global $db;
    
    $children = [];
    
    // 获取直接子栏目
    $sql = "SELECT `catid` FROM `news_category` WHERE `parentid` = $parent_id";
    $result = $db->query($sql);
    
    while ($row = $db->fetch_array($result)) {
        $children[] = $row['catid'];
        
        // 递归获取下一级子栏目
        $sub_children = get_child_categories($row['catid']);
        $children = array_merge($children, $sub_children);
    }
    
    return $children;
}