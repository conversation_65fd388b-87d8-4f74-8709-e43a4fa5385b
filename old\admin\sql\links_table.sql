-- 友情链接表
CREATE TABLE IF NOT EXISTS `links` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '链接ID',
  `name` varchar(100) NOT NULL COMMENT '链接名称',
  `url` varchar(255) NOT NULL COMMENT '链接地址',
  `description` text COMMENT '链接描述',
  `logo` varchar(255) DEFAULT NULL COMMENT 'LOGO地址',
  `sort_order` int(11) DEFAULT 0 COMMENT '排序（数字越小越靠前）',
  `status` tinyint(1) DEFAULT 1 COMMENT '状态（1=启用，0=禁用）',
  `target` varchar(10) DEFAULT '_blank' COMMENT '打开方式（_blank=新窗口，_self=当前窗口）',
  `created_at` int(11) NOT NULL COMMENT '创建时间',
  `updated_at` int(11) NOT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_status_sort` (`status`, `sort_order`),
  KEY `idx_created_at` (`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='友情链接表';

-- 插入示例数据
INSERT INTO `links` (`name`, `url`, `description`, `logo`, `sort_order`, `status`, `target`, `created_at`, `updated_at`) VALUES
('百度', 'https://www.baidu.com', '全球最大的中文搜索引擎', 'https://www.baidu.com/favicon.ico', 1, 1, '_blank', UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
('腾讯', 'https://www.qq.com', '中国领先的互联网增值服务提供商', 'https://www.qq.com/favicon.ico', 2, 1, '_blank', UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
('阿里巴巴', 'https://www.alibaba.com', '全球领先的小企业电子商务公司', 'https://www.alibaba.com/favicon.ico', 3, 1, '_blank', UNIX_TIMESTAMP(), UNIX_TIMESTAMP());
