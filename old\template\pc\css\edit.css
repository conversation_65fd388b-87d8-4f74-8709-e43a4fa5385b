/* 页面布局 */
.yui-content {
    margin: 0 auto;
    padding: 20px 0;
}

.edit-content {
    background: #fff;
    padding: 30px;
    border-radius: 4px;
    box-shadow: 0 1px 3px rgba(0,0,0,0.1);
}

/* 面包屑导航 */
.breadcrumb-container {
    margin-bottom: 20px;
    padding: 15px;
    background: #fff;
    border-radius: 4px;
    box-shadow: 0 1px 3px rgba(0,0,0,0.1);
}

.breadcrumb {
    display: flex;
    align-items: center;
    font-size: 14px;
    color: #666;
}

.breadcrumb a {
    color: #666;
    text-decoration: none;
    transition: color 0.3s;
}

.breadcrumb a:hover {
    color: #1890ff;
}

.breadcrumb .separator {
    margin: 0 8px;
    color: #ccc;
}

.breadcrumb .current {
    color: #1890ff;
}

/* 表单面板 */
.form-panel {
    margin-bottom: 30px;
    padding: 20px;
    background: #fff;
    border: 1px solid #eee;
    border-radius: 4px;
}

.form-group {
    display: flex;
    margin-bottom: 20px;
    align-items: flex-start;
}

.form-group:last-child {
    margin-bottom: 0;
}

.form-label {
    width: 120px;
    padding-top: 8px;
    color: #333;
    font-size: 14px;
    text-align: right;
    margin-right: 20px;
    flex-shrink: 0;
}

.form-label.required:after {
    content: "*";
    color: #ff4d4f;
    margin-left: 4px;
}

.form-right {
    flex: 1;
    max-width: 800px;
}

/* 表单控件 */
.form-control {
    width: 100%;
    height: 36px;
    padding: 8px 12px;
    font-size: 14px;
    border: 1px solid #ddd;
    border-radius: 4px;
    transition: all 0.3s;
    box-sizing: border-box;
}

.form-control:focus {
    border-color: #1890ff;
    box-shadow: 0 0 0 2px rgba(24,144,255,0.2);
    outline: none;
}

.form-control.error {
    border-color: #ff4d4f;
}

textarea.form-control {
    height: 120px;
    resize: vertical;
}

.form-select {
    appearance: none;
    background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='12' height='12' viewBox='0 0 12 12'%3E%3Cpath fill='%23666' d='M6 8.825L1.175 4 2.238 2.938 6 6.7l3.763-3.762L10.825 4z'/%3E%3C/svg%3E");
    background-repeat: no-repeat;
    background-position: right 8px center;
    padding-right: 30px;
}

/* 图片上传 */
.image-upload {
    position: relative;
}

.hidden-input {
    display: none;
}

.upload-btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    width: 100px;
    height: 100px;
    border: 1px dashed #d9d9d9;
    border-radius: 4px;
    cursor: pointer;
    transition: all 0.3s;
}

.upload-btn:hover {
    border-color: #1890ff;
}

.upload-btn i {
    font-size: 24px;
    color: #999;
}

.form-hint {
    margin-top: 8px;
    font-size: 12px;
    color: #999;
}

.image-previews {
    display: flex;
    flex-wrap: wrap;
    gap: 15px;
    margin-top: 15px;
}

.image-item {
    position: relative;
    width: 100px;
    height: 100px;
    border-radius: 4px;
    overflow: hidden;
}

.image-item img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.remove-image {
    position: absolute;
    top: 0;
    right: 0;
    width: 20px;
    height: 20px;
    background: rgba(0,0,0,0.5);
    color: #fff;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    font-size: 14px;
    transition: background-color 0.3s;
}

.remove-image:hover {
    background: rgba(0,0,0,0.7);
}

/* 单选和复选框组 */
.radio-group,
.checkbox-group {
    display: flex;
    flex-wrap: wrap;
    gap: 20px;
}

.radio-item,
.checkbox-item {
    display: flex;
    align-items: center;
    cursor: pointer;
}

.radio-item input[type="radio"],
.checkbox-item input[type="checkbox"] {
    margin-right: 6px;
}

/* 错误提示 */
.error-message {
    color: #ff4d4f;
    font-size: 12px;
    margin-top: 4px;
    display: none;
}

/* 按钮样式 */
.form-actions {
    margin-top: 30px;
    padding-top: 20px;
    border-top: 1px solid #eee;
    text-align: center;
}

.btn {
    display: inline-block;
    min-width: 120px;
    height: 36px;
    line-height: 34px;
    padding: 0 20px;
    font-size: 14px;
    border-radius: 4px;
    cursor: pointer;
    transition: all 0.3s;
    text-decoration: none;
    margin: 0 10px;
}

.btn-primary {
    background: #1890ff;
    color: #fff;
    border: 1px solid #1890ff;
}

.btn-primary:hover {
    background: #40a9ff;
    border-color: #40a9ff;
}

.btn-default {
    background: #fff;
    color: #666;
    border: 1px solid #d9d9d9;
}

.btn-default:hover {
    color: #40a9ff;
    border-color: #40a9ff;
}

/* 加载中遮罩 */
.loading-overlay {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(255,255,255,0.8);
    z-index: 9999;
}

.loading-overlay.show {
    display: flex;
    align-items: center;
    justify-content: center;
}

.loading-spinner {
    text-align: center;
}

.loading-spinner i {
    font-size: 40px;
    color: #1890ff;
    animation: spin 1s infinite linear;
}

.loading-text {
    margin-top: 10px;
    color: #666;
    font-size: 14px;
}

@keyframes spin {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
}

/* 确认对话框 */
.confirm-dialog {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0,0,0,0.5);
    z-index: 9999;
    align-items: center;
    justify-content: center;
}

.confirm-content {
    width: 360px;
    background: #fff;
    border-radius: 4px;
    padding: 20px;
    text-align: center;
}

.confirm-message {
    font-size: 16px;
    color: #333;
    margin-bottom: 20px;
}

.confirm-buttons {
    display: flex;
    justify-content: center;
    gap: 15px;
}

/* 响应式调整 */
@media (max-width: 768px) {
    .form-group {
        flex-direction: column;
    }
    
    .form-label {
        width: 100%;
        text-align: left;
        margin-bottom: 8px;
        padding-top: 0;
    }
    
    .form-right {
        width: 100%;
    }
    
    .btn {
        width: 100%;
        margin: 10px 0;
    }
} 