<?php
// 定义安全常量
define('IN_BTMPS', true);
/**
 * 举报管理控制器
 */
require_once '../include/common.inc.php';

// 强制清理模板缓存 - 一次性代码，解决问题后可以移除
$compiled_dir = '../data/compiled/';
if (is_dir($compiled_dir)) {
    $files = glob($compiled_dir . '*.php');
    foreach ($files as $file) {
        if (is_file($file)) {
            @unlink($file);
        }
    }
}

// 检查管理员是否登录
if (!isset($_SESSION['admin']) || $_SESSION['admin']['is_login'] !== true) {
    header("Location: login.php");
    exit;
}

// 初始化模板引擎
$tpl = new Template();

// 设置当前页面
$current_page = 'report';

// 修改模板目录为admin/template目录
$tpl->setTemplateDir(dirname(__FILE__) . '/template/');

// 获取操作类型
$action = isset($_GET['action']) ? trim($_GET['action']) : 'list';

// 初始化提示信息变量
$message = isset($_GET['message']) ? $_GET['message'] : '';
$error = '';

// 使用全局数据库连接
global $db;

// 处理不同操作
switch ($action) {
    // 列表页面
    default:
    case 'list':
        // 获取筛选参数 - 默认显示未处理的举报
        $status = isset($_GET['status']) ? intval($_GET['status']) : 0; // 0表示未处理
        $type = isset($_GET['type']) ? trim($_GET['type']) : ''; // 空字符串表示全部
        $post_id = isset($_GET['post_id']) ? intval($_GET['post_id']) : 0;
        $keyword = isset($_GET['keyword']) ? trim($_GET['keyword']) : '';
        
        // 构建查询条件
        $where = array();
        
        if ($status >= 0) {
            $where[] = "r.status = $status";
        }
        
        if (!empty($type)) {
            $type = $db->escape($type);
            $where[] = "r.type = '$type'";
        }
        
        if ($post_id > 0) {
            $where[] = "r.post_id = $post_id";
        }
        
        if (!empty($keyword)) {
            $keyword = $db->escape($keyword);
            $where[] = "(r.content LIKE '%$keyword%' OR r.tel LIKE '%$keyword%')";
        }
        
        $where_sql = !empty($where) ? " WHERE " . implode(" AND ", $where) : "";
        
        // 分页处理
        $page = isset($_GET['page']) ? intval($_GET['page']) : 1;
        $page = max(1, $page);
        $page_size = 15;
        
        // 获取总记录数
        $count_sql = "SELECT COUNT(*) AS count FROM `reports` r $where_sql";
        $count_result = $db->query($count_sql);
        $count_data = $db->fetch_array($count_result);
        $total_items = $count_data['count'];
        
        // 计算总页数
        $total_pages = ceil($total_items / $page_size);
        $page = min($page, max(1, $total_pages));
        
        // 计算偏移量
        $offset = ($page - 1) * $page_size;
        
        // 新的联合查询方式
        $reports_sql = "SELECT r.*, p.title as post_title, c.pinyin as category_pinyin 
                       FROM `reports` r 
                       LEFT JOIN `posts` p ON r.post_id = p.id
                       LEFT JOIN `categories` c ON p.category_id = c.id
                       $where_sql 
                       ORDER BY r.id DESC 
                       LIMIT $offset, $page_size";
        
        $reports_result = $db->query($reports_sql);
        $reports = array();
        
        while ($row = $db->fetch_array($reports_result)) {
            // 格式化时间
            if (isset($row['created_at']) && !empty($row['created_at']) && is_numeric($row['created_at'])) {
                $row['created_at'] = date('Y-m-d H:i:s', $row['created_at']);
            } else {
                $row['created_at'] = '未知时间';
            }
            
            // 设置默认值，如果关联查询没有返回
            if (empty($row['post_title'])) {
                $row['post_title'] = '信息不存在或已删除';
            }
            
            if (empty($row['category_pinyin'])) {
                $row['category_pinyin'] = '';
            }
            
            $reports[] = $row;
        }
        
        // 生成分页数据
        $pagination = array(
            'total_items' => $total_items,
            'total_pages' => $total_pages,
            'current_page' => $page,
            'page_size' => $page_size
        );
        
        // 生成分页链接
        $query_params = $_GET;
        unset($query_params['page']);
        $query_string = http_build_query($query_params);
        
        // 生成页码链接
        $page_links = array();
        $page_window = 5; // 显示的页码数量
        
        $start_page = max(1, $page - floor($page_window / 2));
        $end_page = min($total_pages, $start_page + $page_window - 1);
        
        if ($end_page - $start_page + 1 < $page_window) {
            $start_page = max(1, $end_page - $page_window + 1);
        }
        
        for ($i = $start_page; $i <= $end_page; $i++) {
            $page_links[$i] = "report.php?" . ($query_string ? "$query_string&" : "") . "page=$i";
        }
        
        $pagination['page_links'] = $page_links;
        $pagination['previous_link'] = "report.php?" . ($query_string ? "$query_string&" : "") . "page=" . ($page - 1);
        $pagination['next_link'] = "report.php?" . ($query_string ? "$query_string&" : "") . "page=" . ($page + 1);

        // 获取状态统计数据
        $status_counts = array();

        // 统计未处理的举报
        $pending_sql = "SELECT COUNT(*) AS count FROM `reports` WHERE status = 0";
        $pending_result = $db->query($pending_sql);
        $pending_data = $db->fetch_array($pending_result);
        $status_counts['pending'] = $pending_data['count'];

        // 统计已处理的举报
        $processed_sql = "SELECT COUNT(*) AS count FROM `reports` WHERE status = 1";
        $processed_result = $db->query($processed_sql);
        $processed_data = $db->fetch_array($processed_result);
        $status_counts['processed'] = $processed_data['count'];

        // 统计全部举报
        $total_sql = "SELECT COUNT(*) AS count FROM `reports`";
        $total_result = $db->query($total_sql);
        $total_data = $db->fetch_array($total_result);
        $status_counts['total'] = $total_data['count'];

        // 模板赋值
        $tpl->assign('current_page', $current_page);
        $tpl->assign('reports', $reports);
        $tpl->assign('pagination', $pagination);
        $tpl->assign('status', $status);
        $tpl->assign('type', $type);
        $tpl->assign('post_id', $post_id);
        $tpl->assign('keyword', $keyword);
        $tpl->assign('status_counts', $status_counts);
        $tpl->assign('message', $message);
        $tpl->assign('admin', $_SESSION['admin']);
        $tpl->assign('breadcrumb', '举报管理');
        
        // 显示模板
        $tpl->display('report_list.htm');
        break;
        
    // 查看详情
    case 'detail':
        $id = isset($_GET['id']) ? intval($_GET['id']) : 0;
        
        if ($id <= 0) {
            redirect('report.php', '无效的举报ID');
        }
        
        // 查询举报详情
        $report_sql = "SELECT r.* FROM `reports` r WHERE r.id = $id";
        $report_result = $db->query($report_sql);
        
        if ($db->num_rows($report_result) == 0) {
            redirect('report.php', '举报记录不存在');
        }
        
        $report = $db->fetch_array($report_result);
        
        // 格式化时间
        if (isset($report['created_at']) && !empty($report['created_at']) && is_numeric($report['created_at'])) {
            $report['created_at'] = date('Y-m-d H:i:s', $report['created_at']);
        } else {
            $report['created_at'] = '未知时间';
        }
        
        // 查询被举报的信息
        $post = null;
        if ($report['post_id'] > 0) {
            $post_sql = "SELECT p.*, c.pinyin as category_pinyin, pc.contact_name as username
                         FROM `posts` p
                         LEFT JOIN `categories` c ON p.category_id = c.id
                         LEFT JOIN `post_contents` pc ON p.id = pc.post_id
                         WHERE p.id = {$report['post_id']}";
            $post_result = $db->query($post_sql);
            
            if ($db->num_rows($post_result) > 0) {
                $post = $db->fetch_array($post_result);
                
                // 格式化信息发布时间
                if (isset($post['created_at']) && !empty($post['created_at']) && is_numeric($post['created_at'])) {
                    $post['created_at'] = date('Y-m-d H:i:s', $post['created_at']);
                } else {
                    $post['created_at'] = '未知时间';
                }
            }
        }
        
        // 模板赋值
        $tpl->assign('current_page', $current_page);
        $tpl->assign('report', $report);
        $tpl->assign('post', $post);
        $tpl->assign('message', $message);
        $tpl->assign('admin', $_SESSION['admin']);
        $tpl->assign('breadcrumb', '举报详情');
        
        // 显示模板
        $tpl->display('report_detail.htm');
        break;
        
    // 切换状态
    case 'toggle_status':
        $id = isset($_GET['id']) ? intval($_GET['id']) : 0;
        
        if ($id <= 0) {
            redirect('report.php', '无效的举报ID');
        }
        
        // 查询当前状态
        $status_sql = "SELECT status FROM `reports` WHERE id = $id";
        $status_result = $db->query($status_sql);
        
        if ($db->num_rows($status_result) == 0) {
            redirect('report.php', '举报记录不存在');
        }
        
        $status_data = $db->fetch_array($status_result);
        $current_status = $status_data['status'];
        
        // 切换状态
        $new_status = $current_status == 0 ? 1 : 0;
        $update_sql = "UPDATE `reports` SET status = $new_status WHERE id = $id";
        $db->query($update_sql);
        
        // 返回列表页
        redirect('report.php', '状态已更新');
        break;
        
    // 更新状态
    case 'update_status':
        $id = isset($_POST['id']) ? intval($_POST['id']) : 0;
        $status = isset($_POST['status']) ? intval($_POST['status']) : 0;
        
        if ($id <= 0) {
            redirect('report.php', '无效的举报ID');
        }
        
        // 更新状态
        $update_sql = "UPDATE `reports` SET status = $status WHERE id = $id";
        $db->query($update_sql);
        
        // 返回详情页
        redirect("report.php?action=detail&id=$id", '状态已更新');
        break;
        
    // 删除举报
    case 'delete':
        $id = isset($_GET['id']) ? intval($_GET['id']) : 0;
        
        if ($id <= 0) {
            redirect('report.php', '无效的举报ID');
        }
        
        // 删除记录
        $delete_sql = "DELETE FROM `reports` WHERE id = $id";
        $db->query($delete_sql);
        
        // 返回列表页
        redirect('report.php', '举报记录已删除');
        break;
        
    // 批量删除
    case 'batch_delete':
        if (!isset($_POST['report_ids']) || !is_array($_POST['report_ids']) || empty($_POST['report_ids'])) {
            redirect('report.php', '请选择要删除的记录');
        }
        
        $ids = array_map('intval', $_POST['report_ids']);
        $id_string = implode(',', $ids);
        
        // 执行批量删除
        $delete_sql = "DELETE FROM `reports` WHERE id IN ($id_string)";
        $db->query($delete_sql);
        
        // 返回列表页
        redirect('report.php', '已成功删除所选记录');
        break;
} 