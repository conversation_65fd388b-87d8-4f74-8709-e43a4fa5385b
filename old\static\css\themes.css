/* 主题CSS变量 */

/* 原有主题保持不变 */
/* 红色主题 */
.theme-red {
    --primary-color: #e53935;
    --primary-color-rgb: 229,57,53;
    --secondary-color: #fb8c8a;
    --accent-color: #c62828;
    --text-color: #333333;
    --text-light: #757575;
    --bg-color: #f5f5f5;
    --card-color: #ffffff;
    --border-color: #eeeeee;
    --shadow: none;
    --radius: 0px;
    --soft-bg-color: #f5f5f5;
}

/* 蓝色主题 */
.theme-blue {
    --primary-color: #4285f4;
    --primary-color-rgb: 66,133,244;
    --secondary-color: #34a853;
    --accent-color: #ea4335;
    --text-color: #333333;
    --text-light: #757575;
    --bg-color: #f5f5f5;
    --card-color: #ffffff;
    --border-color: #eaeaea;
    --shadow: none;
    --radius: 0px;
    --soft-bg-color: #f5f5f5;
}

/* 橙色主题 */
.theme-orange {
    --primary-color: #ff6b01;
    --primary-color-rgb: 255,107,1;
    --secondary-color: #ff9d45;
    --accent-color: #e64a19;
    --text-color: #333333;
    --text-light: #757575;
    --bg-color: #f5f5f5;
    --card-color: #ffffff;
    --border-color: #eeeeee;
    --shadow: none;
    --radius: 0px;
    --soft-bg-color: #f5f5f5;
}

/* 绿色主题 */
.theme-green {
    --primary-color: #00a878;
    --primary-color-rgb: 0,168,120;
    --secondary-color: #5bc8ac;
    --accent-color: #408e91;
    --text-color: #333333;
    --text-light: #757575;
    --bg-color: #f5f5f5;
    --card-color: #ffffff;
    --border-color: #eeeeee;
    --shadow: none;
    --radius: 0px;
    --soft-bg-color: #f5f5f5;
}

/* 紫色主题 */
.theme-purple {
    --primary-color: #7b68ee;
    --primary-color-rgb: 123,104,238;
    --secondary-color: #a290e3;
    --accent-color: #6a5acd;
    --text-color: #333333;
    --text-light: #757575;
    --bg-color: #f5f5f5;
    --card-color: #ffffff;
    --border-color: #eeeeee;
    --shadow: none;
    --radius: 0px;
    --soft-bg-color: #f5f5f5;
}

/* 粉色主题 */
.theme-pink {
    --primary-color: #e91e63;
    --primary-color-rgb: 233,30,99;
    --secondary-color: #ff4081;
    --accent-color: #c2185b;
    --text-color: #333333;
    --text-light: #757575;
    --bg-color: #f5f5f5;
    --card-color: #ffffff;
    --border-color: #eeeeee;
    --shadow: none;
    --radius: 0px;
    --soft-bg-color: #f5f5f5;
}

/* 海洋主题 */
.theme-ocean {
    --primary-color: #006994;
    --primary-color-rgb: 0,105,148;
    --secondary-color: #48b1bf;
    --accent-color: #00496b;
    --text-color: #333333;
    --text-light: #757575;
    --bg-color: #f5f5f5;
    --card-color: #ffffff;
    --border-color: #eeeeee;
    --shadow: none;
    --radius: 0px;
    --soft-bg-color: #f5f5f5;
}

/* 微信风格主题 - 新增 */
.theme-wechat {
    --primary-color: #07c160;
    --primary-color-rgb: 7,193,96;
    --secondary-color: #95ec95;
    --accent-color: #06ad56;
    --text-color: #191919;
    --text-light: #7f7f7f;
    --bg-color: #f5f5f5;
    --card-color: #ffffff;
    --border-color: #eeeeee;
    --shadow: none;
    --radius: 4px;
    --soft-bg-color: #f5f5f5;
}

/* 支付宝风格主题 - 新增 */
.theme-alipay {
    --primary-color: #1677ff;
    --primary-color-rgb: 22,119,255;
    --secondary-color: #69b5ff;
    --accent-color: #0066ff;
    --text-color: #333333;
    --text-light: #999999;
    --bg-color: #f5f5f5;
    --card-color: #ffffff;
    --border-color: #eeeeee;
    --shadow: none;
    --radius: 8px;
    --soft-bg-color: #f5f5f5;
}

/* 抖音风格主题 - 新增 */
.theme-douyin {
    --primary-color: #fe2c55;
    --primary-color-rgb: 254,44,85;
    --secondary-color: #ff6b88;
    --accent-color: #ed1d41;
    --text-color: #121212;
    --text-light: #8a8a8a;
    --bg-color: #f5f5f5;
    --card-color: #ffffff;
    --border-color: #2f2f2f;
    --shadow: none;
    --radius: 6px;
    --soft-bg-color: #f5f5f5;
}

/* 小米风格主题 - 新增 */
.theme-miui {
    --primary-color: #ff6b00;
    --primary-color-rgb: 255,107,0;
    --secondary-color: #ff9248;
    --accent-color: #f25d00;
    --text-color: #333333;
    --text-light: #666666;
    --bg-color: #f5f5f5;
    --card-color: #ffffff;
    --border-color: #eeeeee;
    --shadow: none;
    --radius: 12px;
    --soft-bg-color: #f5f5f5;
}

/* 简约风格主题 - 新增 */
.theme-simple {
    --primary-color: #4a90e2;
    --primary-color-rgb: 74,144,226;
    --secondary-color: #76aff0;
    --accent-color: #333333;
    
    /* 搜索层专用样式变量 */
    --search-cancel-bg: #f5f5f5;
    --search-cancel-color: #333333;
    --search-clear-bg: #f5f5f5;
    --search-clear-color: #333333;
    --search-header-bg: #ffffff;
    --search-header-border: #eeeeee;
    --search-input-bg: #f5f5f5;
    --search-input-color: #333333;
    --search-placeholder-color: #999999;
    --search-icon-color: #999999;
    --text-color: #333333;
    --text-light: #666666;
    --bg-color: #f5f5f5;
    --card-color: #ffffff;
    --border-color: #eeeeee;
    --shadow: none;
    --radius: 8px;
    --soft-bg-color: #f5f5f5;
    --header-text-color: #333333;
    --header-border-color: #eeeeee;
    --header-bg-color: #ffffff;
    --btn-text-color: #ffffff;
}

/* 主题特定样式覆盖 */
[class*="theme-"] header {
    background-color: var(--primary-color) !important;
    border-radius: var(--radius);
    border-bottom: 1px solid rgba(0,0,0,0.1);
}

/* 确保主题简约下的所有按钮颜色正确 */
.theme-simple .btn,
.theme-simple button,
.theme-simple input[type="button"],
.theme-simple input[type="submit"],
.theme-simple .action-btn {
    background-color: var(--primary-color);
    color: var(--btn-text-color);
    border: none;
}

/* 简约主题特殊处理 */
.theme-simple header {
    color: var(--header-text-color) !important;
    border-bottom: 1px solid var(--header-border-color) !important;
}

.theme-simple .header-back,
.theme-simple .header-title,
.theme-simple .header-share,
.theme-simple .header-search-icon {
    color: var(--header-text-color) !important;
}

.theme-simple .header-back:active,
.theme-simple .header-share:active,
.theme-simple .header-search-icon:active {
    background-color: rgba(0,0,0,0.05) !important;
}

/* 简约主题搜索层特殊处理 */
.theme-simple .search-layer {
    background-color: var(--header-bg-color, #ffffff) !important;
    border-bottom: 1px solid var(--header-border-color) !important;
}

.theme-simple .search-layer .search-header {
    border-bottom: 1px solid var(--border-color) !important;
}

.theme-simple .search-layer .search-back,
.theme-simple .search-layer .search-cancel {
    color: var(--header-text-color, #333333) !important;
    background-color: transparent !important;
    border: none !important;
}

.theme-simple .search-layer .search-form {
    background-color: rgba(0,0,0,0.05) !important;
}

.theme-simple .search-layer .search-icon {
    color: var(--header-text-color, #333333) !important;
    opacity: 0.7;
}

.theme-simple .search-layer .search-input {
    color: var(--header-text-color, #333333) !important;
}

.theme-simple .search-layer .search-input::placeholder {
    color: var(--text-light, #666666) !important;
    opacity: 0.7;
}

.theme-simple .search-section-title .search-clear {
    color: var(--header-text-color, #333333) !important;
    background-color: #f5f5f5 !important;
    border: none !important;
    border-radius: 4px !important;
    padding: 4px 8px !important;
}

.theme-simple .search-section-title .search-clear:active {
    background-color: #e8e8e8 !important;
}

/* 简约主题按钮显示增强 */
.theme-simple .post-meta-actions a,
.theme-simple .manage-actions a,
.theme-simple .float-btns a,
.theme-simple .nav-actions a,
.theme-simple .search-btn,
.theme-simple .share-btn,
.theme-simple .action-btn {
    background-color: var(--primary-color);
    color: var(--btn-text-color);
    border: none;
    border-radius: var(--radius);
    padding: 8px 12px;
    display: inline-block;
    text-align: center;
    margin: 2px;
    text-decoration: none;
}

.theme-simple .btn-text-only {
    background-color: transparent;
    color: var(--primary-color);
    border: 1px solid var(--primary-color);
}

[class*="theme-"] .search-btn:not(.theme-neutral) {
    background-color: var(--primary-color) !important;
    border-radius: var(--radius);
}

[class*="theme-"] .btn-primary:not(.theme-neutral) {
    background-color: var(--primary-color) !important;
    border-radius: var(--radius);
}

/* 主题中立类 */
.theme-neutral {
    background-color: #ff6600 !important;
    color: #ffffff !important;
}

/* 移动端适配 */
@media (max-width: 768px) {
    [class*="theme-"] {
        --radius: calc(var(--radius) * 1.5); /* 移动端增大圆角 */
    }
}