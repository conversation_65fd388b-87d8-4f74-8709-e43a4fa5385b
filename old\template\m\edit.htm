<!DOCTYPE html>
<?php
// 获取主题设置
$theme = isset($_COOKIE['site_theme']) ? $_COOKIE['site_theme'] : 'orange';
$theme_class = 'theme-' . $theme;
?>
<html lang="zh-CN" class="<?php echo $theme_class; ?>">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <title>编辑信息 - <?php echo $post['title']; ?></title>
    <link rel="stylesheet" href="/static/font-awesome/css/all.min.css">
    <link rel="stylesheet" href="/static/css/themes.css">
    <link rel="stylesheet" href="/template/m/css/common.css">
    <link rel="stylesheet" href="/template/m/css/post.css">
    <link rel="stylesheet" href="/static/css/image-compress.css">
    <style>
        /* 确保简约主题头部为白色背景 */
        .theme-simple header {
            background-color: #ffffff !important;
        }
    </style>
</head>
<body>
    <header>
        <div class="header-inner">
            <div class="header-left">
                <a href="/<?php echo $post['category_pinyin']; ?>/<?php echo $post['id']; ?>.html" class="header-back"><i class="fas fa-chevron-left"></i></a>
            </div>
            <div class="header-title">编辑信息</div>
            <div class="header-right"></div>
        </div>
    </header>

    <div class="breadcrumb">
        <div class="container">
            <a href="/"><i class="fas fa-home"></i> 首页</a>
            <span class="separator"></span>
            <a href="/<?php echo $post['category_pinyin']; ?>/"><?php echo $post['category_name']; ?></a>
            <span class="separator"></span>
            <a href="/<?php echo $post['category_pinyin']; ?>/<?php echo $post['id']; ?>.html"><?php echo $post['title']; ?></a>
            <span class="separator"></span>
            <span class="current">编辑</span>
        </div>
    </div>

    <div class="post-content">
        <form id="edit-form" action="/post.php?action=edit&id=<?php echo $post['id']; ?>" method="post" enctype="multipart/form-data">
            <input type="hidden" name="id" value="<?php echo $post['id']; ?>">
            <input type="hidden" name="password" value="<?php echo isset($password) ? $password : ''; ?>">
            <input type="hidden" name="submit" value="1">
            <input type="hidden" name="csrf_token" value="<?php echo generate_csrf_token(); ?>">
            
            <div class="form-panel">
                <div class="form-group">
                    <label class="form-label">栏目</label>
                    <div class="form-right category-selected">
                        <div class="category-name"><strong><?php echo $post['category_name']; ?></strong> <a href="/post.php" class="reselect-category"><i class="fas fa-arrow-left"></i>重新选择栏目</a></div>
                    </div>
                    <input type="hidden" name="category_id" value="<?php echo $post['category_id']; ?>">
                </div>
                
                <div class="form-group">
                    <label for="region_id" class="form-label required">地区</label>
                    <div class="form-right">
                        <select name="region_id" id="region_id" class="form-control form-select" required>
                            <option value="">请选择地区：</option>
                            <?php foreach ($regions as $province): ?>
                            <optgroup label="<?php echo $province['name']; ?>">
                                <?php foreach ($province['children'] as $city): ?>
                                <option value="<?php echo $city['id']; ?>" <?php echo ($post['region_id'] == $city['id']) ? 'selected' : ''; ?>><?php echo $city['name']; ?></option>
                                <?php endforeach; ?>
                            </optgroup>
                            <?php endforeach; ?>
                        </select>
                    </div>
                </div>
                
                <div class="form-group">
                    <label for="expire_days" class="form-label required">有效期</label>
                    <div class="form-right">
                        <select name="expire_days" id="expire_days" class="form-control form-select" required>
                            <option value="7" <?php echo ($post['expire_days'] == 7) ? 'selected' : ''; ?>>7天</option>
                            <option value="15" <?php echo ($post['expire_days'] == 15) ? 'selected' : ''; ?>>15天</option>
                            <option value="30" <?php echo ($post['expire_days'] == 30 || empty($post['expire_days'])) ? 'selected' : ''; ?>>30天</option>
                            <option value="60" <?php echo ($post['expire_days'] == 60) ? 'selected' : ''; ?>>60天</option>
                            <option value="90" <?php echo ($post['expire_days'] == 90) ? 'selected' : ''; ?>>90天</option>
                        </select>
                    </div>
                </div>
                
                <div class="form-group">
                    <label for="title" class="form-label required">标题</label>
                    <div class="form-right">
                        <input type="text" class="form-control" id="title" name="title" value="<?php echo $post['title']; ?>" required>
                    </div>
                </div>
                
                <div class="form-group form-group-last">
                    <label for="content" class="form-label required">内容</label>
                    <div class="form-right">
                        <textarea class="form-control form-textarea" id="content" name="content" required><?php echo $post['content']; ?></textarea>
                    </div>
                </div>
            </div>

            <div class="form-panel">
                <div class="form-group form-group-noborder">
                    <label class="form-label">图片</label>
                    <div class="form-right">
                        <div class="image-upload">
                            <input type="file" id="image_uploads" name="images[]" accept="image/*" multiple class="hidden-input">
                            <label for="image_uploads" class="upload-btn">
                                <i class="fas fa-plus"></i>
                            </label>
                            <span class="form-hint image-hint">支持JPG、PNG格式，最多{$upload_config.max_count}张</span>
                            
                            <div class="image-previews" id="image-previews">
                                <?php if (!empty($post_images)): ?>
                                    <?php foreach ($post_images as $image): ?>
                                    <div class="image-item">
                                        <img src="<?php echo isset($image['url']) ? $image['url'] : $image['thumb_path']; ?>" alt="已上传图片">
                                        <span class="remove-image" data-id="<?php echo $image['id']; ?>">×</span>
                                        <input type="hidden" name="existing_images[]" value="<?php echo $image['id']; ?>">
                                    </div>
                                    <?php endforeach; ?>
                                <?php elseif (!empty($images)): ?>
                                    <?php foreach ($images as $image): ?>
                                    <div class="image-item">
                                        <img src="<?php echo isset($image['url']) ? $image['url'] : $image['thumb_path']; ?>" alt="已上传图片">
                                        <span class="remove-image" data-id="<?php echo $image['id']; ?>">×</span>
                                        <input type="hidden" name="existing_images[]" value="<?php echo $image['id']; ?>">
                                    </div>
                                    <?php endforeach; ?>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <?php if (!empty($custom_fields)): ?>
            <div class="form-panel" id="custom-fields">
                <?php foreach ($custom_fields as $field): ?>
                <div class="form-group">
                    <label for="field_<?php echo $field['id']; ?>" class="form-label <?php echo $field['required'] ? 'required' : ''; ?>">
                        <?php echo $field['label']; ?>
                    </label>
                    <div class="form-right">
                    <?php if ($field['type'] == 'text'): ?>
                    <input type="text" name="fields[<?php echo $field['id']; ?>]" id="field_<?php echo $field['id']; ?>" 
                           class="form-control" value="<?php echo isset($field_values[$field['id']]) ? $field_values[$field['id']] : ''; ?>" <?php echo $field['required'] ? 'required' : ''; ?>>
                    
                    <?php elseif ($field['type'] == 'textarea'): ?>
                    <textarea name="fields[<?php echo $field['id']; ?>]" id="field_<?php echo $field['id']; ?>" 
                              class="form-control form-textarea" <?php echo $field['required'] ? 'required' : ''; ?>><?php echo isset($field_values[$field['id']]) ? $field_values[$field['id']] : ''; ?></textarea>
                    
                    <?php elseif ($field['type'] == 'select'): ?>
                    <select name="fields[<?php echo $field['id']; ?>]" id="field_<?php echo $field['id']; ?>" 
                            class="form-control form-select" <?php echo $field['required'] ? 'required' : ''; ?>>
                        <option value="">请选择</option>
                        <?php foreach (explode(',', $field['options']) as $option): ?>
                        <option value="<?php echo $option; ?>" <?php echo (isset($field_values[$field['id']]) && $field_values[$field['id']] == $option) ? 'selected' : ''; ?>><?php echo $option; ?></option>
                        <?php endforeach; ?>
                    </select>
                    
                    <?php elseif ($field['type'] == 'radio'): ?>
                    <div class="radio-group">
                        <?php foreach (explode(',', $field['options']) as $key => $option): ?>
                        <div class="radio-item">
                            <input type="radio" name="fields[<?php echo $field['id']; ?>]" id="field_<?php echo $field['id']; ?>_<?php echo $key; ?>" 
                                   value="<?php echo $option; ?>" <?php echo (isset($field_values[$field['id']]) && $field_values[$field['id']] == $option) ? 'checked' : ($key == 0 && $field['required'] ? 'required' : ''); ?>>
                            <label for="field_<?php echo $field['id']; ?>_<?php echo $key; ?>"><?php echo $option; ?></label>
                        </div>
                        <?php endforeach; ?>
                    </div>
                    
                    <?php elseif ($field['type'] == 'checkbox'): ?>
                    <div class="checkbox-group">
                        <?php 
                        $saved_values = isset($field_values[$field['id']]) ? explode(',', $field_values[$field['id']]) : array();
                        foreach (explode(',', $field['options']) as $key => $option): 
                        ?>
                        <div class="checkbox-item">
                            <input type="checkbox" name="fields[<?php echo $field['id']; ?>][]" id="field_<?php echo $field['id']; ?>_<?php echo $key; ?>" 
                                   value="<?php echo $option; ?>" <?php echo in_array($option, $saved_values) ? 'checked' : ''; ?>>
                            <label for="field_<?php echo $field['id']; ?>_<?php echo $key; ?>"><?php echo $option; ?></label>
                        </div>
                        <?php endforeach; ?>
                    </div>
                    
                    <?php elseif ($field['type'] == 'number'): ?>
                    <input type="number" name="fields[<?php echo $field['id']; ?>]" id="field_<?php echo $field['id']; ?>" 
                           class="form-control" value="<?php echo isset($field_values[$field['id']]) ? $field_values[$field['id']] : ''; ?>" <?php echo $field['required'] ? 'required' : ''; ?>>
                    
                    <?php endif; ?>
                    
                    <?php if (!empty($field['hint'])): ?>
                    <div class="form-hint"><?php echo $field['hint']; ?></div>
                    <?php endif; ?>
                    </div>
                </div>
                <?php endforeach; ?>
            </div>
            <?php endif; ?>

            <div class="form-panel">
                <div class="form-group">
                    <label for="contact_name" class="form-label required">联系人</label>
                    <div class="form-right">
                        <input type="text" class="form-control" id="contact_name" name="contact_name" value="<?php echo $post['contact_name']; ?>" required>
                    </div>
                </div>
                
                <div class="form-group">
                    <label for="contact_mobile" class="form-label required">手机</label>
                    <div class="form-right">
                        <input type="tel" class="form-control" id="contact_mobile" name="contact_mobile" value="<?php echo $post['contact_mobile']; ?>" required pattern="[0-9]{11}">
                    </div>
                </div>
                
                <div class="form-group form-group-noborder">
                    <label for="contact_weixin" class="form-label">微信</label>
                    <div class="form-right">
                        <input type="text" class="form-control" id="contact_weixin" name="contact_weixin" value="<?php echo $post['contact_weixin']; ?>">
                        <label class="checkbox weixin-checkbox">
                            <input type="checkbox" id="weixin_same"> 与手机号相同
                        </label>
                    </div>
                </div>
            </div>
        </form>
    </div>

    <!-- 通栏提交按钮 -->
    <div class="submit-group">
        <button type="submit" name="submit" class="submit-button" id="submit-btn" form="edit-form">
            保存修改
        </button>
    </div>

    <!-- 加载中遮罩 -->
    <div class="loading-overlay" id="loading-overlay">
        <div class="loading-spinner">
            <i class="fas fa-spinner fa-spin"></i>
            <div class="loading-text">正在提交中...</div>
        </div>
    </div>
    
    <!-- 提交成功提示 -->
    <div class="success-overlay" id="success-overlay">
        <div class="success-message">
            <i class="fas fa-check-circle"></i>
            <div class="success-text">修改成功！</div>
            <div class="success-subtext">您的信息已更新</div>
            <button class="success-btn" id="success-btn">确定</button>
        </div>
    </div>

    <footer>
        <div class="container">
            <p>© <?php echo date('Y'); ?> <?php echo $site_name; ?> 版权所有</p>
        </div>
    </footer>

    <!-- 移动端友好的提示框 -->
    <div class="toast-container" id="toast-container"></div>
    
    <!-- 移动端友好的确认对话框 -->
    <div class="confirm-overlay" id="confirm-dialog">
        <div class="confirm-dialog">
            <div class="confirm-title" id="confirm-message">确定要删除吗？</div>
            <div class="confirm-buttons">
                <button class="confirm-btn cancel" id="confirm-cancel">取消</button>
                <button class="confirm-btn confirm" id="confirm-ok">确定</button>
            </div>
        </div>
    </div>

    <script>
    document.addEventListener('DOMContentLoaded', function() {
        // 表单验证和AJAX提交
        const form = document.getElementById('edit-form');
        const loadingOverlay = document.getElementById('loading-overlay');
        const successOverlay = document.getElementById('success-overlay');
        const successBtn = document.getElementById('success-btn');
        
        form.addEventListener('submit', function(event) {
            let hasError = false;
            let firstErrorField = null;
            
            // 获取所有必填字段
            const requiredFields = form.querySelectorAll('[required]');
            
            // 检查每个必填字段
            requiredFields.forEach(function(field) {
                if (!field.value.trim()) {
                    hasError = true;
                    field.style.borderColor = '#ff3b30';
                    
                    // 记录第一个错误字段
                    if (!firstErrorField) {
                        firstErrorField = field;
                    }
                    
                    // 添加错误提示
                    let errorMsg = document.createElement('div');
                    errorMsg.className = 'form-error';
                    errorMsg.textContent = '请填写' + field.closest('.form-group').querySelector('.form-label').textContent.trim();
                    
                    // 移除之前的错误提示
                    let existingError = field.parentNode.querySelector('.form-error');
                    if (existingError) {
                        existingError.remove();
                    }
                    
                    // 添加新的错误提示
                    field.parentNode.appendChild(errorMsg);
                } else {
                    field.style.borderColor = '#eee';
                    
                    // 移除错误提示
                    let existingError = field.parentNode.querySelector('.form-error');
                    if (existingError) {
                        existingError.remove();
                    }
                }
            });
            
            // 如果有错误，阻止表单提交并滚动到第一个错误字段
            if (hasError) {
                event.preventDefault();
                if (firstErrorField) {
                    firstErrorField.focus();
                    firstErrorField.scrollIntoView({ behavior: 'smooth', block: 'center' });
                }
                return false;
            }
            
            // 显示加载中提示
            loadingOverlay.classList.add('show');
            
            // 使用AJAX提交表单
            event.preventDefault();
            
            const formData = new FormData(form);

            // 添加ajax标记
            formData.append('ajax', '1');

            // 移除原始图片文件，添加压缩后的图片文件
            if (processedFiles && processedFiles.length > 0) {
                // 移除原始图片文件
                formData.delete('images[]');

                // 添加压缩后的文件
                processedFiles.forEach((file, index) => {
                    formData.append('images[]', file);
                });

                console.log('移动端使用压缩后的图片文件:', processedFiles);
            }

            // 整理已有图片数据
            const existingImages = [];
            document.querySelectorAll('.image-item input[name="existing_images[]"]').forEach(input => {
                existingImages.push(input.value);
            });
            if (existingImages.length > 0) {
                formData.append('existing_images', JSON.stringify(existingImages));
            }
            
            fetch(form.action, {
                method: 'POST',
                body: formData
            })
            .then(response => {
                if (!response.ok) {
                    throw new Error('提交失败，请重试');
                }
                return response.text();
            })
            .then(text => {
                // 隐藏加载中提示
                loadingOverlay.classList.remove('show');
                
                // 尝试解析响应
                let responseData;
                try {
                    responseData = JSON.parse(text);
                } catch (e) {
                    // 如果响应中包含成功信息，视为成功
                    if (text.includes('成功')) {
                        successOverlay.classList.add('show');
                        
                        // 设置成功按钮点击事件
                        successBtn.onclick = function() {
                            window.location.href = "/<?php echo $post['category_pinyin']; ?>/<?php echo $post['id']; ?>.html";
                        };
                        return;
                    } else {
                        alert('保存失败，请稍后再试');
                        return;
                    }
                }
                
                // 处理JSON响应
                if (responseData && responseData.success) {
                    // 显示成功提示
                    successOverlay.classList.add('show');
                    
                    // 点击确定按钮后跳转到详情页面
                    successBtn.onclick = function() {
                        if (responseData.detail_url) {
                            window.location.href = responseData.detail_url;
                        } else {
                            window.location.href = "/<?php echo $post['category_pinyin']; ?>/<?php echo $post['id']; ?>.html";
                        }
                    };
                } else {
                    // 显示错误信息
                    alert(responseData.message || '保存失败，请稍后再试');
                }
            })
            .catch(error => {
                // 隐藏加载中
                loadingOverlay.classList.remove('show');
                
                // 显示错误提示
                alert('提交失败，请稍后再试');
            });
        });
        
        // 移动端图片上传预览和压缩（编辑页面）
        const input = document.getElementById('image_uploads');
        const preview = document.getElementById('image-previews');

        // 初始化移动端图片压缩器（优先使用完整版，失败则使用简化版）
        let mobileCompressor;

        if (typeof MobileImageCompressor !== 'undefined') {
            mobileCompressor = new MobileImageCompressor({
                maxWidth: 1200,
                maxHeight: 1200,
                quality: 0.7,
                targetSize: {$upload_config.max_size} * 1024 * 1024,
                maxSize: 8 * 1024 * 1024,
                enableExifRotation: true,
                showProgress: true
            });
        } else {
            mobileCompressor = new SimpleMobileCompressor();
        }

        // 存储处理后的文件
        let processedFiles = [];

        // 简化的拍照图片处理函数
        async function processCamera(file) {
            return new Promise((resolve, reject) => {
                // 如果文件小于2MB，直接返回原文件
                if (file.size < 2 * 1024 * 1024) {
                    resolve(file);
                    return;
                }

                const reader = new FileReader();
                reader.onload = function(e) {
                    const img = new Image();
                    img.onload = function() {
                        try {
                            const canvas = document.createElement('canvas');
                            const ctx = canvas.getContext('2d');

                            // 计算压缩后的尺寸
                            let { width, height } = img;
                            const maxSize = 1200;

                            if (width > maxSize || height > maxSize) {
                                const ratio = Math.min(maxSize / width, maxSize / height);
                                width = Math.round(width * ratio);
                                height = Math.round(height * ratio);
                            }

                            canvas.width = width;
                            canvas.height = height;

                            // 绘制图片
                            ctx.drawImage(img, 0, 0, width, height);

                            // 转换为Blob
                            canvas.toBlob(function(blob) {
                                if (blob) {
                                    // 创建新文件，保持原文件名但改为jpg格式
                                    const newFileName = file.name.replace(/\.[^.]+$/, '.jpg');
                                    const processedFile = new File([blob], newFileName, {
                                        type: 'image/jpeg',
                                        lastModified: Date.now()
                                    });
                                    resolve(processedFile);
                                } else {
                                    resolve(file); // 处理失败时返回原文件
                                }
                            }, 'image/jpeg', 0.8);

                        } catch (error) {
                            resolve(file); // 出错时返回原文件
                        }
                    };
                    img.onerror = () => resolve(file); // 加载失败时返回原文件
                    img.src = e.target.result;
                };
                reader.onerror = () => resolve(file); // 读取失败时返回原文件
                reader.readAsDataURL(file);
            });
        }

        // 为相册选择和拍照都添加事件监听器
        async function handleImageSelection(event) {
            const files = Array.from(this.files);

            // 限制上传数量
            var maxCount = {$upload_config.max_count};

            // 计算已有图片数量（有data-id属性的是已有图片）
            var existingCount = document.querySelectorAll('.image-item .remove-image[data-id]').length;

            // 检查总数量是否超过限制
            if (existingCount + files.length > maxCount) {
                alert('图片总数不能超过' + maxCount + '张，当前已有' + existingCount + '张图片，最多还能上传' + (maxCount - existingCount) + '张');
                this.value = '';
                return;
            }

            if (files.length === 0) return;

            // 显示移动端进度提示
            const progressEl = mobileCompressor.showMobileProgress(document.body, true);

            try {
                // 处理每个文件
                for (let i = 0; i < files.length; i++) {
                    const file = files[i];

                    // 检查文件类型
                    if (!file.type.match('image.*')) {
                        console.warn('跳过非图片文件:', file.name);
                        continue;
                    }

                    try {
                        // 更新进度
                        const progressPercent = Math.round(((i + 1) / files.length) * 100);
                        mobileCompressor.updateMobileProgress(progressPercent, `正在处理第 ${i + 1} 张新图片...`);

                        // 压缩图片
                        const result = await mobileCompressor.compressFile(file, (percent, text) => {
                            mobileCompressor.updateMobileProgress(percent, text);
                        });

                        // 将压缩后的文件添加到processedFiles数组
                        processedFiles.push(result.file);

                        // 创建预览
                        const div = document.createElement('div');
                        div.className = 'image-item';
                        div.style.position = 'relative';
                        div.setAttribute('data-file-index', processedFiles.length - 1); // 记录文件索引

                        const img = document.createElement('img');
                        const reader = new FileReader();

                        reader.onload = function(e) {
                            img.src = e.target.result;
                        };
                        reader.readAsDataURL(result.file);

                        const removeBtn = document.createElement('span');
                        removeBtn.className = 'remove-image';
                        removeBtn.textContent = '×';
                        removeBtn.addEventListener('click', function() {
                            // 从processedFiles数组中移除对应的文件
                            const fileIndex = parseInt(div.getAttribute('data-file-index'));
                            if (fileIndex >= 0 && fileIndex < processedFiles.length) {
                                processedFiles.splice(fileIndex, 1);
                                // 更新其他元素的索引
                                document.querySelectorAll('.image-item[data-file-index]').forEach((item, idx) => {
                                    const currentIndex = parseInt(item.getAttribute('data-file-index'));
                                    if (currentIndex > fileIndex) {
                                        item.setAttribute('data-file-index', currentIndex - 1);
                                    }
                                });
                            }
                            div.remove();
                        });

                        // 添加压缩信息（移动端简化版）
                        if (result.compressed) {
                            const compressInfo = document.createElement('div');
                            compressInfo.className = 'compress-info';
                            compressInfo.style.cssText = `
                                position: absolute; bottom: 2px; left: 2px; right: 2px;
                                background: rgba(0,0,0,0.7); color: white;
                                font-size: 10px; padding: 2px 4px;
                                border-radius: 2px; text-align: center;
                            `;

                            const originalSize = (result.originalSize / 1024 / 1024).toFixed(1);
                            const finalSize = (result.finalSize / 1024 / 1024).toFixed(1);
                            compressInfo.textContent = `${originalSize}MB → ${finalSize}MB`;

                            div.appendChild(compressInfo);
                        }

                        div.appendChild(img);
                        div.appendChild(removeBtn);
                        preview.appendChild(div);

                    } catch (error) {
                        console.error('图片压缩失败:', error);
                        alert(`图片 "${file.name}" 处理失败: ${error.message}`);
                    }
                }

                // 隐藏进度提示
                mobileCompressor.showMobileProgress(document.body, false);

            } catch (error) {
                console.error('图片处理出错:', error);
                alert('图片处理出错: ' + error.message);
                mobileCompressor.showMobileProgress(document.body, false);
            }

            // 清空input的值，允许重复选择相同文件
            this.value = '';
        }

        // 绑定图片选择事件
        input.addEventListener('change', handleImageSelection);
        
        // 确认对话框相关
        const confirmDialog = document.getElementById('confirm-dialog');
        const confirmMessage = document.getElementById('confirm-message');
        const confirmOk = document.getElementById('confirm-ok');
        const confirmCancel = document.getElementById('confirm-cancel');
        
        // 显示确认对话框
        function showConfirm(message, callback) {
            confirmMessage.textContent = message;
            confirmDialog.classList.add('show');
            
            // 重新绑定确认按钮事件
            const oldConfirmOk = confirmOk.cloneNode(true);
            confirmOk.parentNode.replaceChild(oldConfirmOk, confirmOk);
            
            const newConfirmOk = document.getElementById('confirm-ok');
            newConfirmOk.addEventListener('click', function() {
                callback(true);
                confirmDialog.classList.remove('show');
            });
            
            confirmCancel.addEventListener('click', function() {
                callback(false);
                confirmDialog.classList.remove('show');
            });
        }
        
        // 删除已有图片
        const removeButtons = document.querySelectorAll('.remove-image[data-id]');
        removeButtons.forEach(function(button) {
            button.addEventListener('click', function() {
                const imageId = this.getAttribute('data-id');
                const container = this.closest('.image-item');
                const input = container.querySelector('input[name="existing_images[]"]');
                const postId = "<?php echo intval($post['id']); ?>";
                
                showConfirm('确定要删除这张图片吗？', function(confirmed) {
                    if (confirmed) {
                        // 调用API删除图片
                        fetch('/post.php?action=delete_image', {
                            method: 'POST',
                            headers: {
                                'Content-Type': 'application/x-www-form-urlencoded',
                                'X-Requested-With': 'XMLHttpRequest'
                            },
                            body: 'id=' + imageId + '&post_id=' + postId + '&csrf_token=<?php echo generate_csrf_token(); ?>'
                        })
                        .then(response => response.json())
                        .then(data => {
                            if (data.success) {
                                // 更改input为删除标记
                                input.name = 'deleted_images[]';
                                
                                // 隐藏图片项
                                container.style.display = 'none';
                                
                                // 显示成功提示
                                showToast('图片已成功删除', 'success');
                            } else {
                                showToast('删除失败: ' + data.message, 'error');
                            }
                        })
                        .catch(error => {
                            showToast('删除图片时发生错误', 'error');
                        });
                    }
                });
            });
        });
        
        // 点击确认对话框外部关闭它
        confirmDialog.addEventListener('click', function(e) {
            if (e.target === confirmDialog) {
                confirmDialog.classList.remove('show');
            }
        });
        
        // 移动端友好的提示函数
        function showToast(message, type = 'normal', duration = 2000) {
            const container = document.getElementById('toast-container');
            const toast = document.createElement('div');
            toast.className = 'toast ' + type;
            toast.textContent = message;
            
            container.appendChild(toast);
            
            // 触发重排以应用过渡效果
            setTimeout(() => {
                toast.classList.add('show');
            }, 10);
            
            // 设置自动消失
            setTimeout(() => {
                toast.classList.remove('show');
                
                // 动画结束后移除元素
                setTimeout(() => {
                    container.removeChild(toast);
                }, 300);
            }, duration);
        }
        
        // 微信号与手机号同步
        const mobileInput = document.getElementById('contact_mobile');
        const wechatInput = document.getElementById('contact_weixin');
        const wechatSame = document.getElementById('weixin_same');
        
        // 检查是否相同，设置初始状态
        if (mobileInput.value && mobileInput.value === wechatInput.value) {
            wechatSame.checked = true;
            wechatInput.disabled = true;
        }
        
        wechatSame.addEventListener('change', function() {
            if (this.checked) {
                wechatInput.value = mobileInput.value;
                wechatInput.disabled = true;
            } else {
                wechatInput.disabled = false;
            }
        });
        
        mobileInput.addEventListener('input', function() {
            if (wechatSame.checked) {
                wechatInput.value = this.value;
            }
        });
    });
    </script>
    <script src="/static/js/mobile-image-compress.js"></script>
    <script>
    // 简化的手机端图片压缩功能
    function SimpleMobileCompressor() {
        this.compressFile = function(file, progressCallback) {
            return new Promise((resolve, reject) => {
                if (!file.type.match('image.*')) {
                    reject(new Error('不支持的文件类型'));
                    return;
                }

                if (progressCallback) progressCallback(10, '正在读取图片...');

                const reader = new FileReader();
                reader.onload = function(e) {
                    const img = new Image();
                    img.onload = function() {
                        try {
                            if (progressCallback) progressCallback(50, '正在压缩图片...');

                            const canvas = document.createElement('canvas');
                            const ctx = canvas.getContext('2d');

                            // 手机端使用更小的尺寸
                            let { width, height } = img;
                            const maxWidth = 1200;
                            const maxHeight = 1200;

                            if (width > maxWidth || height > maxHeight) {
                                const ratio = Math.min(maxWidth / width, maxHeight / height);
                                width *= ratio;
                                height *= ratio;
                            }

                            canvas.width = width;
                            canvas.height = height;

                            // 绘制图片
                            ctx.drawImage(img, 0, 0, width, height);

                            if (progressCallback) progressCallback(90, '正在生成文件...');

                            // 转换为Blob
                            canvas.toBlob(function(blob) {
                                const compressedFile = new File([blob], file.name, {
                                    type: 'image/jpeg',
                                    lastModified: Date.now()
                                });

                                if (progressCallback) progressCallback(100, '压缩完成');

                                resolve({
                                    file: compressedFile,
                                    compressed: true,
                                    originalSize: file.size,
                                    finalSize: blob.size,
                                    compressionRatio: ((1 - blob.size / file.size) * 100).toFixed(1) + '%',
                                    dimensions: { width: Math.round(width), height: Math.round(height) }
                                });
                            }, 'image/jpeg', 0.7);

                        } catch (error) {
                            reject(error);
                        }
                    };
                    img.onerror = () => reject(new Error('图片加载失败'));
                    img.src = e.target.result;
                };
                reader.onerror = () => reject(new Error('文件读取失败'));
                reader.readAsDataURL(file);
            });
        };

        this.showMobileProgress = function(container, show) {
            // 简化的进度显示
            if (show) {
                if (!document.getElementById('simple-mobile-progress')) {
                    const progressEl = document.createElement('div');
                    progressEl.id = 'simple-mobile-progress';
                    progressEl.style.cssText = `
                        position: fixed; top: 50%; left: 50%; transform: translate(-50%, -50%);
                        background: rgba(0,0,0,0.8); color: white; padding: 20px;
                        border-radius: 8px; z-index: 9999; text-align: center;
                    `;
                    progressEl.innerHTML = '<div>正在处理图片...</div>';
                    document.body.appendChild(progressEl);
                }
            } else {
                const progressEl = document.getElementById('simple-mobile-progress');
                if (progressEl) progressEl.remove();
            }
        };

        this.updateMobileProgress = function(percent, text) {
            const progressEl = document.getElementById('simple-mobile-progress');
            if (progressEl && text) {
                progressEl.innerHTML = `<div>${text}</div>`;
            }
        };
    }
    </script>
</body>
</html>