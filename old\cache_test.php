<?php
/**
 * 缓存功能测试页面
 * 无需登录即可查看缓存优化效果
 * 访问: localhost/cache_test.php
 */

define('IN_BTMPS', true);
require_once './include/common.inc.php';

// 设置页面标题
$pageTitle = '缓存功能测试';

// 处理AJAX请求
if (isset($_GET['action'])) {
    header('Content-Type: application/json; charset=utf-8');
    
    switch ($_GET['action']) {
        case 'test_basic':
            echo json_encode(testBasicCache());
            break;
            
        case 'test_performance':
            echo json_encode(testPerformance());
            break;
            
        case 'get_stats':
            echo json_encode(getCacheTestStats());
            break;
            
        case 'cleanup':
            $cleaned = cache_cleanup();
            echo json_encode(['success' => true, 'cleaned' => $cleaned]);
            break;
            
        case 'warmup':
            $warmed = cache_warmup(['categories_all', 'regions_all']);
            echo json_encode(['success' => true, 'warmed' => $warmed]);
            break;
            
        default:
            echo json_encode(['error' => '未知操作']);
    }
    exit;
}

/**
 * 测试基础缓存功能
 */
function testBasicCache() {
    $results = [];
    
    // 测试数据
    $testData = [
        'string' => '测试字符串',
        'number' => 12345,
        'array' => [1, 2, 3, 4, 5],
        'object' => (object)['name' => '测试对象', 'value' => 100]
    ];
    
    // 测试不同类型的缓存
    foreach ($testData as $type => $data) {
        $key = "test_{$type}_" . time();
        
        // 设置缓存
        $setResult = cache_set($key, $data, 60);
        
        // 获取缓存
        $getData = cache_get($key);
        $getResult = ($getData === $data);
        
        // 删除缓存
        cache_delete($key);
        
        $results[] = [
            'type' => $type,
            'set' => $setResult,
            'get' => $getResult,
            'match' => $getResult
        ];
    }
    
    return ['success' => true, 'results' => $results];
}

/**
 * 测试缓存性能
 */
function testPerformance() {
    $iterations = 20;
    $testData = ['performance' => 'test', 'data' => range(1, 100)];
    
    // 写入性能测试
    $writeStart = microtime(true);
    for ($i = 0; $i < $iterations; $i++) {
        cache_set("perf_test_{$i}", $testData, 60);
    }
    $writeTime = microtime(true) - $writeStart;
    
    // 读取性能测试
    $readStart = microtime(true);
    for ($i = 0; $i < $iterations; $i++) {
        cache_get("perf_test_{$i}");
    }
    $readTime = microtime(true) - $readStart;
    
    // 清理测试缓存
    for ($i = 0; $i < $iterations; $i++) {
        cache_delete("perf_test_{$i}");
    }
    
    return [
        'success' => true,
        'iterations' => $iterations,
        'write_time' => round($writeTime * 1000, 2),
        'read_time' => round($readTime * 1000, 2),
        'avg_write' => round(($writeTime / $iterations) * 1000, 2),
        'avg_read' => round(($readTime / $iterations) * 1000, 2)
    ];
}

/**
 * 获取缓存统计信息
 */
function getCacheTestStats() {
    $info = $GLOBALS['file_cache']->getInfo();
    $stats = cache_stats();
    
    // 分层统计
    $levelStats = [];
    $levels = ['permanent', 'long', 'medium', 'short', 'temp'];
    
    foreach ($levels as $level) {
        $levelDir = DATA_PATH . 'cache/' . $level . '/';
        if (is_dir($levelDir)) {
            $files = glob($levelDir . '*.cache');
            $size = 0;
            foreach ($files as $file) {
                $size += filesize($file);
            }
            
            $levelStats[] = [
                'name' => $level,
                'count' => count($files),
                'size' => $size,
                'size_formatted' => formatBytes($size)
            ];
        }
    }
    
    return [
        'basic' => $info,
        'performance' => $stats,
        'levels' => $levelStats,
        'config' => [
            'cache_enable' => $GLOBALS['config']['cache_enable'] ?? 1,
            'cache_compress' => $GLOBALS['config']['cache_compress'] ?? 0
        ]
    ];
}

/**
 * 格式化字节数
 */
function formatBytes($bytes, $precision = 2) {
    $units = array('B', 'KB', 'MB', 'GB', 'TB');
    
    for ($i = 0; $bytes > 1024 && $i < count($units) - 1; $i++) {
        $bytes /= 1024;
    }
    
    return round($bytes, $precision) . ' ' . $units[$i];
}
?>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $pageTitle; ?></title>
    <style>
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }
        .header h1 {
            margin: 0;
            font-size: 2.5em;
        }
        .header p {
            margin: 10px 0 0 0;
            opacity: 0.9;
        }
        .content {
            padding: 30px;
        }
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        .stat-card {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 20px;
            text-align: center;
            border-left: 4px solid #007bff;
        }
        .stat-number {
            font-size: 2em;
            font-weight: bold;
            color: #007bff;
            margin-bottom: 5px;
        }
        .stat-label {
            color: #666;
            font-size: 0.9em;
        }
        .test-section {
            background: white;
            border: 1px solid #e9ecef;
            border-radius: 8px;
            margin-bottom: 20px;
            overflow: hidden;
        }
        .test-header {
            background: #f8f9fa;
            padding: 15px 20px;
            border-bottom: 1px solid #e9ecef;
            font-weight: bold;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        .test-content {
            padding: 20px;
        }
        .btn {
            background: #007bff;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
        }
        .btn:hover {
            background: #0056b3;
        }
        .btn-success {
            background: #28a745;
        }
        .btn-success:hover {
            background: #1e7e34;
        }
        .btn-warning {
            background: #ffc107;
            color: #212529;
        }
        .btn-warning:hover {
            background: #e0a800;
        }
        .result-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 15px;
        }
        .result-table th,
        .result-table td {
            padding: 10px;
            text-align: left;
            border-bottom: 1px solid #e9ecef;
        }
        .result-table th {
            background: #f8f9fa;
            font-weight: bold;
        }
        .status-success {
            color: #28a745;
            font-weight: bold;
        }
        .status-error {
            color: #dc3545;
            font-weight: bold;
        }
        .loading {
            display: none;
            color: #007bff;
        }
        .level-bar {
            height: 20px;
            background: #e9ecef;
            border-radius: 10px;
            overflow: hidden;
            margin: 10px 0;
        }
        .level-fill {
            height: 100%;
            background: linear-gradient(90deg, #007bff, #28a745);
            transition: width 0.3s ease;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🚀 缓存功能测试</h1>
            <p>测试优化后的缓存系统性能和功能</p>
        </div>
        
        <div class="content">
            <!-- 统计信息 -->
            <div class="stats-grid" id="stats-grid">
                <div class="stat-card">
                    <div class="stat-number" id="total-files">-</div>
                    <div class="stat-label">缓存文件数</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number" id="total-size">-</div>
                    <div class="stat-label">总占用空间</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number" id="hit-rate">-</div>
                    <div class="stat-label">缓存命中率</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number" id="compression">-</div>
                    <div class="stat-label">压缩状态</div>
                </div>
            </div>
            
            <!-- 基础功能测试 -->
            <div class="test-section">
                <div class="test-header">
                    <span>基础缓存功能测试</span>
                    <button class="btn" onclick="runBasicTest()">
                        <span class="loading">⟳</span> 运行测试
                    </button>
                </div>
                <div class="test-content">
                    <p>测试缓存的设置、获取、删除等基础操作</p>
                    <div id="basic-test-result"></div>
                </div>
            </div>
            
            <!-- 性能测试 -->
            <div class="test-section">
                <div class="test-header">
                    <span>缓存性能测试</span>
                    <button class="btn" onclick="runPerformanceTest()">
                        <span class="loading">⟳</span> 运行测试
                    </button>
                </div>
                <div class="test-content">
                    <p>测试缓存的读写性能</p>
                    <div id="performance-test-result"></div>
                </div>
            </div>
            
            <!-- 分层缓存统计 -->
            <div class="test-section">
                <div class="test-header">
                    <span>分层缓存统计</span>
                    <button class="btn" onclick="refreshStats()">
                        <span class="loading">⟳</span> 刷新统计
                    </button>
                </div>
                <div class="test-content">
                    <div id="level-stats"></div>
                </div>
            </div>
            
            <!-- 缓存管理 -->
            <div class="test-section">
                <div class="test-header">
                    <span>缓存管理操作</span>
                    <div>
                        <button class="btn btn-success" onclick="warmupCache()">预热缓存</button>
                        <button class="btn btn-warning" onclick="cleanupCache()">清理过期</button>
                    </div>
                </div>
                <div class="test-content">
                    <div id="management-result">
                        <p>点击上方按钮执行缓存管理操作</p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 页面加载时获取统计数据
        document.addEventListener('DOMContentLoaded', function() {
            refreshStats();
        });
        
        // 刷新统计数据
        function refreshStats() {
            showLoading();
            fetch('?action=get_stats')
                .then(response => response.json())
                .then(data => {
                    updateStats(data);
                    hideLoading();
                })
                .catch(error => {
                    console.error('获取统计数据失败:', error);
                    hideLoading();
                });
        }
        
        // 更新统计显示
        function updateStats(data) {
            document.getElementById('total-files').textContent = data.basic.count;
            document.getElementById('total-size').textContent = data.basic.total_size_formatted;
            document.getElementById('hit-rate').textContent = data.performance.hit_rate;
            document.getElementById('compression').textContent = data.config.cache_compress ? '已启用' : '未启用';
            
            // 更新分层统计
            updateLevelStats(data.levels);
        }
        
        // 更新分层统计
        function updateLevelStats(levels) {
            let html = '';
            let maxSize = Math.max(...levels.map(l => l.size));
            
            levels.forEach(function(level) {
                let percentage = maxSize > 0 ? (level.size / maxSize * 100) : 0;
                html += `
                    <div style="margin-bottom: 20px;">
                        <div style="display: flex; justify-content: space-between; margin-bottom: 5px;">
                            <span><strong>${level.name}</strong></span>
                            <span>${level.count} 个文件 (${level.size_formatted})</span>
                        </div>
                        <div class="level-bar">
                            <div class="level-fill" style="width: ${percentage}%"></div>
                        </div>
                    </div>
                `;
            });
            
            document.getElementById('level-stats').innerHTML = html || '<p>暂无缓存数据</p>';
        }
        
        // 运行基础测试
        function runBasicTest() {
            showLoading();
            fetch('?action=test_basic')
                .then(response => response.json())
                .then(data => {
                    displayBasicTestResult(data);
                    hideLoading();
                })
                .catch(error => {
                    console.error('基础测试失败:', error);
                    hideLoading();
                });
        }
        
        // 显示基础测试结果
        function displayBasicTestResult(data) {
            if (!data.success) {
                document.getElementById('basic-test-result').innerHTML = '<p class="status-error">测试失败</p>';
                return;
            }
            
            let html = '<table class="result-table"><tr><th>数据类型</th><th>设置</th><th>获取</th><th>匹配</th></tr>';
            
            data.results.forEach(function(result) {
                html += `<tr>
                    <td>${result.type}</td>
                    <td class="${result.set ? 'status-success' : 'status-error'}">${result.set ? '✓' : '✗'}</td>
                    <td class="${result.get ? 'status-success' : 'status-error'}">${result.get ? '✓' : '✗'}</td>
                    <td class="${result.match ? 'status-success' : 'status-error'}">${result.match ? '✓' : '✗'}</td>
                </tr>`;
            });
            
            html += '</table>';
            document.getElementById('basic-test-result').innerHTML = html;
        }
        
        // 运行性能测试
        function runPerformanceTest() {
            showLoading();
            fetch('?action=test_performance')
                .then(response => response.json())
                .then(data => {
                    displayPerformanceTestResult(data);
                    hideLoading();
                })
                .catch(error => {
                    console.error('性能测试失败:', error);
                    hideLoading();
                });
        }
        
        // 显示性能测试结果
        function displayPerformanceTestResult(data) {
            if (!data.success) {
                document.getElementById('performance-test-result').innerHTML = '<p class="status-error">测试失败</p>';
                return;
            }
            
            let html = `
                <table class="result-table">
                    <tr><th>测试项目</th><th>结果</th></tr>
                    <tr><td>测试次数</td><td>${data.iterations}</td></tr>
                    <tr><td>总写入时间</td><td>${data.write_time} 毫秒</td></tr>
                    <tr><td>总读取时间</td><td>${data.read_time} 毫秒</td></tr>
                    <tr><td>平均写入时间</td><td>${data.avg_write} 毫秒/次</td></tr>
                    <tr><td>平均读取时间</td><td>${data.avg_read} 毫秒/次</td></tr>
                </table>
            `;
            
            document.getElementById('performance-test-result').innerHTML = html;
        }
        
        // 预热缓存
        function warmupCache() {
            showLoading();
            fetch('?action=warmup')
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        document.getElementById('management-result').innerHTML = 
                            `<p class="status-success">✓ 缓存预热完成，预热了 ${data.warmed} 项</p>`;
                        refreshStats();
                    } else {
                        document.getElementById('management-result').innerHTML = 
                            '<p class="status-error">✗ 缓存预热失败</p>';
                    }
                    hideLoading();
                })
                .catch(error => {
                    console.error('预热失败:', error);
                    hideLoading();
                });
        }
        
        // 清理过期缓存
        function cleanupCache() {
            showLoading();
            fetch('?action=cleanup')
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        document.getElementById('management-result').innerHTML = 
                            `<p class="status-success">✓ 清理完成，删除了 ${data.cleaned} 个过期文件</p>`;
                        refreshStats();
                    } else {
                        document.getElementById('management-result').innerHTML = 
                            '<p class="status-error">✗ 清理失败</p>';
                    }
                    hideLoading();
                })
                .catch(error => {
                    console.error('清理失败:', error);
                    hideLoading();
                });
        }
        
        // 显示加载状态
        function showLoading() {
            document.querySelectorAll('.loading').forEach(el => el.style.display = 'inline');
        }
        
        // 隐藏加载状态
        function hideLoading() {
            document.querySelectorAll('.loading').forEach(el => el.style.display = 'none');
        }
    </script>
</body>
</html>
