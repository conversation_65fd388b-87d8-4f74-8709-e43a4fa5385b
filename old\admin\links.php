<?php
// 友情链接管理
define('IN_BTMPS', true);

// 引入公共文件
require_once '../include/common.inc.php';

// 检查管理员权限
if (!isset($_SESSION['admin']) || $_SESSION['admin']['is_login'] !== true) {
    header('Location: login.php');
    exit;
}

// 引入后台公共函数
require_once(dirname(__FILE__) . '/include/admin.fun.php');

// 修改模板目录为admin/template目录（必须在common.inc.php之后设置）
$tpl->setTemplateDir(dirname(__FILE__) . '/template/');

// 获取操作类型
$action = isset($_GET['action']) ? $_GET['action'] : 'list';

// 处理友情链接操作
switch ($action) {
    // 友情链接列表
    case 'list':
        // 获取当前页码
        $page = isset($_GET['page']) ? intval($_GET['page']) : 1;
        $page = max(1, $page);
        
        // 每页显示数量
        $per_page = 20;
        
        // 获取总数
        $count_sql = "SELECT COUNT(*) as total FROM links";
        $count_result = $db->query($count_sql);
        $total_items = $db->fetch_array($count_result)['total'];

        // 计算分页
        $total_pages = ceil($total_items / $per_page);
        $offset = ($page - 1) * $per_page;

        // 获取友情链接列表
        $sql = "SELECT * FROM links ORDER BY sort_order ASC, id DESC LIMIT {$offset}, {$per_page}";
        $result = $db->query($sql);
        
        $links = [];
        while ($row = $db->fetch_array($result)) {
            $links[] = $row;
        }
        
        // 分页信息
        $pagination = [
            'current_page' => $page,
            'total_pages' => $total_pages,
            'total_items' => $total_items,
            'per_page' => $per_page,
            'has_prev' => $page > 1,
            'has_next' => $page < $total_pages,
            'prev_page' => $page - 1,
            'next_page' => $page + 1
        ];
        
        // 分配变量到模板
        assign('links', $links);
        assign('pagination', $pagination);
        assign('current_page', 'links');
        assign('page_title', '友情链接管理');

        // 显示模板
        $tpl->display('links_list.htm');
        break;
        
    // 添加友情链接 - 重定向到列表页面
    case 'add':
        header('Location: links.php');
        exit;
        break;
        
    // 编辑友情链接 - 重定向到列表页面
    case 'edit':
        header('Location: links.php');
        exit;
        break;

    // AJAX保存友情链接
    case 'save':
        header('Content-Type: application/json');

        if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
            echo json_encode(['success' => false, 'error' => '无效的请求方式']);
            exit;
        }

        $id = isset($_POST['id']) ? intval($_POST['id']) : 0;
        $result = save_link($id);

        echo json_encode($result);
        exit;
        break;

    // 获取单个友情链接数据
    case 'get':
        header('Content-Type: application/json');

        $id = isset($_GET['id']) ? intval($_GET['id']) : 0;
        if ($id <= 0) {
            echo json_encode(['success' => false, 'error' => '无效的链接ID']);
            exit;
        }

        $sql = "SELECT * FROM links WHERE id = ?";
        $result = $db->query($sql, [$id]);
        $link_data = $db->fetch_array($result);

        if ($link_data) {
            echo json_encode(['success' => true, 'data' => $link_data]);
        } else {
            echo json_encode(['success' => false, 'error' => '友情链接不存在']);
        }
        exit;
        break;

    // 删除友情链接
    case 'delete':
        $id = isset($_GET['id']) ? intval($_GET['id']) : 0;
        if ($id > 0) {
            $sql = "DELETE FROM links WHERE id = ?";
            $result = $db->query($sql, [$id]);
            
            if ($result) {
                // 清除友情链接缓存
                clearFriendLinksCache();
                $message = '友情链接删除成功';
            } else {
                $message = '友情链接删除失败';
            }
        } else {
            $message = '无效的友情链接ID';
        }
        
        header("Location: links.php?message=" . urlencode($message));
        exit;
        break;
        
    // 批量删除
    case 'batch_delete':
        if ($_SERVER['REQUEST_METHOD'] === 'POST') {
            $link_ids = isset($_POST['link_ids']) ? $_POST['link_ids'] : [];
            
            if (!empty($link_ids)) {
                $ids = array_map('intval', $link_ids);
                $ids_str = implode(',', $ids);
                
                $sql = "DELETE FROM links WHERE id IN ({$ids_str})";
                $result = $db->query($sql);
                
                if ($result) {
                    // 清除友情链接缓存
                    clearFriendLinksCache();
                    $message = '批量删除成功，共删除 ' . count($ids) . ' 个友情链接';
                } else {
                    $message = '批量删除失败';
                }
            } else {
                $message = '请选择要删除的友情链接';
            }
        } else {
            $message = '无效的请求方式';
        }
        
        header("Location: links.php?message=" . urlencode($message));
        exit;
        break;
        
    // 更新状态
    case 'toggle_status':
        $id = isset($_GET['id']) ? intval($_GET['id']) : 0;
        if ($id > 0) {
            // 获取当前状态
            $sql = "SELECT status FROM links WHERE id = ?";
            $result = $db->query($sql, [$id]);
            $row = $db->fetch_array($result);
            
            if ($row) {
                $new_status = $row['status'] == 1 ? 0 : 1;
                $update_sql = "UPDATE links SET status = ? WHERE id = ?";
                $update_result = $db->query($update_sql, [$new_status, $id]);
                
                if ($update_result) {
                    // 清除友情链接缓存
                    clearFriendLinksCache();
                    $message = '状态更新成功';
                } else {
                    $message = '状态更新失败';
                }
            } else {
                $message = '友情链接不存在';
            }
        } else {
            $message = '无效的友情链接ID';
        }
        
        header("Location: links.php?message=" . urlencode($message));
        exit;
        break;
        
    default:
        header('Location: links.php?action=list');
        exit;
}

/**
 * 保存友情链接信息
 */
function save_link($id = 0) {
    global $db;
    
    // 获取表单数据
    $name = isset($_POST['name']) ? trim($_POST['name']) : '';
    $url = isset($_POST['url']) ? trim($_POST['url']) : '';
    $description = isset($_POST['description']) ? trim($_POST['description']) : '';
    $sort_order = isset($_POST['sort_order']) ? intval($_POST['sort_order']) : 0;
    $status = isset($_POST['status']) ? intval($_POST['status']) : 1;
    $target = isset($_POST['target']) ? trim($_POST['target']) : '_blank';
    
    // 验证必填字段
    if (empty($name)) {
        return ['success' => false, 'error' => '请输入链接名称'];
    }
    
    if (empty($url)) {
        return ['success' => false, 'error' => '请输入链接地址'];
    }
    
    // 验证URL格式
    if (!filter_var($url, FILTER_VALIDATE_URL)) {
        return ['success' => false, 'error' => '请输入有效的链接地址'];
    }
    
    // 检查链接名称是否已存在
    $check_sql = "SELECT id FROM links WHERE name = ?";
    if ($id > 0) {
        $check_sql .= " AND id != ?";
        $check_result = $db->query($check_sql, [$name, $id]);
    } else {
        $check_result = $db->query($check_sql, [$name]);
    }
    
    if ($db->num_rows($check_result) > 0) {
        return ['success' => false, 'error' => '链接名称已存在'];
    }
    
    // 开始事务
    $db->beginTransaction();
    
    try {
        $now = time();
        
        if ($id > 0) {
            // 更新现有友情链接
            $sql = "UPDATE links SET name = ?, url = ?, description = ?, sort_order = ?, status = ?, target = ?, updated_at = ? WHERE id = ?";
            $result = $db->query($sql, [$name, $url, $description, $sort_order, $status, $target, $now, $id]);
        } else {
            // 添加新友情链接
            $sql = "INSERT INTO links (name, url, description, sort_order, status, target, created_at, updated_at) VALUES (?, ?, ?, ?, ?, ?, ?, ?)";
            $result = $db->query($sql, [$name, $url, $description, $sort_order, $status, $target, $now, $now]);
        }
        
        if (!$result) {
            throw new Exception('保存友情链接失败');
        }
        
        // 提交事务
        $db->commit();

        // 清除友情链接缓存
        clearFriendLinksCache();

        return ['success' => true];
    } catch (Exception $e) {
        // 回滚事务
        $db->rollback();
        return ['success' => false, 'error' => $e->getMessage()];
    }
}
?>
