<?php
/**
 * MySQL Database Operation Class
 * Supports PHP 7.3-8.2
 * MySQL 数据库操作类
 * 支持 PHP 7.3-8.2
 */

if (!defined('IN_BTMPS')) {
    exit('Access Denied');
}

class MySQL
{
    /**
     * Database connection
     * 数据库连接
     * @var mysqli
     */
    private $conn;
    
    /**
     * Last query executed
     * 最后执行的查询
     * @var string
     */
    private $lastQuery;
    
    /**
     * Last statement
     * 最后的预处理语句
     * @var mysqli_stmt
     */
    private $statement;
    
    /**
     * Configuration parameters
     * 配置参数
     * @var array
     */
    private $config = [
        'host' => 'localhost',      // 数据库主机
        'username' => 'root',       // 数据库用户名
        'password' => '',           // 数据库密码
        'database' => '',           // 数据库名
        'charset' => 'utf8mb4',     // 字符集
        'port' => 3306,             // 端口
        'socket' => null,           // Socket
        'persistent' => false       // 是否使用持久连接
    ];

    /**
     * Constructor
     * 构造函数
     * 
     * @param array $config Database configuration 数据库配置
     */
    public function __construct(array $config = [])
    {
        // 映射配置键名
        $config['host'] = $config['db_host'];
        $config['username'] = $config['db_user'];
        $config['password'] = $config['db_pass'];
        $config['database'] = $config['db_name'];
        $config['charset'] = $config['db_charset'];
        $config['port'] = $config['db_port'];

        $this->config = array_merge($this->config, $config);
        $this->connect();
    }
    
    /**
     * Connect to database
     * 连接到数据库
     * 
     * @return bool|string Connection result or error message 连接结果或错误信息
     */
    public function connect()
    {
        $host = $this->config['host'];
        if ($this->config['persistent']) {
            $host = 'p:' . $host; // 使用持久连接
        }
        
        try {
            $this->conn = new \mysqli(
                $host,
                $this->config['username'],
                $this->config['password'],
                $this->config['database'],
                $this->config['port'],
                $this->config['socket']
            );
            
            if ($this->conn->connect_error) {
                $error = $this->conn->connect_error;
                $this->conn = null;
                
                // 返回友好的错误信息
                if (strpos($error, 'Access denied') !== false) {
                    return '数据库连接失败：用户名或密码错误，请检查配置文件中的数据库连接信息。';
                } else if (strpos($error, 'Unknown database') !== false) {
                    return '数据库连接失败：数据库不存在，请确认数据库名称是否正确或数据库是否已创建。';
                } else if (strpos($error, 'Connection refused') !== false) {
                    return '数据库连接失败：无法连接到数据库服务器，请确认数据库服务是否已启动。';
                } else {
                    return '数据库连接失败：' . $error . '。请检查数据库配置并确保数据库服务正常运行。';
                }
            }
            
            $this->conn->set_charset($this->config['charset']); // 设置字符集
            return true;
        } catch (\Exception $e) {
            return '数据库连接异常：' . $e->getMessage() . '。请检查数据库配置并确保数据库服务正常运行。';
        }
    }
    
    /**
     * Close database connection
     * 关闭数据库连接
     * 
     * @return bool
     */
    public function close()
    {
        if ($this->statement) {
            $this->statement->close(); // 关闭预处理语句
        }
        
        if ($this->conn) {
            return $this->conn->close(); // 关闭数据库连接
        }
        
        return true;
    }
    
    /**
     * Check if connection is alive
     * 检查连接是否正常
     * 
     * @return bool Connection status 连接状态
     */
    public function ping() {
        return ($this->conn && $this->conn->ping());
    }

    /**
     * Execute a query with prepared statements
     * 使用预处理语句执行查询
     * 
     * @param string $query SQL query SQL查询语句
     * @param array $params Parameters for prepared statement 预处理语句的参数
     * @return mixed Query result 查询结果
     */
    public function query($query, $params = [])
    {
        $this->lastQuery = $query;
        
        if (!$this->conn) {
            throw new \Exception('无法执行查询：数据库连接不可用');
        }
        
        if (empty($params)) {
            return $this->conn->query($query); // 没有参数，直接执行查询
        }
        
        $this->statement = $this->conn->prepare($query);
        
        if (!$this->statement) {
            throw new \Exception('Query preparation failed: ' . $this->conn->error); // 查询准备失败
        }
        
        if (!empty($params)) {
            $types = '';
            $bindParams = [];
            
            foreach ($params as $param) {
                if (is_int($param)) {
                    $types .= 'i'; // 整数
                } elseif (is_float($param)) {
                    $types .= 'd'; // 浮点数
                } elseif (is_string($param)) {
                    $types .= 's'; // 字符串
                } else {
                    $types .= 'b'; // 二进制
                }
                $bindParams[] = $param;
            }
            
            $bindParams = array_merge([$types], $bindParams);
            call_user_func_array([$this->statement, 'bind_param'], $this->refValues($bindParams)); // 绑定参数
        }
        
        $this->statement->execute(); // 执行查询
        
        if ($this->statement->errno) {
            throw new \Exception('Query execution failed: ' . $this->statement->error); // 查询执行失败
        }
        
        $result = $this->statement->get_result();
        
        return $result !== false ? $result : true;
    }
    
    /**
     * Execute an SQL query and fetch all results
     * 执行SQL查询并获取所有结果
     * 
     * @param string $query SQL query SQL查询语句
     * @param array $params Parameters for prepared statement 预处理语句的参数
     * @return array Results 结果数组
     */
    public function fetchAll(string $query, array $params = [])
    {
        $result = $this->query($query, $params);
        
        if (!$result || $result === true) {
            return [];
        }
        
        return $result->fetch_all(MYSQLI_ASSOC); // 以关联数组形式返回所有结果
    }
    
    /**
     * Execute an SQL query and fetch a single row
     * 执行SQL查询并获取单行结果
     * 
     * @param string $query SQL query SQL查询语句
     * @param array $params Parameters for prepared statement 预处理语句的参数
     * @return array|null Result row or null 结果行或空
     */
    public function fetchRow(string $query, array $params = [])
    {
        $result = $this->query($query, $params);
        
        if (!$result || $result === true) {
            return null;
        }
        
        $row = $result->fetch_assoc(); // 获取关联数组形式的单行
        return $row !== null ? $row : null;
    }
    
    /**
     * Fetch a row from a result as an associative array
     * 从结果集中获取一行作为关联数组
     * 
     * @param \mysqli_result $result Result to fetch from 结果集
     * @return array|null Array containing row data or null 包含行数据的数组或空
     */
    public function fetch_array($result)
    {
        if (!$result || !($result instanceof \mysqli_result)) {
            return null;
        }
        
        return $result->fetch_assoc();
    }
    
    /**
     * Get the number of rows in a result set
     * 获取结果集中的行数
     * 
     * @param \mysqli_result $result Result to count rows from 结果集
     * @return int Number of rows 行数
     */
    public function num_rows($result)
    {
        if (!$result || !($result instanceof \mysqli_result)) {
            return 0;
        }
        
        return $result->num_rows;
    }
    
    /**
     * Execute an SQL query and fetch a single column
     * 执行SQL查询并获取单个值
     * 
     * @param string $query SQL query SQL查询语句
     * @param array $params Parameters for prepared statement 预处理语句的参数
     * @return mixed|null Column value or null 列值或空
     */
    public function fetchOne(string $query, array $params = [])
    {
        $result = $this->query($query, $params);
        
        if (!$result || $result === true) {
            return null;
        }
        
        $row = $result->fetch_row(); // 获取索引数组形式的单行
        return isset($row[0]) ? $row[0] : null; // 返回第一列的值
    }
    
    /**
     * Get the number of affected rows
     * 获取受影响的行数
     * 
     * @return int Number of affected rows 受影响的行数
     */
    public function affectedRows()
    {
        return $this->statement ? $this->statement->affected_rows : $this->conn->affected_rows;
    }
    
    /**
     * Get the last insert ID
     * 获取最后插入的ID
     * 
     * @return int Last insert ID 最后插入的ID
     */
    public function lastInsertId()
    {
        return $this->conn->insert_id;
    }
    
    /**
     * Get the last insert ID (兼容方法)
     * 
     * @return int 最后插入的ID
     */
    public function insert_id()
    {
        return $this->lastInsertId();
    }
    
    /**
     * Begin a transaction
     * 开始事务
     * 
     * @return bool
     */
    public function beginTransaction()
    {
        return $this->conn->begin_transaction();
    }
    
    /**
     * Commit a transaction
     * 提交事务
     * 
     * @return bool
     */
    public function commit()
    {
        return $this->conn->commit();
    }
    
    /**
     * Rollback a transaction
     * 回滚事务
     * 
     * @return bool
     */
    public function rollback()
    {
        return $this->conn->rollback();
    }
    
    /**
     * Check if a transaction is currently active
     * 检查事务是否处于活动状态
     * 
     * @return bool True if a transaction is active, false otherwise
     */
    public function inTransaction()
    {
        // mysqli没有直接的inTransaction方法，所以我们通过检查autocommit状态来判断
        if ($this->conn) {
            return !$this->conn->autocommit;
        }
        return false;
    }
    
    /**
     * Escape a string
     * 转义字符串
     * 
     * @param string $value String to escape 需要转义的字符串
     * @return string Escaped string 转义后的字符串
     */
    public function escape($value)
    {
        return $this->conn->real_escape_string($value);
    }
    
    /**
     * Get the last query executed
     * 获取最后执行的查询
     * 
     * @return string Last query 最后的查询
     */
    public function getLastQuery()
    {
        return $this->lastQuery;
    }
    
    /**
     * Helper method to get references for bind_param
     * 辅助方法，为bind_param获取引用
     * 
     * @param array $arr Array to get references from 需要获取引用的数组
     * @return array Array with references 带引用的数组
     */
    private function refValues($arr)
    {
        if (strnatcmp(phpversion(), '5.3') >= 0) {
            $refs = [];
            foreach ($arr as $key => $value) {
                $refs[$key] = &$arr[$key]; // 创建引用
            }
            return $refs;
        }
        return $arr;
    }
    
    /**
     * Destructor
     * 析构函数
     */
    public function __destruct()
    {
        $this->close(); // 关闭数据库连接
    }

    /**
     * 获取结果中的一个字段值
     */
    public function get_value($sql) {
        return $this->fetchOne($sql);
    }
    
    /**
     * Get MySQL error message
     * 获取MySQL错误信息
     * @return string 错误信息
     */
    public function error() {
        return mysqli_error($this->conn);
    }
    
    /**
     * Get MySQL error number
     * 获取MySQL错误码
     * @return int 错误码
     */
    public function errno() {
        return mysqli_errno($this->conn);
    }
    
    /**
     * Insert data into a table
     * 向表中插入数据
     * 
     * @param string $table Table name 表名
     * @param array $data Data to insert 要插入的数据
     * @return int|bool Last insert ID or false on failure 最后插入的ID或失败时返回false
     */
    public function insert($table, $data) {
        $fields = array();
        $values = array();
        
        foreach ($data as $field => $value) {
            $fields[] = "`$field`";
            if (is_null($value)) {
                $values[] = "NULL";
            } elseif (is_int($value) || is_float($value)) {
                $values[] = "$value";
            } else {
                $values[] = "'" . $this->escape($value) . "'";
            }
        }
        
        $sql = "INSERT INTO `$table` (" . implode(', ', $fields) . ") VALUES (" . implode(', ', $values) . ")";
        
        if ($this->query($sql)) {
            return $this->lastInsertId();
        }
        
        return false;
    }
    
    /**
     * Update data in a table
     * 更新表中的数据
     * 
     * @param string $table Table name 表名
     * @param array $data Data to update 要更新的数据
     * @param string $where Where clause 条件语句
     * @return int|bool Number of affected rows or false on failure 受影响的行数或失败时返回false
     */
    public function update($table, $data, $where = '') {
        $set = array();
        
        foreach ($data as $field => $value) {
            if (is_null($value)) {
                $set[] = "`$field` = NULL";
            } elseif (is_int($value) || is_float($value)) {
                $set[] = "`$field` = $value";
            } else {
                $set[] = "`$field` = '" . $this->escape($value) . "'";
            }
        }
        
        $sql = "UPDATE `$table` SET " . implode(', ', $set);
        
        if (!empty($where)) {
            $sql .= " WHERE $where";
        }
        
        if ($this->query($sql)) {
            return $this->affectedRows();
        }
        
        return false;
    }
    
    /**
     * Delete data from a table
     * 从表中删除数据
     * 
     * @param string $table Table name 表名
     * @param string $where Where clause 条件语句
     * @return int|bool Number of affected rows or false on failure 受影响的行数或失败时返回false
     */
    public function delete($table, $where = '') {
        $sql = "DELETE FROM `$table`";
        
        if (!empty($where)) {
            $sql .= " WHERE $where";
        }
        
        if ($this->query($sql)) {
            return $this->affectedRows();
        }
        
        return false;
    }
    
    /**
     * Check if a table exists
     * 检查表是否存在
     * 
     * @param string $table Table name 表名
     * @return bool Whether the table exists 表是否存在
     */
    public function table_exists($table) {
        $result = $this->query("SHOW TABLES LIKE '$table'");
        return ($result && $result->num_rows > 0);
    }
    
    /**
     * Get one row from a query
     * 从查询中获取一行数据
     * 
     * @param string $sql SQL query SQL查询语句
     * @return array|null Row data or null 行数据或空
     */
    public function get_one($sql) {
        return $this->fetchRow($sql);
    }
    
    /**
     * Get database version
     * 获取数据库版本
     * 
     * @return string Database version 数据库版本
     */
    public function version() {
        return $this->conn->server_info;
    }
} 