<?php
define('IN_BTMPS', true);
require_once 'include/common.inc.php';

echo "检查系统写入信息时涉及的表...\n\n";

// 1. 检查主要的信息相关表
$tables = [
    'posts' => '信息主表',
    'post_contents' => '信息内容表',
    'post_count' => '分类统计表',
    'operation_logs' => '操作日志表'
];

foreach ($tables as $table => $desc) {
    echo "=== $desc ($table) ===\n";
    
    // 检查表是否存在
    $result = $db->query("SHOW TABLES LIKE '$table'");
    if (!$db->fetch_array($result)) {
        echo "  ✗ 表不存在\n\n";
        continue;
    }
    
    // 检查表结构
    $result = $db->query("DESCRIBE $table");
    echo "  表结构:\n";
    while ($row = $db->fetch_array($result)) {
        echo "    " . $row['Field'] . " - " . $row['Type'] . "\n";
    }
    
    // 检查记录数量
    $result = $db->query("SELECT COUNT(*) as total FROM $table");
    $count_row = $db->fetch_array($result);
    echo "  记录数量: " . $count_row['total'] . "\n\n";
}

// 2. 检查updateCategoryCount函数的实现
echo "=== 检查updateCategoryCount函数 ===\n";
echo "当前函数实现: 只返回true，没有实际更新数据库\n";
echo "需要修复: 应该更新post_count表中的统计数据\n\n";

// 3. 模拟信息发布流程
echo "=== 信息发布涉及的表操作 ===\n";
echo "根据代码分析，发布信息时会操作以下表:\n";
echo "1. posts表 - INSERT 信息基本数据\n";
echo "2. post_contents表 - INSERT 信息详细内容、联系人信息\n";
echo "3. operation_logs表 - INSERT 操作日志（如果启用）\n";
echo "4. post_count表 - UPDATE 分类统计（但当前函数未实现）\n\n";

// 4. 检查当前post_count表的数据
echo "=== 当前post_count表数据 ===\n";
$result = $db->query("SELECT category_id, post_count FROM post_count ORDER BY category_id LIMIT 10");
while ($row = $db->fetch_array($result)) {
    echo "  分类 {$row['category_id']}: {$row['post_count']} 条信息\n";
}

// 5. 检查实际posts表中的统计
echo "\n=== 实际posts表统计对比 ===\n";
$result = $db->query("SELECT category_id, COUNT(*) as actual_count FROM posts WHERE status = 1 GROUP BY category_id ORDER BY category_id LIMIT 10");
while ($row = $db->fetch_array($result)) {
    echo "  分类 {$row['category_id']}: {$row['actual_count']} 条信息（实际）\n";
}

echo "\n✓ 检查完成！\n";
?>
