<?php
/**
 * 后台通用函数库
 */

// 确保文件被合法调用
if (!defined('IN_BTMPS')) {
    die('Access Denied');
}

/**
 * 生成通用分页数据
 * 
 * @param int $total 总记录数
 * @param int $page 当前页码
 * @param int $perpage 每页记录数
 * @param string $url 分页URL前缀，后面会自动跟上page=x
 * @param array $params 附加参数，会添加到URL中
 * @param int $page_range 显示当前页码前后各多少个页码
 * @return array 返回分页数据数组
 */
function generate_pagination($total, $page, $perpage, $url, $params = [], $page_range = 5) {
    // 确保当前页码有效
    $page = max(1, min($page, ceil($total / $perpage) ?: 1));
    
    // 计算总页数
    $total_page = max(1, ceil($total / $perpage));
    
    // 构建分页URL参数
    $query_string = '';
    if (!empty($params)) {
        foreach ($params as $key => $value) {
            if ($key != 'page') { // 避免page参数重复
                $query_string .= '&' . $key . '=' . urlencode($value);
            }
        }
    }
    
    // 构建带参数的URL
    $page_url = $url;
    if (strpos($page_url, '?') === false) {
        $page_url .= '?';
    } else {
        $page_url .= '&';
    }
    
    // 当前页显示的记录范围
    $start = ($page - 1) * $perpage + 1;
    $end = min($page * $perpage, $total);
    
    // 计算需要显示的页码范围
    $start_page = max(1, $page - $page_range);
    $end_page = min($total_page, $page + $page_range);
    
    // 构建分页链接数组
    $page_links = [];
    for ($i = $start_page; $i <= $end_page; $i++) {
        $page_links[$i] = $page_url . 'page=' . $i . $query_string;
    }
    
    // 返回分页数据
    return [
        'total' => $total,                                         // 总记录数
        'perpage' => $perpage,                                     // 每页记录数
        'page' => $page,                                           // 当前页码
        'total_page' => $total_page,                               // 总页数
        'start' => $start,                                         // 当前页开始记录
        'end' => $end,                                             // 当前页结束记录
        'page_links' => $page_links,                               // 页码链接
        'first_link' => $page_url . 'page=1' . $query_string,      // 首页链接
        'last_link' => $page_url . 'page=' . $total_page . $query_string, // 末页链接
        'prev_link' => $page > 1 ? $page_url . 'page=' . ($page - 1) . $query_string : '', // 上一页链接
        'next_link' => $page < $total_page ? $page_url . 'page=' . ($page + 1) . $query_string : '', // 下一页链接
        'has_prev' => $page > 1,                                   // 是否有上一页
        'has_next' => $page < $total_page,                         // 是否有下一页
    ];
}

/**
 * 生成分页HTML代码
 * 
 * @param array $pagination generate_pagination函数返回的分页数据
 * @param bool $simple 是否使用简单分页样式
 * @return string 分页HTML代码
 */
function pagination_html($pagination, $simple = false) {
    $html = '<ul class="pagination">';
    
    // 首页
    if ($pagination['page'] > 1) {
        $html .= '<li><a href="' . $pagination['first_link'] . '">首页</a></li>';
    } else {
        $html .= '<li class="disabled"><span>首页</span></li>';
    }
    
    // 上一页
    if ($pagination['has_prev']) {
        $html .= '<li><a href="' . $pagination['prev_link'] . '">上一页</a></li>';
    } else {
        $html .= '<li class="disabled"><span>上一页</span></li>';
    }
    
    // 页码
    foreach ($pagination['page_links'] as $page => $link) {
        if ($page == $pagination['page']) {
            $html .= '<li class="active"><span>' . $page . '</span></li>';
        } else {
            $html .= '<li><a href="' . $link . '">' . $page . '</a></li>';
        }
    }
    
    // 下一页
    if ($pagination['has_next']) {
        $html .= '<li><a href="' . $pagination['next_link'] . '">下一页</a></li>';
    } else {
        $html .= '<li class="disabled"><span>下一页</span></li>';
    }
    
    // 末页
    if ($pagination['page'] < $pagination['total_page']) {
        $html .= '<li><a href="' . $pagination['last_link'] . '">末页</a></li>';
    } else {
        $html .= '<li class="disabled"><span>末页</span></li>';
    }
    
    $html .= '</ul>';
    
    return $html;
}

/**
 * 显示消息并跳转
 * 
 * @param string $message 消息内容
 * @param string $url 跳转地址
 * @param int $wait 等待时间（秒）
 */
function show_message($message, $url = '', $wait = 3) {
    global $tpl;
    
    if (empty($url)) {
        $url = 'javascript:history.back(-1);';
    }
    
    $tpl->assign('message', $message);
    $tpl->assign('url', $url);
    $tpl->assign('wait', $wait);
    $tpl->display('message.htm');
    
    exit;
}

/**
 * 设置后台公共模板变量
 * 统一设置所有后台页面需要的公共变量，包括admin用户信息
 *
 * @param Template $tpl 模板对象
 * @param string $current_page 当前页面标识
 * @param string $page_title 页面标题
 * @param string $breadcrumb 面包屑导航
 * @param string $message 消息提示
 * @param string $error 错误提示
 */
function set_admin_template_vars($tpl, $current_page = '', $page_title = '', $breadcrumb = '', $message = '', $error = '') {
    // 设置管理员信息
    if (isset($_SESSION['admin'])) {
        $tpl->assign('admin', $_SESSION['admin']);
    }

    // 设置页面基本信息
    if (!empty($current_page)) {
        $tpl->assign('current_page', $current_page);
    }

    if (!empty($page_title)) {
        $tpl->assign('page_title', $page_title);
    }

    if (!empty($breadcrumb)) {
        $tpl->assign('breadcrumb', $breadcrumb);
    }

    if (!empty($message)) {
        $tpl->assign('message', $message);
    }

    if (!empty($error)) {
        $tpl->assign('error', $error);
    }
}
