<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>图片预览切换箭头测试</title>
    <link rel="stylesheet" href="css/view.css">
    <style>
        body { margin: 0; padding: 20px; font-family: Arial, sans-serif; }
        .test-container { max-width: 1200px; margin: 0 auto; }
        .test-title { text-align: center; margin-bottom: 30px; color: #333; }
        .test-description { background: #f5f5f5; padding: 15px; border-radius: 5px; margin-bottom: 30px; }
        .test-description h3 { margin-top: 0; color: #2e8ded; }
        .test-gallery { display: flex; flex-wrap: wrap; gap: 15px; justify-content: center; }
        .test-image { width: 200px; height: 150px; object-fit: cover; cursor: pointer; border-radius: 5px; box-shadow: 0 2px 5px rgba(0,0,0,0.1); transition: transform 0.3s ease; }
        .test-image:hover { transform: scale(1.05); }
        .size-label { text-align: center; margin-top: 5px; font-size: 12px; color: #666; }
    </style>
</head>
<body>
    <div class="test-container">
        <h1 class="test-title">PC端图片预览切换箭头位置优化测试</h1>
        
        <div class="test-description">
            <h3>测试说明</h3>
            <p><strong>问题：</strong>原来的切换箭头位置会根据图片大小变化，用户体验不佳。</p>
            <p><strong>解决方案：</strong>将切换箭头固定在视窗两侧，不随图片大小变化位置。</p>
            <p><strong>优化功能：</strong>只有一张图片时自动隐藏切换箭头。</p>
            <p><strong>测试方法：</strong>点击下面不同尺寸的图片，观察切换箭头位置是否保持固定。点击单张图片测试时应该不显示切换箭头。</p>
        </div>

        <h3 style="margin-top: 30px; color: #2e8ded;">多图片测试（有切换箭头）</h3>
        <div class="test-gallery">
            <div>
                <img class="test-image gallery-item" src="https://picsum.photos/400/300" data-original="https://picsum.photos/400/300" alt="小图片">
                <div class="size-label">小图片 (400x300)</div>
            </div>
            <div>
                <img class="test-image gallery-item" src="https://picsum.photos/800/600" data-original="https://picsum.photos/800/600" alt="中等图片">
                <div class="size-label">中等图片 (800x600)</div>
            </div>
            <div>
                <img class="test-image gallery-item" src="https://picsum.photos/1200/900" data-original="https://picsum.photos/1200/900" alt="大图片">
                <div class="size-label">大图片 (1200x900)</div>
            </div>
            <div>
                <img class="test-image gallery-item" src="https://picsum.photos/300/800" data-original="https://picsum.photos/300/800" alt="竖图">
                <div class="size-label">竖图 (300x800)</div>
            </div>
            <div>
                <img class="test-image gallery-item" src="https://picsum.photos/1000/400" data-original="https://picsum.photos/1000/400" alt="横图">
                <div class="size-label">横图 (1000x400)</div>
            </div>
        </div>

        <h3 style="margin-top: 30px; color: #2e8ded;">单图片测试（无切换箭头）</h3>
        <div class="test-gallery">
            <div>
                <img class="test-image single-gallery-item" src="https://picsum.photos/600/400" data-original="https://picsum.photos/600/400" alt="单张测试图片">
                <div class="size-label">单张图片 (600x400)</div>
            </div>
        </div>
    </div>

    <!-- 图片灯箱效果 -->
    <div id="image-lightbox" class="lightbox-overlay" style="display: none;">
        <div class="lightbox-container">
            <span class="lightbox-close" onclick="closeLightbox()">&times;</span>
            <img id="lightbox-img" class="lightbox-content" src="" alt="灯箱图片">
            <div class="lightbox-caption" id="lightbox-caption"></div>
        </div>
        <!-- 切换箭头移到overlay层，固定在视窗两侧 -->
        <a class="lightbox-prev" onclick="changeImage(-1)">&#10094;</a>
        <a class="lightbox-next" onclick="changeImage(1)">&#10095;</a>
    </div>

    <script src="js/jquery.min.js"></script>
    <script>
        // 图片灯箱相关逻辑
        $(document).ready(function() {
            // 图片灯箱相关变量
            var lightbox = document.getElementById('image-lightbox');
            var lightboxContainer = lightbox.querySelector('.lightbox-container');
            var lightboxImg = document.getElementById('lightbox-img');
            var lightboxCaption = document.getElementById('lightbox-caption');
            var currentImageIndex = 0;
            var galleryImages = [];
            
            // 公开函数，使其可从HTML调用
            window.openLightbox = function(imgElement) {
                // 获取原图路径
                var originalSrc = imgElement.getAttribute('data-original');

                // 根据点击的图片类型收集相应的图库图片
                if (imgElement.classList.contains('single-gallery-item')) {
                    // 单张图片
                    galleryImages = $('.single-gallery-item').get();
                } else {
                    // 多张图片
                    galleryImages = $('.gallery-item').get();
                }

                // 找到当前图片索引
                for (var i = 0; i < galleryImages.length; i++) {
                    if (galleryImages[i] === imgElement) {
                        currentImageIndex = i;
                        break;
                    }
                }

                // 设置图片源
                lightboxImg.src = originalSrc;

                // 添加页面计数
                updateCaption();

                // 根据图片数量决定是否显示切换箭头
                if (galleryImages.length <= 1) {
                    lightbox.classList.add('single-image');
                } else {
                    lightbox.classList.remove('single-image');
                }

                // 显示灯箱并禁止页面滚动
                lightbox.style.display = 'flex';
                document.body.style.overflow = 'hidden';

                // 添加动画效果
                setTimeout(function() {
                    lightboxContainer.style.opacity = '1';
                }, 10);
            };
            
            // 关闭灯箱
            window.closeLightbox = function() {
                lightbox.style.display = 'none';
                document.body.style.overflow = '';
            };
            
            // 更新说明文字
            function updateCaption() {
                var imgAlt = $(galleryImages[currentImageIndex]).attr('alt');
                lightboxCaption.innerHTML = imgAlt + " - 图片 " + (currentImageIndex + 1) + " / " + galleryImages.length;
            }
            
            // 切换图片
            window.changeImage = function(step) {
                currentImageIndex += step;
                
                // 循环浏览
                if (currentImageIndex >= galleryImages.length) {
                    currentImageIndex = 0;
                } else if (currentImageIndex < 0) {
                    currentImageIndex = galleryImages.length - 1;
                }
                
                // 更新图片
                var nextImageSrc = $(galleryImages[currentImageIndex]).attr('data-original');
                lightboxImg.src = nextImageSrc;
                updateCaption();
            };
            
            // 使用jQuery绑定事件
            $('.gallery-item, .single-gallery-item').on('click', function(e) {
                e.preventDefault();
                openLightbox(this);
                return false;
            });
            
            // 点击灯箱背景时关闭
            $(lightbox).click(function(e) {
                // 如果点击的是灯箱背景而不是内容容器，则关闭灯箱
                if (e.target === lightbox) {
                    closeLightbox();
                }
            });
            
            // 阻止点击容器时事件冒泡到背景
            $(lightboxContainer).click(function(e) {
                e.stopPropagation();
            });
            
            // 键盘导航
            $(document).keydown(function(e) {
                if (lightbox.style.display === 'flex') {
                    if (e.keyCode === 37) {
                        changeImage(-1);
                    } else if (e.keyCode === 39) {
                        changeImage(1);
                    } else if (e.keyCode === 27) {
                        closeLightbox();
                    }
                }
            });
            
            // 确保关闭按钮工作
            $('.lightbox-close').click(function(e) {
                e.preventDefault();
                closeLightbox();
                return false;
            });
        });
    </script>
</body>
</html>
