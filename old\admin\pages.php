<?php
// 定义安全常量
define('IN_BTMPS', true);
/**
 * 单页管理页面
 */
// 引入公共文件
require_once('../include/common.inc.php');

// 检查管理员是否登录
if (!isset($_SESSION['admin']) || $_SESSION['admin']['is_login'] !== true) {
    header("Location: login.php");
    exit;
}

// 当前页面
$current_page = 'pages';

// 操作类型
$action = isset($_GET['action']) ? $_GET['action'] : 'list';

// 信息提示
$message = '';
$error = '';

// 修改模板目录为admin/template目录
$tpl->setTemplateDir(dirname(__FILE__) . '/template/');

// 引入后台公共函数
require_once(dirname(__FILE__) . '/include/admin.fun.php');

// 处理POST请求
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    switch ($action) {
        case 'add':
        case 'edit':
            $result = save_page();
            if ($result['success']) {
                $message = $result['message'];
                // 重定向到列表页面
                header("Location: pages.php?message=" . urlencode($message));
                exit;
            } else {
                $error = $result['error'];
            }
            break;
            
        case 'delete':
            $result = delete_page();
            if ($result['success']) {
                $message = $result['message'];
            } else {
                $error = $result['error'];
            }
            // 重定向到列表页面
            header("Location: pages.php?message=" . urlencode($message) . "&error=" . urlencode($error));
            exit;
            break;
            
        case 'batch_delete':
            $result = batch_delete_pages();
            if ($result['success']) {
                $message = $result['message'];
            } else {
                $error = $result['error'];
            }
            // 重定向到列表页面
            header("Location: pages.php?message=" . urlencode($message) . "&error=" . urlencode($error));
            exit;
            break;
    }
}

// 处理GET请求
switch ($action) {
    case 'list':
        // 列表逻辑已移到default case中
        $action = 'list';
        goto list_logic;
        
    case 'add':
        // 添加页面，不需要额外处理
        break;
        
    case 'edit':
        $id = isset($_GET['id']) ? intval($_GET['id']) : 0;
        if ($id <= 0) {
            $error = '无效的页面ID';
            $action = 'list';
            // 重新执行列表逻辑
            goto list_logic;
        }

        // 获取页面信息
        $sql = "SELECT * FROM pages WHERE id = ?";
        $result = $db->query($sql, array($id));
        $page_info = $db->fetch_array($result);

        if (!$page_info) {
            $error = '页面不存在';
            $action = 'list';
            // 重新执行列表逻辑
            goto list_logic;
        }

        $tpl->assign('page_info', $page_info);
        break;

    case 'delete':
        $id = isset($_GET['id']) ? intval($_GET['id']) : 0;
        if ($id <= 0) {
            $error = '无效的页面ID';
            $action = 'list';
            // 重新执行列表逻辑
            goto list_logic;
        }

        // 获取页面信息用于确认
        $sql = "SELECT * FROM pages WHERE id = ?";
        $result = $db->query($sql, array($id));
        $page_info = $db->fetch_array($result);

        if (!$page_info) {
            $error = '页面不存在';
            $action = 'list';
            // 重新执行列表逻辑
            goto list_logic;
        }

        $tpl->assign('page_info', $page_info);
        break;

    default:
        $action = 'list';
        // 执行列表逻辑
        list_logic:
        // 获取分页参数
        $page = isset($_GET['page']) ? intval($_GET['page']) : 1;
        $per_page = 20;
        $offset = ($page - 1) * $per_page;

        // 获取搜索参数
        $keyword = isset($_GET['keyword']) ? trim($_GET['keyword']) : '';
        $status = isset($_GET['status']) ? intval($_GET['status']) : -1;

        // 构建查询条件
        $where_conditions = array();
        $params = array();

        if (!empty($keyword)) {
            $where_conditions[] = "(title LIKE ? OR path LIKE ?)";
            $params[] = "%{$keyword}%";
            $params[] = "%{$keyword}%";
        }

        if ($status >= 0) {
            $where_conditions[] = "status = ?";
            $params[] = $status;
        }

        $where_clause = '';
        if (!empty($where_conditions)) {
            $where_clause = 'WHERE ' . implode(' AND ', $where_conditions);
        }

        // 获取总数
        $count_sql = "SELECT COUNT(*) as total FROM pages {$where_clause}";
        $count_result = $db->query($count_sql, $params);
        $total = $db->fetch_array($count_result)['total'];

        // 获取列表数据
        $sql = "SELECT * FROM pages {$where_clause} ORDER BY sort_order ASC, id DESC LIMIT {$offset}, {$per_page}";
        $result = $db->query($sql, $params);
        $pages = array();
        while ($row = $db->fetch_array($result)) {
            $pages[] = $row;
        }

        // 计算分页信息
        $total_pages = ceil($total / $per_page);

        // 分配变量到模板
        $tpl->assign('pages', $pages);
        $tpl->assign('total', $total);
        $tpl->assign('page', $page);
        $tpl->assign('per_page', $per_page);
        $tpl->assign('total_pages', $total_pages);
        $tpl->assign('keyword', $keyword);
        $tpl->assign('status', $status);
        break;
}

// 接收URL传递的消息
if (isset($_GET['message']) && empty($message)) {
    $message = $_GET['message'];
}

if (isset($_GET['error']) && empty($error)) {
    $error = $_GET['error'];
}

// 传递数据到模板
$tpl->assign('current_page', $current_page);
$tpl->assign('breadcrumb', '单页管理');
$tpl->assign('message', $message);
$tpl->assign('error', $error);
$tpl->assign('action', $action);
$tpl->assign('page_title', '单页管理');
$tpl->assign('admin', $_SESSION['admin']);

// 根据不同操作分配特定数据
// 注意：模板显示在文件末尾统一处理

/**
 * 保存页面信息
 */
function save_page() {
    global $db;
    
    // 获取表单数据
    $id = isset($_POST['id']) ? intval($_POST['id']) : 0;
    $title = isset($_POST['title']) ? trim($_POST['title']) : '';
    $path = isset($_POST['path']) ? trim($_POST['path']) : '';
    // UEditor内容不需要HTML编码，保持原始HTML格式
    $content = isset($_POST['content']) ? $_POST['content'] : '';
    $meta_keywords = isset($_POST['meta_keywords']) ? trim($_POST['meta_keywords']) : '';
    $meta_description = isset($_POST['meta_description']) ? trim($_POST['meta_description']) : '';
    $status = isset($_POST['status']) ? intval($_POST['status']) : 1;
    $sort_order = isset($_POST['sort_order']) ? intval($_POST['sort_order']) : 0;

    // 验证必填字段
    if (empty($title)) {
        return array('success' => false, 'error' => '请输入页面标题');
    }

    if (empty($path)) {
        return array('success' => false, 'error' => '请输入页面路径');
    }

    // 验证路径格式（伪静态路径，不需要.html后缀）
    if (!preg_match('/^[a-zA-Z0-9\/\-_]+$/', $path)) {
        return array('success' => false, 'error' => '页面路径格式不正确，只能包含字母、数字、斜杠、横线和下划线');
    }

    // 自动生成URL（用于显示）
    $url = '/page/' . $path . '.html';
    
    $now = time();
    
    try {
        if ($id > 0) {
            // 更新页面
            // 检查路径是否已存在（排除当前记录）
            $check_sql = "SELECT id FROM pages WHERE path = ? AND id != ?";
            $check_result = $db->query($check_sql, array($path, $id));
            if ($db->fetch_array($check_result)) {
                return array('success' => false, 'error' => '页面路径已存在');
            }

            $sql = "UPDATE pages SET title = ?, path = ?, url = ?, content = ?, meta_keywords = ?, meta_description = ?, status = ?, sort_order = ?, updated_at = ? WHERE id = ?";
            $result = $db->query($sql, array($title, $path, $url, $content, $meta_keywords, $meta_description, $status, $sort_order, $now, $id));
            
            if ($result) {
                return array('success' => true, 'message' => '页面更新成功');
            } else {
                return array('success' => false, 'error' => '页面更新失败');
            }
        } else {
            // 添加页面
            // 检查路径是否已存在
            $check_sql = "SELECT id FROM pages WHERE path = ?";
            $check_result = $db->query($check_sql, array($path));
            if ($db->fetch_array($check_result)) {
                return array('success' => false, 'error' => '页面路径已存在');
            }

            $sql = "INSERT INTO pages (title, path, url, content, meta_keywords, meta_description, status, sort_order, created_at, updated_at) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";
            $result = $db->query($sql, array($title, $path, $url, $content, $meta_keywords, $meta_description, $status, $sort_order, $now, $now));
            
            if ($result) {
                return array('success' => true, 'message' => '页面添加成功');
            } else {
                return array('success' => false, 'error' => '页面添加失败');
            }
        }
    } catch (Exception $e) {
        return array('success' => false, 'error' => '操作失败：' . $e->getMessage());
    }
}

/**
 * 删除页面
 */
function delete_page() {
    global $db;

    $id = isset($_POST['id']) ? intval($_POST['id']) : 0;
    if ($id <= 0) {
        return array('success' => false, 'error' => '无效的页面ID');
    }

    try {
        // 获取页面信息
        $sql = "SELECT * FROM pages WHERE id = ?";
        $result = $db->query($sql, array($id));
        $page_info = $db->fetch_array($result);

        if (!$page_info) {
            return array('success' => false, 'error' => '页面不存在');
        }

        // 删除数据库记录
        $delete_sql = "DELETE FROM pages WHERE id = ?";
        $delete_result = $db->query($delete_sql, array($id));

        if ($delete_result) {
            return array('success' => true, 'message' => '页面删除成功');
        } else {
            return array('success' => false, 'error' => '页面删除失败');
        }
    } catch (Exception $e) {
        return array('success' => false, 'error' => '删除失败：' . $e->getMessage());
    }
}

/**
 * 批量删除页面
 */
function batch_delete_pages() {
    global $db;

    $ids = isset($_POST['ids']) ? $_POST['ids'] : array();
    if (empty($ids) || !is_array($ids)) {
        return array('success' => false, 'error' => '请选择要删除的页面');
    }

    $ids = array_map('intval', $ids);
    $ids = array_filter($ids, function($id) { return $id > 0; });

    if (empty($ids)) {
        return array('success' => false, 'error' => '无效的页面ID');
    }

    try {
        // 获取要删除的页面信息
        $placeholders = str_repeat('?,', count($ids) - 1) . '?';
        $sql = "SELECT * FROM pages WHERE id IN ({$placeholders})";
        $result = $db->query($sql, $ids);
        $pages = array();
        while ($row = $db->fetch_array($result)) {
            $pages[] = $row;
        }

        // 删除数据库记录
        $delete_sql = "DELETE FROM pages WHERE id IN ({$placeholders})";
        $delete_result = $db->query($delete_sql, $ids);

        if ($delete_result) {
            $count = count($pages);
            return array('success' => true, 'message' => "成功删除 {$count} 个页面");
        } else {
            return array('success' => false, 'error' => '批量删除失败');
        }
    } catch (Exception $e) {
        return array('success' => false, 'error' => '批量删除失败：' . $e->getMessage());
    }
}

// 伪静态模式，不再需要HTML文件生成功能

// 分配公共变量
$tpl->assign('current_page', $current_page);
$tpl->assign('action', $action);
$tpl->assign('message', $message);
$tpl->assign('error', $error);

// 显示模板
switch ($action) {
    case 'add':
        $tpl->display('pages_edit.htm');
        break;
    case 'edit':
        $tpl->display('pages_edit.htm');
        break;
    case 'delete':
        $tpl->display('pages_delete.htm');
        break;
    default:
        $tpl->display('pages_list.htm');
        break;
}
?>
