<?php
/**
 * 操作日志管理
 */

define('IN_BTMPS', true);
require_once '../include/common.inc.php';
require_once '../include/OperationLogger.class.php';

// 引入后台通用函数
require_once dirname(__FILE__) . '/include/admin.fun.php';

// 检查管理员是否登录
if (!isset($_SESSION['admin']) || $_SESSION['admin']['is_login'] !== true) {
    header('Location: login.php');
    exit;
}

// 初始化日志记录器
$logger = new OperationLogger($db, $config);

// 处理操作
$op = $_GET['op'] ?? 'list';
$message = '';
$error = '';

switch ($op) {
    case 'delete':
        // 删除单个日志
        $id = intval($_GET['id'] ?? 0);
        if ($id > 0) {
            $result = $logger->deleteLogs([$id]);
            if ($result) {
                $message = '日志删除成功';
            } else {
                $error = '日志删除失败';
            }
        }
        break;
        
    case 'batch_delete':
        // 批量删除日志
        $ids = $_POST['ids'] ?? [];
        if (!empty($ids) && is_array($ids)) {
            $ids = array_map('intval', $ids);
            $ids = array_filter($ids, function($id) { return $id > 0; });
            
            if (!empty($ids)) {
                $result = $logger->deleteLogs($ids);
                if ($result) {
                    $message = '批量删除成功，共删除 ' . count($ids) . ' 条记录';
                } else {
                    $error = '批量删除失败';
                }
            }
        }
        break;
        
    case 'clean':
        // 清理旧日志
        $days = intval($_POST['days'] ?? 90);
        $days = max(7, min(365, $days));
        
        $affected_rows = $logger->cleanOldLogs($days);
        if ($affected_rows !== false) {
            $message = "清理完成，共删除 {$affected_rows} 条超过 {$days} 天的日志记录";
        } else {
            $error = '清理失败';
        }
        break;
        
    case 'export':
        // 导出日志（简单的CSV格式）
        $params = [
            'operation_type' => $_GET['operation_type'] ?? '',
            'target_type' => $_GET['target_type'] ?? '',
            'start_time' => !empty($_GET['start_date']) ? strtotime($_GET['start_date']) : '',
            'end_time' => !empty($_GET['end_date']) ? strtotime($_GET['end_date'] . ' 23:59:59') : '',
            'page_size' => 1000
        ];
        
        $logs_data = $logger->getLogs($params);
        
        header('Content-Type: text/csv; charset=utf-8');
        header('Content-Disposition: attachment; filename="operation_logs_' . date('Y-m-d') . '.csv"');
        
        echo "\xEF\xBB\xBF"; // UTF-8 BOM
        echo "ID,用户ID,用户名,操作类型,目标类型,目标ID,目标标题,操作描述,IP地址,端口,状态,创建时间\n";
        
        foreach ($logs_data['data'] as $log) {
            echo sprintf('"%s","%s","%s","%s","%s","%s","%s","%s","%s","%s","%s","%s"' . "\n",
                   $log['id'],
                   $log['user_id'],
                   $log['username'],
                   $log['operation_type'],
                   $log['target_type'],
                   $log['target_id'],
                   str_replace('"', '""', $log['target_title']),
                   str_replace('"', '""', $log['operation_desc']),
                   $log['ip_address'],
                   $log['port'],
                   $log['status'] ? '成功' : '失败',
                   date('Y-m-d H:i:s', $log['created_at'])
            );
        }
        exit;
}

// 获取筛选参数
$filter_params = [
    'operation_type' => $_GET['operation_type'] ?? '',
    'target_type' => $_GET['target_type'] ?? '',
    'ip_address' => $_GET['ip_address'] ?? '',
    'keyword' => $_GET['keyword'] ?? '',
    'start_time' => !empty($_GET['start_date']) ? strtotime($_GET['start_date']) : '',
    'end_time' => !empty($_GET['end_date']) ? strtotime($_GET['end_date'] . ' 23:59:59') : '',
    'page' => intval($_GET['page'] ?? 1),
    'page_size' => 20
];

// 获取日志数据
$logs_data = $logger->getLogs($filter_params);

// 处理日志数据
if (!empty($logs_data['data'])) {
    foreach ($logs_data['data'] as &$log) {
        // 截断描述
        if (strlen($log['operation_desc']) > 50) {
            $log['operation_desc_short'] = mb_substr($log['operation_desc'], 0, 50, 'UTF-8') . '...';
        } else {
            $log['operation_desc_short'] = $log['operation_desc'];
        }

        // 格式化时间
        $log['formatted_time'] = date('m-d H:i', $log['created_at']);
    }
    unset($log);
}

// 生成标准分页数据
$pagination_params = [];
foreach (['operation_type', 'target_type', 'ip_address', 'keyword', 'start_date', 'end_date'] as $param) {
    if (!empty($_GET[$param])) {
        $pagination_params[$param] = $_GET[$param];
    }
}

$pagination = generate_pagination($logs_data['total'], $logs_data['page'], $logs_data['page_size'], 'operation_logs.php', $pagination_params);

// 生成分页HTML代码
$pagination_html = pagination_html($pagination, true);

// 获取今日统计
$today_start = strtotime(date('Y-m-d'));
$today_stats_sql = "
SELECT 
    COUNT(*) as total_today,
    COUNT(DISTINCT ip_address) as unique_ips_today,
    COUNT(DISTINCT user_id) as active_users_today
FROM operation_logs 
WHERE created_at >= {$today_start}";

$today_result = $db->query($today_stats_sql);
$today_stats = $db->fetch_array($today_result);

if (!$today_stats) {
    $today_stats = [
        'total_today' => 0,
        'unique_ips_today' => 0,
        'active_users_today' => 0
    ];
}

// 构建URL参数
$url_params = '';
foreach (['operation_type', 'target_type', 'ip_address', 'keyword', 'start_date', 'end_date'] as $param) {
    if (!empty($_GET[$param])) {
        $url_params .= '&' . $param . '=' . urlencode($_GET[$param]);
    }
}

// 设置模板目录
$tpl->setTemplateDir(dirname(__FILE__) . '/template/');

// 设置模板变量
$tpl->assign('page_title', '操作日志管理');
$tpl->assign('current_page', 'operation_logs');
$tpl->assign('breadcrumb', '操作日志管理');
$tpl->assign('admin', $_SESSION['admin']);

// 传递数据到模板
$tpl->assign('message', $message);
$tpl->assign('error', $error);
$tpl->assign('today_stats', $today_stats);
$tpl->assign('logs_data', $logs_data);
$tpl->assign('url_params', $url_params);
$tpl->assign('filter_params', $_GET);
$tpl->assign('pagination', $pagination);
$tpl->assign('pagination_html', $pagination_html);

// 显示模板
$tpl->display('operation_logs.htm');
?>

