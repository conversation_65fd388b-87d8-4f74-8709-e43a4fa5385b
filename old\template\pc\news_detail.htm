<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>{$news.title} - {$site_name}</title>
    <meta name="keywords" content="{if !empty($news.keywords)}{$news.keywords}{else}{$news.title},{$site_name},新闻资讯{/if}" />
    <meta name="description" content="{if !empty($news.description)}{$news.description}{else}{$news.title} - {$site_name}新闻资讯{/if}" />
    <link rel="stylesheet" href="/template/pc/css/common.css?<?php echo time(); ?>">
    <link rel="stylesheet" href="/template/pc/css/news.css?<?php echo time(); ?>">
    <link rel="stylesheet" href="/static/css/content-responsive.css?<?php echo time(); ?>">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css">
    <script type="text/javascript" src="/template/pc/js/m.js"></script>
</head>
<body>
    {include file="header.htm"}
    <div class="yui-clear"></div>
    
    <!-- 主内容区域 -->
    <div class="yui-content yui-1200">
        <!-- 面包屑导航 -->
        <div class="breadcrumb-container">
            <div class="breadcrumb">
                <a href="/">首页</a>
                <span class="separator">></span>
                <a href="/news/">新闻中心</a>
                <span class="separator">></span>
                <a href="/news/{$news.pinyin}/">{$news.catname}</a>
                <span class="separator">></span>
                <span class="current">正文</span>
            </div>
        </div>

        <!-- 新闻详情内容区域 - 通栏显示 -->
        <div class="news-detail-container">
            <!-- 新闻详情主体 -->
            <div class="news-detail-main">
                <!-- 新闻标题 -->
                <div class="news-detail-header">
                    <h1 class="news-detail-title">{$news.title}</h1>
                    <div class="news-detail-meta">
                        <span><i class="icon-time"></i></span>
                        {if $news.author}<span><i class="icon-author"></i>作者: {$news.author}</span>{/if}
                        {if $news.catname}<span><i class="icon-cat"></i>栏目: {$news.catname}</span>{/if}
                        <span><i class="icon-views"></i>浏览: {$news.click}</span>
                        <span class="news-share"><i class="icon-share"></i>分享</span>
                    </div>
                </div>

                <!-- 新闻摘要 -->
                <div class="news-detail-summary">
                    {if $news.description}
                    <p>{$news.description}</p>
                    {/if}
                </div>

                <!-- 新闻正文 -->
                <div class="news-detail-content">
                    <?php echo $news['content']; ?>
                </div>

                <!-- 分享和点赞 -->
                <div class="news-detail-interaction">
                    <div class="interaction-item like">
                        <i class="icon-like"></i>
                        <span>点赞</span>
                        <em>{$news.click}</em>
                    </div>
                    <div class="interaction-item collect">
                        <i class="icon-collect"></i>
                        <span>收藏</span>
                        <em>{$news.click}</em>
                    </div>
                </div>

                <!-- 上一篇下一篇 -->
                <div class="news-detail-nav">
                    <div class="prev-next">
                        <div class="prev">
                            <span>上一篇：</span>
                            {if $prev_news}
                            <a href="/news/{$prev_news.id}.html">{$prev_news.title}</a>
                            {else}
                            <span style="color:#999;">没有了</span>
                            {/if}
                        </div>
                        <div class="next">
                            <span>下一篇：</span>
                            {if $next_news}
                            <a href="/news/{$next_news.id}.html">{$next_news.title}</a>
                            {else}
                            <span style="color:#999;">没有了</span>
                            {/if}
                        </div>
                    </div>
                </div>

                <!-- 相关推荐 -->
                <div class="news-detail-related">
                    <h3 class="related-title"><span>相关推荐</span></h3>
                    <ul class="related-list">
                        {if $related_news}
                        {loop $related_news $news_item}
                        <li><a href="/news/{$news_item.id}.html">{$news_item.title}</a></li>
                        {/loop}
                        {else}
                        <li><span style="color:#999;">暂无相关文章</span></li>
                        {/if}
                    </ul>
                </div>
            </div>
        </div>
    </div>
    
    <!-- 底部 -->
    <div class="yui-footer">
        <div class="yui-1200">
            <div class="footer-content bg-white">
                <p class="footer-nav">
                    <a href="/" title="{$site_name}">网站首页</a>
                    <a href="#" target="_blank">广告服务</a>
                    <a href="#" target="_blank">法律声明</a>
                    <a href="#" target="_blank">网站介绍</a>
                    <a href="#" target="_blank">联系我们</a>
                    <a href="#" target="_blank">招聘信息</a>
                </p>
                <p class="footer-disclaimer">本站信息均由网民发表,不代表本网站立场,如侵犯了您的权利请致电投诉</p>
                <p class="footer-copyright">Copyright © 2006-{date('Y')} <a href="/" title="{$site_name}" class="db_link">{$site_name}</a> 版权所有</p>
            </div>
        </div>
    </div>

    <script type="text/javascript" src="/template/pc/js/jquery.min.js"></script>
    <script type="text/javascript" src="/template/pc/js/common.js"></script>
</body>
</html> 