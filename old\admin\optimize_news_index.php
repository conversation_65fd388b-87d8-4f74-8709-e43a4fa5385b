<?php
/**
 * 新闻数据库索引优化脚本
 * 检查并优化新闻相关表的索引
 */

// 定义入口常量
define('IN_BTMPS', true);

require_once dirname(__FILE__) . '/../include/common.inc.php';

// 检查管理员是否登录
if (!isset($_SESSION['admin']) || $_SESSION['admin']['is_login'] !== true) {
    header("Location: login.php");
    exit;
}

// 获取数据库表前缀
$db_prefix = isset($config['db_prefix']) ? $config['db_prefix'] : '';

/**
 * 检查索引是否存在
 */
function checkIndexExists($table, $indexName) {
    global $db;
    $sql = "SHOW INDEX FROM `{$table}` WHERE Key_name = '{$indexName}'";
    $result = $db->query($sql);
    return $db->num_rows($result) > 0;
}

/**
 * 创建索引
 */
function createIndex($table, $indexName, $columns, $type = 'INDEX') {
    global $db;
    
    if (checkIndexExists($table, $indexName)) {
        return "索引 {$indexName} 已存在";
    }
    
    $sql = "ALTER TABLE `{$table}` ADD {$type} `{$indexName}` ({$columns})";
    
    try {
        $db->query($sql);
        return "成功创建索引 {$indexName}";
    } catch (Exception $e) {
        return "创建索引 {$indexName} 失败: " . $e->getMessage();
    }
}

/**
 * 分析表
 */
function analyzeTable($table) {
    global $db;
    $sql = "ANALYZE TABLE `{$table}`";
    $db->query($sql);
    return "已分析表 {$table}";
}

/**
 * 优化表
 */
function optimizeTable($table) {
    global $db;
    $sql = "OPTIMIZE TABLE `{$table}`";
    $db->query($sql);
    return "已优化表 {$table}";
}

$messages = [];

// 处理优化请求
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action'])) {
    $action = $_POST['action'];
    
    switch ($action) {
        case 'optimize_indexes':
            // 优化新闻表索引
            $newsTable = $db_prefix . 'news';
            
            // 检查并创建必要的索引
            $indexes = [
                // 基础查询索引
                ['name' => 'idx_catid_show_time', 'columns' => '`catid`, `is_show`, `addtime`', 'desc' => '分类+显示状态+时间复合索引'],
                ['name' => 'idx_show_top_recommend_time', 'columns' => '`is_show`, `is_top`, `is_recommend`, `addtime`', 'desc' => '显示+置顶+推荐+时间复合索引'],
                ['name' => 'idx_addtime_desc', 'columns' => '`addtime` DESC', 'desc' => '时间降序索引'],
                ['name' => 'idx_click_desc', 'columns' => '`click` DESC', 'desc' => '点击量降序索引'],
                ['name' => 'idx_update_time', 'columns' => '`update_time`', 'desc' => '更新时间索引'],
                
                // 状态查询索引
                ['name' => 'idx_is_show', 'columns' => '`is_show`', 'desc' => '显示状态索引'],
                ['name' => 'idx_is_top', 'columns' => '`is_top`', 'desc' => '置顶状态索引'],
                ['name' => 'idx_is_recommend', 'columns' => '`is_recommend`', 'desc' => '推荐状态索引'],
                
                // 搜索优化索引
                ['name' => 'idx_title', 'columns' => '`title`(50)', 'desc' => '标题前缀索引'],
                ['name' => 'idx_author', 'columns' => '`author`', 'desc' => '作者索引'],
            ];
            
            foreach ($indexes as $index) {
                $result = createIndex($newsTable, $index['name'], $index['columns']);
                $messages[] = $index['desc'] . ': ' . $result;
            }
            
            // 创建全文索引（如果支持）
            try {
                $engine_sql = "SELECT ENGINE FROM information_schema.TABLES WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = '{$newsTable}'";
                $engine_result = $db->query($engine_sql);
                $engine_row = $db->fetch_array($engine_result);
                
                if ($engine_row && strtolower($engine_row['ENGINE']) === 'myisam') {
                    $fulltext_result = createIndex($newsTable, 'ft_title_desc', '`title`, `description`', 'FULLTEXT');
                    $messages[] = '全文索引: ' . $fulltext_result;
                } else {
                    $messages[] = '全文索引: 跳过（需要MyISAM引擎）';
                }
            } catch (Exception $e) {
                $messages[] = '全文索引检查失败: ' . $e->getMessage();
            }
            
            // 优化新闻内容表索引
            $newsContentTable = $db_prefix . 'news_content';
            $content_indexes = [
                ['name' => 'idx_content_search', 'columns' => '`content`(100)', 'desc' => '内容搜索索引'],
            ];
            
            foreach ($content_indexes as $index) {
                $result = createIndex($newsContentTable, $index['name'], $index['columns']);
                $messages[] = '内容表 ' . $index['desc'] . ': ' . $result;
            }
            
            // 优化分类表索引
            $categoryTable = $db_prefix . 'news_category';
            $category_indexes = [
                ['name' => 'idx_parent_show_sort', 'columns' => '`parentid`, `is_show`, `sort_order`', 'desc' => '父级+显示+排序复合索引'],
                ['name' => 'idx_show_sort', 'columns' => '`is_show`, `sort_order`', 'desc' => '显示+排序索引'],
            ];
            
            foreach ($category_indexes as $index) {
                $result = createIndex($categoryTable, $index['name'], $index['columns']);
                $messages[] = '分类表 ' . $index['desc'] . ': ' . $result;
            }
            
            break;
            
        case 'analyze_tables':
            // 分析表
            $tables = [$db_prefix . 'news', $db_prefix . 'news_content', $db_prefix . 'news_category'];
            foreach ($tables as $table) {
                $messages[] = analyzeTable($table);
            }
            break;
            
        case 'optimize_tables':
            // 优化表
            $tables = [$db_prefix . 'news', $db_prefix . 'news_content', $db_prefix . 'news_category'];
            foreach ($tables as $table) {
                $messages[] = optimizeTable($table);
            }
            break;
    }
}

// 获取当前索引信息
function getCurrentIndexes($table) {
    global $db;
    $sql = "SHOW INDEX FROM `{$table}`";
    $result = $db->query($sql);
    $indexes = [];
    
    while ($row = $db->fetch_array($result)) {
        $indexes[] = $row;
    }
    
    return $indexes;
}

// 获取表统计信息
function getTableStats($table) {
    global $db;
    $sql = "SELECT 
                COUNT(*) as total_rows,
                AVG(LENGTH(title)) as avg_title_length,
                AVG(LENGTH(description)) as avg_desc_length,
                COUNT(DISTINCT catid) as categories_count,
                SUM(is_top) as top_count,
                SUM(is_recommend) as recommend_count,
                SUM(CASE WHEN is_show = 1 THEN 1 ELSE 0 END) as visible_count
            FROM `{$table}`";
    
    $result = $db->query($sql);
    return $db->fetch_array($result);
}

$newsTable = $db_prefix . 'news';
$currentIndexes = getCurrentIndexes($newsTable);
$tableStats = getTableStats($newsTable);

?>
<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>新闻数据库索引优化</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background: #f5f5f5; }
        .container { max-width: 1200px; margin: 0 auto; background: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .header { border-bottom: 2px solid #007cba; padding-bottom: 10px; margin-bottom: 20px; }
        .section { margin-bottom: 30px; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        .section h3 { margin-top: 0; color: #333; }
        .btn { padding: 10px 20px; margin: 5px; border: none; border-radius: 4px; cursor: pointer; font-size: 14px; }
        .btn-primary { background: #007cba; color: white; }
        .btn-success { background: #28a745; color: white; }
        .btn-warning { background: #ffc107; color: black; }
        .btn:hover { opacity: 0.8; }
        .messages { background: #f8f9fa; border: 1px solid #dee2e6; border-radius: 4px; padding: 15px; margin: 10px 0; }
        .message { margin: 5px 0; padding: 5px; border-left: 4px solid #007cba; background: #e7f3ff; }
        table { width: 100%; border-collapse: collapse; margin: 10px 0; }
        th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
        th { background: #f2f2f2; }
        .stats { display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px; }
        .stat-card { background: #f8f9fa; padding: 15px; border-radius: 5px; text-align: center; }
        .stat-number { font-size: 24px; font-weight: bold; color: #007cba; }
        .stat-label { color: #666; margin-top: 5px; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>新闻数据库索引优化</h1>
            <p>检查和优化新闻系统的数据库索引，提升查询性能</p>
        </div>

        <?php if (!empty($messages)): ?>
        <div class="messages">
            <h3>操作结果</h3>
            <?php foreach ($messages as $message): ?>
            <div class="message"><?php echo htmlspecialchars($message); ?></div>
            <?php endforeach; ?>
        </div>
        <?php endif; ?>

        <div class="section">
            <h3>数据库统计信息</h3>
            <div class="stats">
                <div class="stat-card">
                    <div class="stat-number"><?php echo number_format($tableStats['total_rows']); ?></div>
                    <div class="stat-label">总新闻数</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number"><?php echo $tableStats['categories_count']; ?></div>
                    <div class="stat-label">分类数量</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number"><?php echo $tableStats['visible_count']; ?></div>
                    <div class="stat-label">可见新闻</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number"><?php echo $tableStats['top_count']; ?></div>
                    <div class="stat-label">置顶新闻</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number"><?php echo $tableStats['recommend_count']; ?></div>
                    <div class="stat-label">推荐新闻</div>
                </div>
            </div>
        </div>

        <div class="section">
            <h3>索引优化操作</h3>
            <form method="post" style="margin-bottom: 20px;">
                <button type="submit" name="action" value="optimize_indexes" class="btn btn-primary">
                    优化索引结构
                </button>
                <button type="submit" name="action" value="analyze_tables" class="btn btn-success">
                    分析表结构
                </button>
                <button type="submit" name="action" value="optimize_tables" class="btn btn-warning">
                    优化表数据
                </button>
            </form>
            
            <div style="background: #fff3cd; border: 1px solid #ffeaa7; padding: 10px; border-radius: 4px; margin: 10px 0;">
                <strong>注意：</strong>
                <ul style="margin: 5px 0; padding-left: 20px;">
                    <li>优化索引结构：创建高效的复合索引，提升查询性能</li>
                    <li>分析表结构：更新表的统计信息，帮助MySQL选择最优查询计划</li>
                    <li>优化表数据：整理表碎片，回收空间，提升I/O性能</li>
                    <li>建议在网站访问量较低时进行优化操作</li>
                </ul>
            </div>
        </div>

        <div class="section">
            <h3>当前索引信息</h3>
            <table>
                <thead>
                    <tr>
                        <th>索引名称</th>
                        <th>列名</th>
                        <th>索引类型</th>
                        <th>是否唯一</th>
                        <th>基数</th>
                    </tr>
                </thead>
                <tbody>
                    <?php foreach ($currentIndexes as $index): ?>
                    <tr>
                        <td><?php echo htmlspecialchars($index['Key_name']); ?></td>
                        <td><?php echo htmlspecialchars($index['Column_name']); ?></td>
                        <td><?php echo htmlspecialchars($index['Index_type']); ?></td>
                        <td><?php echo $index['Non_unique'] ? '否' : '是'; ?></td>
                        <td><?php echo number_format($index['Cardinality']); ?></td>
                    </tr>
                    <?php endforeach; ?>
                </tbody>
            </table>
        </div>

        <div class="section">
            <h3>推荐的索引策略</h3>
            <ul>
                <li><strong>复合索引优先：</strong>根据查询模式创建复合索引，如 (catid, is_show, addtime)</li>
                <li><strong>覆盖索引：</strong>让索引包含查询所需的所有列，避免回表查询</li>
                <li><strong>前缀索引：</strong>对于长文本字段使用前缀索引，如 title(50)</li>
                <li><strong>排序优化：</strong>为常用的排序字段创建降序索引</li>
                <li><strong>定期维护：</strong>定期分析和优化表，保持索引效率</li>
            </ul>
        </div>
    </div>
</body>
</html>
