{include file="header.htm"}

<style>
/* 页面标题样式 */
.page-title {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    padding-bottom: 12px;
    border-bottom: 1px solid #ddd;
}

.page-title h1 {
    font-size: 20px;
    font-weight: 600;
    color: #333;
    margin: 0;
}

/* 表单样式 - 紧凑布局 */
.form-group {
    display: flex;
    align-items: flex-start;
    margin-bottom: 16px;
    gap: 20px;
}

.form-group.compact {
    margin-bottom: 12px;
}

.form-label {
    flex: 0 0 120px;
    margin: 6px 0 0 0;
    font-weight: 600;
    color: #333;
    font-size: 14px;
    text-align: right;
}

.form-field {
    flex: 1;
    min-width: 0;
}

.form-control, .form-select {
    display: block;
    width: 100%;
    max-width: 500px;
    padding: 6px 12px;
    font-size: 14px;
    line-height: 1.5;
    color: #333;
    background-color: #fff;
    border: 1px solid #ddd;
    border-radius: 4px;
    transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
}

.form-control:focus, .form-select:focus {
    border-color: #1b68ff;
    outline: 0;
    box-shadow: 0 0 0 0.2rem rgba(27, 104, 255, 0.25);
}

.form-control.form-readonly {
    background-color: #f8f9fa;
    color: #6c757d;
    cursor: not-allowed;
}

textarea.form-control {
    min-height: 80px;
    resize: vertical;
    max-width: 600px;
}

/* 表单描述在右侧 */
.form-description {
    flex: 0 0 200px;
    margin: 6px 0 0 0;
    font-size: 12px;
    color: #666;
    line-height: 1.4;
}

/* 帮助文本样式 */
.form-hint {
    display: block;
    margin-top: 4px;
    font-size: 12px;
    color: #666;
    line-height: 1.4;
}

/* 徽章样式 */
.badge {
    display: inline-block;
    padding: 4px 8px;
    font-size: 12px;
    font-weight: 500;
    border-radius: 4px;
    text-align: center;
    white-space: nowrap;
}

.badge-success {
    color: #fff;
    background-color: #28a745;
}

.badge-warning {
    color: #212529;
    background-color: #ffc107;
}

.badge-danger {
    color: #fff;
    background-color: #dc3545;
}

.badge-secondary {
    color: #fff;
    background-color: #6c757d;
}

/* 按钮样式 */
.btn {
    display: inline-block;
    padding: 8px 16px;
    font-size: 14px;
    font-weight: 500;
    line-height: 1.5;
    text-align: center;
    text-decoration: none;
    vertical-align: middle;
    cursor: pointer;
    border: 1px solid transparent;
    border-radius: 4px;
    transition: all 0.15s ease-in-out;
}

.btn-primary {
    color: #fff;
    background-color: #1b68ff;
    border-color: #1b68ff;
}

.btn-primary:hover {
    background-color: #0056b3;
    border-color: #0056b3;
    color: #fff;
    text-decoration: none;
}

.btn-danger {
    color: #fff;
    background-color: #dc3545;
    border-color: #dc3545;
}

.btn-danger:hover {
    background-color: #c82333;
    border-color: #bd2130;
    color: #fff;
    text-decoration: none;
}

.btn-secondary {
    color: #fff;
    background-color: #6c757d;
    border-color: #6c757d;
}

.btn-secondary:hover {
    background-color: #5a6268;
    border-color: #545b62;
    color: #fff;
    text-decoration: none;
}

.btn-light-secondary {
    background-color: #f0f0f0;
    color: #666666;
    border: 1px solid #dddddd;
}

.btn-light-secondary:hover {
    background-color: #e0e0e0;
    color: #444444;
    text-decoration: none;
}

/* 卡片样式 - 紧凑版 */
.card {
    background: #fff;
    border: 1px solid #ddd;
    border-radius: 6px;
    box-shadow: 0 1px 3px rgba(0,0,0,0.1);
    margin-bottom: 20px;
}

.card-header {
    padding: 16px 20px;
    border-bottom: 1px solid #eee;
    background-color: #f8f9fa;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.card-title {
    font-size: 16px;
    font-weight: 600;
    color: #333;
    margin: 0;
}

.card-body {
    padding: 20px;
}

/* 分区样式 */
.section {
    margin-bottom: 30px;
}

.section-title {
    font-size: 16px;
    font-weight: 600;
    color: #333;
    margin: 0 0 16px 0;
    padding-bottom: 8px;
    border-bottom: 1px solid #eee;
}

/* 表单按钮区域 */
.form-actions {
    display: flex;
    gap: 12px;
    padding-top: 16px;
    border-top: 1px solid #eee;
    margin-top: 20px;
    margin-left: 140px; /* 与标签对齐 */
}

.btn-row {
    display: flex;
    gap: 12px;
    margin-top: 20px;
}

/* 信息预览样式 */
.post-preview {
    background-color: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 6px;
    padding: 20px;
    margin-top: 20px;
}

.post-preview .section-title {
    color: #495057;
    border-bottom-color: #dee2e6;
}

/* 移除链接下划线 */
a, a:hover, a:focus, a:active {
    text-decoration: none !important;
}

/* 响应式调整 */
@media (max-width: 768px) {
    .card-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 12px;
    }

    .form-group {
        flex-direction: column;
        align-items: stretch;
        gap: 8px;
    }

    .form-label {
        flex: none;
        text-align: left;
        margin: 0 0 4px 0;
    }

    .form-description {
        flex: none;
        margin: 4px 0 0 0;
    }

    .form-actions, .btn-row {
        margin-left: 0;
        flex-direction: column;
    }

    .form-control, .form-select, textarea.form-control {
        max-width: none;
    }
}
</style>

<!-- 消息提示 -->
{if $message}
<div class="alert alert-success">{$message}</div>
{/if}

<!-- 举报详情 -->
<div class="card mb-4">
    <div class="card-header d-flex justify-content-between align-items-center">
        <div>
            <h3 class="card-title">举报详情</h3>
        </div>
        <div>
            <a href="report.php" class="btn btn-light-secondary">
                <i class="fas fa-arrow-left"></i> 返回列表
            </a>
        </div>
    </div>
    
    <div class="card-body">
        <form action="report.php?action=update_status" method="post">
            <input type="hidden" name="id" value="{$report.id}">
            
            <!-- 举报基本信息 -->
            <div class="section">
                <h4 class="section-title" style="margin-bottom: 20px; padding-bottom: 10px; border-bottom: 1px solid #e9ecef;">
                    举报信息</h4>
                
                <div class="form-group compact">
                    <label class="form-label">举报ID</label>
                    <div class="form-field">
                        <input type="text" class="form-control form-readonly" readonly value="{$report.id}" style="width: 120px;">
                    </div>
                    <div class="form-description">系统自动生成的举报编号</div>
                </div>

                <div class="form-group compact">
                    <label class="form-label">举报类型</label>
                    <div class="form-field">
                        <div style="line-height: 32px;">
                            {if $report.type == '虚假信息'}
                                <span class="badge badge-warning">虚假信息</span>
                            {elseif $report.type == '诈骗信息'}
                                <span class="badge badge-danger">诈骗信息</span>
                            {elseif $report.type == '违法信息'}
                                <span class="badge badge-danger">违法信息</span>
                            {elseif $report.type == '广告信息'}
                                <span class="badge badge-warning">广告信息</span>
                            {elseif $report.type == '违规信息'}
                                <span class="badge badge-warning">违规信息</span>
                            {elseif $report.type == '其他问题'}
                                <span class="badge badge-secondary">其他问题</span>
                            {else}
                                <span class="badge badge-secondary">{$report.type}</span>
                            {/if}
                        </div>
                    </div>
                    <div class="form-description">举报人选择的举报类型</div>
                </div>

                <div class="form-group">
                    <label class="form-label">举报内容</label>
                    <div class="form-field">
                        <textarea class="form-control form-readonly" readonly rows="4">{$report.content}</textarea>
                    </div>
                    <div class="form-description">举报人填写的详细举报内容</div>
                </div>

                <div class="form-group compact">
                    <label class="form-label">联系方式</label>
                    <div class="form-field">
                        <input type="text" class="form-control form-readonly" readonly value="{$report.tel|default:'未提供'}">
                    </div>
                    <div class="form-description">举报人的联系方式（可选）</div>
                </div>

                <div class="form-group compact">
                    <label class="form-label">举报IP</label>
                    <div class="form-field">
                        <input type="text" class="form-control form-readonly" readonly value="{$report.ip}" style="width: 200px;">
                    </div>
                    <div class="form-description">举报人的IP地址</div>
                </div>

                <div class="form-group compact">
                    <label class="form-label">举报时间</label>
                    <div class="form-field">
                        <input type="text" class="form-control form-readonly" readonly value="{$report.created_at}" style="width: 200px;">
                    </div>
                    <div class="form-description">举报提交的时间</div>
                </div>
                
                <!-- 处理状态 -->
                <div class="form-group">
                    <label class="form-label">处理状态</label>
                    <div class="form-field">
                        <select name="status" class="form-control" style="width: 150px;">
                            <option value="0" {if $report.status == 0}selected{/if}>未处理</option>
                            <option value="1" {if $report.status == 1}selected{/if}>已处理</option>
                        </select>
                        <div class="form-hint">
                            修改处理状态后需点击下方"更新状态"按钮保存
                        </div>
                    </div>
                    <div class="form-description">选择举报的处理状态</div>
                </div>
            </div>
            
            <!-- 被举报信息预览 -->
            {if $post}
            <div class="section post-preview">
                <h4 class="section-title">被举报信息</h4>

                <div class="form-group compact">
                    <label class="form-label">信息ID</label>
                    <div class="form-field">
                        <input type="text" class="form-control form-readonly" readonly value="{$post.id}" style="width: 120px;">
                    </div>
                    <div class="form-description">被举报信息的ID编号</div>
                </div>

                <div class="form-group">
                    <label class="form-label">信息标题</label>
                    <div class="form-field">
                        <input type="text" class="form-control form-readonly" readonly value="{$post.title}">
                    </div>
                    <div class="form-description">被举报信息的标题</div>
                </div>

                <div class="form-group compact">
                    <label class="form-label">发布者</label>
                    <div class="form-field">
                        <input type="text" class="form-control form-readonly" readonly value="{$post.username|default:'匿名用户'}" style="width: 200px;">
                    </div>
                    <div class="form-description">信息发布者的用户名</div>
                </div>

                <div class="form-group compact">
                    <label class="form-label">发布时间</label>
                    <div class="form-field">
                        <input type="text" class="form-control form-readonly" readonly value="{$post.created_at}" style="width: 200px;">
                    </div>
                    <div class="form-description">信息发布的时间</div>
                </div>
                
                <div class="form-group">
                    <label class="form-label">信息状态</label>
                    <div class="form-field">
                        <div style="line-height: 38px;">
                            {if $post.status == 1}
                                <span class="badge badge-success">正常</span>
                            {elseif $post.status == 0}
                                <span class="badge badge-danger">已下架</span>
                            {else}
                                <span class="badge badge-secondary">未知</span>
                            {/if}
                        </div>
                    </div>
                </div>
                
                <div style="margin-top: 20px;">
                    <a href="../{$post.category_pinyin}/{$post.id}.html" target="_blank" class="btn btn-light-primary">
                        <i class="fas fa-external-link-alt"></i> 查看原信息
                    </a>
                    <a href="info.php?action=edit&id={$post.id}" target="_blank" class="btn btn-light-primary">
                        <i class="fas fa-edit"></i> 编辑信息
                    </a>
                </div>
            </div>
            {else}
            <div class="section post-preview">
                <h4>被举报信息</h4>
                <p class="text-muted">信息不存在或已被删除</p>
            </div>
            {/if}
            
            <!-- 操作按钮 -->
            <div class="form-actions">
                <button type="submit" class="btn btn-primary">
                    <i class="fas fa-check"></i> 更新状态
                </button>

                <a href="report.php?action=delete&id={$report.id}" class="btn btn-danger" onclick="return confirm('确定要删除这条举报记录吗？')">
                    <i class="fas fa-trash"></i> 删除举报
                </a>

                <a href="report.php" class="btn btn-secondary">
                    <i class="fas fa-times"></i> 取消
                </a>
            </div>
        </form>
    </div>
</div>

{include file="footer.htm"} 