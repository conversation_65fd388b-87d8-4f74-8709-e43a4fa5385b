/*根据UI结构重写CSS，仅在相应UI组件创建时，加载对应css，顺序加载
*/

/*-------基础UI构建，必须加载-------*/
@import "uibase.css";
@import "toolbar.css";
@import "editor.css";


/*-------可选中菜单按钮，按需加载-------*/
    /*可选中菜单按钮--依赖splitbutton*/
@import "menubutton.css";
    /*可选中菜单按钮-弹出菜单*/
@import "menu.css";


/*-------不可选中菜单按钮，按需加载-------*/
    /*不可选中菜单按钮--依赖splitbutton*/
@import "combox.css";


/*-------按钮类型，按需加载-------*/
    /*普通按钮*/
@import "button.css";
    /*按钮icon*/
@import "buttonicon.css";
    /*弹出菜单按钮-附加按钮*/
@import "splitbutton.css";
    /*弹出菜单*/
@import "popup.css";
    /*提示消息*/
@import "message.css";


/*-------独立按钮样式，按需加载-------*/
    /*弹出对话框样式*/
@import "dialog.css";
    /*段落格式弹出菜单*/
@import "paragraphpicker.css";
    /*表格弹出菜单*/
@import "tablepicker.css";
    /*颜色弹出菜单*/
@import "colorpicker.css";
    /*自动排版弹出菜单*/
@import "autotypesetpicker.css";
    /*平均分布菜单*/
@import "cellalignpicker.css";
    /*分隔线*/
@import "separtor.css";
    /*颜色按钮--依赖splitbutton*/
@import "colorbutton.css";
    /*表情按钮--依赖splitbutton*/
@import "multiMenu.css";
    /*右键菜单*/
@import "contextmenu.css";
    /*快捷菜单*/
@import "shortcutmenu.css";
    /*粘贴提示*/
@import "pastepicker.css";