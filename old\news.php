<?php
/**
 * 新闻前台入口文件
 */

// 定义入口常量
define('IN_BTMPS', true);

// 引入公共文件
require_once dirname(__FILE__) . '/include/common.inc.php';

// 获取操作类型
$id = isset($_GET['id']) ? intval($_GET['id']) : 0;
$catid = isset($_GET['catid']) ? intval($_GET['catid']) : 0;
$catpinyin = isset($_GET['catpinyin']) ? trim($_GET['catpinyin']) : '';
$page = isset($_GET['page']) ? intval($_GET['page']) : 1;
$page = max(1, $page);
$perpage = 10; // 每页显示数量

// 如果有栏目拼音参数，根据拼音查找栏目ID
if (!empty($catpinyin) && $catid == 0) {
    $sql = "SELECT catid FROM news_category WHERE pinyin = '" . $db->escape($catpinyin) . "' AND is_show = 1 LIMIT 1";
    $result = $db->query($sql);
    if ($result && $db->num_rows($result) > 0) {
        $row = $db->fetch_array($result);
        $catid = $row['catid'];
    }
}

// 根据参数确定操作类型
$op = 'index';
if ($id > 0) {
    $op = 'detail';
} elseif ($catid > 0) {
    $op = 'list';
}

// 定义数据库表前缀
$db_prefix = $config['db_prefix'];

// 获取所有新闻栏目
$sql = "SELECT * FROM `news_category` ORDER BY `sort_order` ASC, `catid` ASC";
$result = $db->query($sql);
$all_categories = [];
while ($row = $db->fetch_array($result)) {
    $all_categories[$row['catid']] = $row;
}

/**
 * 生成新闻详情页URL
 */
function build_news_detail_url($id) {
    return "/news/{$id}.html";
}

/**
 * 生成新闻栏目页URL
 */
function build_news_category_url($catpinyin, $page = 1) {
    if (empty($catpinyin)) {
        return '/news/';
    }

    if ($page > 1) {
        return "/news/{$catpinyin}/p{$page}/";
    } else {
        return "/news/{$catpinyin}/";
    }
}

/**
 * 生成新闻首页URL
 */
function build_news_home_url($page = 1) {
    if ($page > 1) {
        return "/news/?page={$page}";
    } else {
        return '/news/';
    }
}

// 构建栏目树
$top_categories = [];
$sub_categories = [];
foreach ($all_categories as $cat) {
    if ($cat['parentid'] == 0) {
        $top_categories[$cat['catid']] = $cat;
        $top_categories[$cat['catid']]['sub_cats'] = [];
    } else {
        $sub_categories[] = $cat;
    }
}

// 将子栏目加入父栏目中
foreach ($sub_categories as $sub_cat) {
    if (isset($top_categories[$sub_cat['parentid']])) {
        $top_categories[$sub_cat['parentid']]['sub_cats'][] = $sub_cat;
    }
}

// 异步更新阅读量
function update_news_click($id) {
    global $db;
    
    // 使用SESSION暂存阅读计数
    if (!isset($_SESSION['news_clicks'])) {
        $_SESSION['news_clicks'] = [];
    }
    
    $_SESSION['news_clicks'][$id] = ($_SESSION['news_clicks'][$id] ?? 0) + 1;
    
    // 当计数达到一定值或在合适的时机批量更新
    if ($_SESSION['news_clicks'][$id] >= 5) {
        update_news_click_batch();
    }
}

function update_news_click_batch() {
    global $db;
    
    if (empty($_SESSION['news_clicks'])) {
        return;
    }
    
    // 构建批量更新SQL
    $cases = [];
    $ids = [];
    
    foreach ($_SESSION['news_clicks'] as $id => $count) {
        if ($count > 0) {
            $cases[] = "WHEN {$id} THEN click + {$count}";
            $ids[] = $id;
            unset($_SESSION['news_clicks'][$id]);
        }
    }
    
    if (!empty($cases) && !empty($ids)) {
        $ids_str = implode(',', $ids);
        $sql = "UPDATE `news` SET click = CASE id " . 
                implode(' ', $cases) . 
                " END WHERE id IN ({$ids_str})";
        $db->query($sql);
    }
}

// 根据不同的操作类型进行处理
switch ($op) {
    // 新闻首页 - 统一新闻列表显示
    case 'index':
        // 分页参数
        $start = ($page - 1) * $perpage;

        // 获取筛选参数
        $filter_recommend = isset($_GET['recommend']) ? intval($_GET['recommend']) : 0;
        $filter_top = isset($_GET['top']) ? intval($_GET['top']) : 0;

        // 构建查询条件
        $where_conditions = ["n.is_show = 1"];

        if ($filter_recommend) {
            $where_conditions[] = "n.is_recommend = 1";
        }

        if ($filter_top) {
            $where_conditions[] = "n.is_top = 1";
        }

        $where = implode(' AND ', $where_conditions);

        // 生成缓存参数
        $cache_params = [
            'page' => $page,
            'perpage' => $perpage,
            'filter_recommend' => $filter_recommend,
            'filter_top' => $filter_top,
            'where' => $where
        ];

        // 尝试从缓存获取数据
        $cached_data = news_cache_get_home($cache_params);

        if ($cached_data !== false) {
            // 使用缓存数据
            $news_list = $cached_data['news_list'];
            $total_row = $cached_data['total_row'];
        } else {
            // 从数据库查询
            $sql = "SELECT SQL_CALC_FOUND_ROWS n.id, n.catid, n.title, n.author, n.click, n.is_top,
                    n.is_recommend, n.addtime, n.description, n.thumb, c.catname
                    FROM `news` n
                    LEFT JOIN `news_category` c ON n.catid = c.catid
                    WHERE {$where}
                    ORDER BY n.is_top DESC, n.is_recommend DESC, n.addtime DESC, n.id DESC
                    LIMIT {$start}, {$perpage}";
            $result = $db->query($sql);
            $news_list = [];
            while ($row = $db->fetch_array($result)) {
                // 确保thumb字段存在
                if (!isset($row['thumb'])) {
                    $row['thumb'] = '';
                }
                // 确保description字段存在
                if (!isset($row['description'])) {
                    $row['description'] = '';
                }
                $news_list[] = $row;
            }

            // 获取总记录数
            $total_result = $db->query("SELECT FOUND_ROWS() as total");
            $total_row = $db->fetch_array($total_result);

            // 缓存数据
            $cache_data = [
                'news_list' => $news_list,
                'total_row' => $total_row
            ];
            news_cache_set_home($cache_data, $cache_params); // 使用配置的缓存时间
        }
        $total = $total_row['total'];
        $total_page = ceil($total / $perpage);

        // 生成分页
        $pagebar = '';
        if ($total_page > 1) {
            $pagebar = '<div class="pagination">';

            // 上一页
            if ($page > 1) {
                $prev_page = $page - 1;
                $pagebar .= '<a href="/news/?page=' . $prev_page;
                if ($filter_recommend) $pagebar .= '&recommend=1';
                if ($filter_top) $pagebar .= '&top=1';
                $pagebar .= '" class="prev">上一页</a>';
            }

            // 页码
            $start_page = max(1, $page - 2);
            $end_page = min($total_page, $page + 2);

            for ($i = $start_page; $i <= $end_page; $i++) {
                if ($i == $page) {
                    $pagebar .= '<span class="current">' . $i . '</span>';
                } else {
                    $pagebar .= '<a href="/news/?page=' . $i;
                    if ($filter_recommend) $pagebar .= '&recommend=1';
                    if ($filter_top) $pagebar .= '&top=1';
                    $pagebar .= '">' . $i . '</a>';
                }
            }

            // 下一页
            if ($page < $total_page) {
                $next_page = $page + 1;
                $pagebar .= '<a href="/news/?page=' . $next_page;
                if ($filter_recommend) $pagebar .= '&recommend=1';
                if ($filter_top) $pagebar .= '&top=1';
                $pagebar .= '" class="next">下一页</a>';
            }

            $pagebar .= '</div>';
        }

        // 获取新闻分类
        $news_categories = [];
        foreach ($top_categories as $cat) {
            if ($cat['parentid'] == 0) {
                $news_categories[] = $cat;
            }
        }

        // 获取热门新闻
        $sql = "SELECT id, title, addtime, click, thumb, description, is_top, is_recommend, author FROM `news`
                WHERE is_show = 1 ORDER BY click DESC LIMIT 10";
        $result = $db->query($sql);
        $hot_news = [];
        while ($row = $db->fetch_array($result)) {
            // 确保所有字段都有默认值
            $row['thumb'] = $row['thumb'] ?? '';
            $row['description'] = $row['description'] ?? '';
            $row['is_top'] = $row['is_top'] ?? 0;
            $row['is_recommend'] = $row['is_recommend'] ?? 0;
            $row['author'] = $row['author'] ?? '';
            $hot_news[] = $row;
        }

        // 设置模板变量
        $tpl->assign('top_categories', $top_categories);
        $tpl->assign('all_categories', $all_categories);
        $tpl->assign('news_categories', $news_categories); // 添加新闻分类变量
        $tpl->assign('news_list', $news_list);
        $tpl->assign('hot_news', $hot_news);
        $tpl->assign('pagebar', $pagebar);
        $tpl->assign('total', $total);
        $tpl->assign('page', $page);
        $tpl->assign('total_page', $total_page);
        $tpl->assign('current_page', 'news');
        $tpl->assign('filter_recommend', $filter_recommend);
        $tpl->assign('filter_top', $filter_top);
        $tpl->assign('catid', $catid); // 添加catid变量
        $tpl->assign('catpinyin', $catpinyin); // 添加catpinyin变量
        $tpl->assign('page_title', '新闻中心');

        // 显示模板
        $tpl->display('news_index.htm');
        break;
    
    // 新闻列表页
    case 'list':
        // 获取筛选参数
        $filter_recommend = isset($_GET['recommend']) ? intval($_GET['recommend']) : 0;
        $filter_top = isset($_GET['top']) ? intval($_GET['top']) : 0;

        // 获取当前栏目信息
        $current_cat = [];
        if ($catid > 0) {
            if (isset($all_categories[$catid])) {
                $current_cat = $all_categories[$catid];

                // 确保description字段存在
                if (!isset($current_cat['description'])) {
                    $current_cat['description'] = '';
                }

                // 如果是一级栏目，需要获取其所有子栏目的文章
                if ($current_cat['parentid'] == 0) {
                    $sub_catids = [];
                    foreach ($sub_categories as $sub_cat) {
                        if ($sub_cat['parentid'] == $catid) {
                            $sub_catids[] = $sub_cat['catid'];
                        }
                    }

                    if (!empty($sub_catids)) {
                        $catid_str = implode(',', $sub_catids);
                        $where = "n.is_show = 1 AND n.catid IN ({$catid_str})";
                    } else {
                        $where = "n.is_show = 1 AND n.catid = {$catid}";
                    }
                } else {
                    // 二级栏目直接查询
                    $where = "n.is_show = 1 AND n.catid = {$catid}";
                }
            } else {
                $where = "n.is_show = 1";
            }
        } else {
            $where = "n.is_show = 1";
        }

        // 添加推荐筛选
        if ($filter_recommend == 1) {
            $where .= " AND n.is_recommend = 1";
        }

        // 添加置顶筛选
        if ($filter_top == 1) {
            $where .= " AND n.is_top = 1";
        }
        
        // 生成缓存参数
        $cache_params = [
            'catid' => $catid,
            'page' => $page,
            'perpage' => $perpage,
            'filter_recommend' => $filter_recommend,
            'filter_top' => $filter_top,
            'where' => $where
        ];

        // 尝试从缓存获取数据
        $cached_data = news_cache_get_list($cache_params);

        if ($cached_data !== false) {
            // 使用缓存数据
            $news_list = $cached_data['news_list'];
            $total_row = $cached_data['total_row'];
        } else {
            // 使用SQL_CALC_FOUND_ROWS优化分页查询
            $start = ($page - 1) * $perpage;

            $sql = "SELECT SQL_CALC_FOUND_ROWS n.id, n.catid, n.title, n.author, n.click, n.is_top,
                    n.is_recommend, n.addtime, n.description, n.thumb, c.catname
                    FROM `news` n
                    LEFT JOIN `news_category` c ON n.catid = c.catid
                    WHERE {$where}
                    ORDER BY n.addtime DESC, n.id DESC
                    LIMIT {$start}, {$perpage}";
            $result = $db->query($sql);
            $news_list = [];
            while ($row = $db->fetch_array($result)) {
                // 确保thumb字段存在
                if (!isset($row['thumb'])) {
                    $row['thumb'] = '';
                }
                // 描述字段可能在news_category表中
                if (!isset($row['description']) && isset($current_cat['description'])) {
                    $row['description'] = $current_cat['description'];
                } elseif (!isset($row['description'])) {
                    $row['description'] = '';
                }
                $news_list[] = $row;
            }

            // 获取总记录数，不需要额外COUNT查询
            $count_result = $db->query("SELECT FOUND_ROWS() AS total");
            $count_data = $db->fetch_array($count_result);
            $total = $count_data['total'];
            $total_row = ['total' => $total];

            // 缓存数据
            $cache_data = [
                'news_list' => $news_list,
                'total_row' => $total_row
            ];
            news_cache_set_list($cache_data, $cache_params); // 使用配置的缓存时间
        }

        $total = $total_row['total'];
        
        // 计算分页
        $total_page = ceil($total / $perpage);
        $page = min($page, $total_page);
        
        // 构建分页URL
        $current_cat_pinyin = '';
        if ($catid > 0 && isset($all_categories[$catid]) && !empty($all_categories[$catid]['pinyin'])) {
            $current_cat_pinyin = $all_categories[$catid]['pinyin'];
        }

        // 构建分页URL参数
        $page_params = [];
        if ($filter_recommend == 1) {
            $page_params[] = 'recommend=1';
        }
        if ($filter_top == 1) {
            $page_params[] = 'top=1';
        }
        $page_query = empty($page_params) ? '' : '?' . implode('&', $page_params);

        // 生成分页HTML
        $pagebar = '';
        if ($total_page > 1) {
            $pagebar = '<div class="pagination">';

            // 上一页
            if ($page > 1) {
                if (!empty($current_cat_pinyin)) {
                    if ($page - 1 == 1) {
                        $pagebar .= '<a href="/news/' . $current_cat_pinyin . '/' . $page_query . '">上一页</a>';
                    } else {
                        $pagebar .= '<a href="/news/' . $current_cat_pinyin . '/p' . ($page - 1) . '/' . $page_query . '">上一页</a>';
                    }
                } else {
                    $pagebar .= '<a href="/news/?page=' . ($page - 1) . ($page_query ? '&' . substr($page_query, 1) : '') . '">上一页</a>';
                }
            } else {
                $pagebar .= '<span class="disabled">上一页</span>';
            }

            // 数字页码
            $start_page = max(1, $page - 3);
            $end_page = min($total_page, $start_page + 6);
            if ($start_page > 1) {
                if (!empty($current_cat_pinyin)) {
                    $pagebar .= '<a href="/news/' . $current_cat_pinyin . '/' . $page_query . '">1</a>';
                } else {
                    $pagebar .= '<a href="/news/' . $page_query . '">1</a>';
                }
                if ($start_page > 2) {
                    $pagebar .= '<span class="ellipsis">...</span>';
                }
            }

            for ($i = $start_page; $i <= $end_page; $i++) {
                if ($i == $page) {
                    $pagebar .= '<span class="current">' . $i . '</span>';
                } else {
                    if (!empty($current_cat_pinyin)) {
                        if ($i == 1) {
                            $pagebar .= '<a href="/news/' . $current_cat_pinyin . '/' . $page_query . '">' . $i . '</a>';
                        } else {
                            $pagebar .= '<a href="/news/' . $current_cat_pinyin . '/p' . $i . '/' . $page_query . '">' . $i . '</a>';
                        }
                    } else {
                        if ($i == 1) {
                            $pagebar .= '<a href="/news/' . $page_query . '">' . $i . '</a>';
                        } else {
                            $pagebar .= '<a href="/news/?page=' . $i . ($page_query ? '&' . substr($page_query, 1) : '') . '">' . $i . '</a>';
                        }
                    }
                }
            }

            if ($end_page < $total_page) {
                if ($end_page < $total_page - 1) {
                    $pagebar .= '<span class="ellipsis">...</span>';
                }
                if (!empty($current_cat_pinyin)) {
                    $pagebar .= '<a href="/news/' . $current_cat_pinyin . '/p' . $total_page . '/' . $page_query . '">' . $total_page . '</a>';
                } else {
                    $pagebar .= '<a href="/news/?page=' . $total_page . ($page_query ? '&' . substr($page_query, 1) : '') . '">' . $total_page . '</a>';
                }
            }

            // 下一页
            if ($page < $total_page) {
                if (!empty($current_cat_pinyin)) {
                    $pagebar .= '<a href="/news/' . $current_cat_pinyin . '/p' . ($page + 1) . '/' . $page_query . '">下一页</a>';
                } else {
                    $pagebar .= '<a href="/news/?page=' . ($page + 1) . ($page_query ? '&' . substr($page_query, 1) : '') . '">下一页</a>';
                }
            } else {
                $pagebar .= '<span class="disabled">下一页</span>';
            }

            $pagebar .= '</div>';
        }
        
        // 获取热门新闻
        $sql = "SELECT id, title, addtime, click FROM `news` 
                WHERE is_show = 1 ORDER BY click DESC LIMIT 10";
        $result = $db->query($sql);
        $hot_news = [];
        while ($row = $db->fetch_array($result)) {
            $hot_news[] = $row;
        }
        
        // 获取新闻分类
        $news_categories = [];
        foreach ($top_categories as $cat) {
            if ($cat['parentid'] == 0) {
                $news_categories[] = $cat;
            }
        }

        // 设置模板变量
        $tpl->assign('top_categories', $top_categories);
        $tpl->assign('all_categories', $all_categories);
        $tpl->assign('news_categories', $news_categories); // 添加新闻分类变量
        $tpl->assign('category_info', $current_cat); // 添加当前分类信息
        $tpl->assign('current_cat', $current_cat);
        $tpl->assign('news_list', $news_list);
        $tpl->assign('hot_news', $hot_news);
        $tpl->assign('pagebar', $pagebar);
        $tpl->assign('total', $total);
        $tpl->assign('page', $page);
        $tpl->assign('total_page', $total_page);
        $tpl->assign('current_page', 'news');
        $tpl->assign('filter_recommend', $filter_recommend);
        $tpl->assign('filter_top', $filter_top);
        $tpl->assign('catid', $catid);
        $tpl->assign('catpinyin', $catpinyin);
        
        $page_title = isset($current_cat['catname']) ? $current_cat['catname'] : '新闻列表';
        $tpl->assign('page_title', $page_title);
        
        // 显示模板
        $tpl->display('news_list.htm');
        break;
    
    // 新闻详情页
    case 'detail':
        if ($id <= 0) {
            header("Location: news.php");
            exit;
        }
        
        // 尝试从缓存获取新闻详情
        $cached_news = news_cache_get_detail($id);

        if ($cached_news !== false) {
            // 使用缓存数据
            $news = $cached_news;
        } else {
            // 分开查询主表和内容表
            $sql = "SELECT n.*, c.catname, c.parentid, c.pinyin
                    FROM `news` n
                    LEFT JOIN `news_category` c ON n.catid = c.catid
                    WHERE n.id = ? AND n.is_show = 1";
            $result = $db->query($sql, [$id]);
            $news = $db->fetch_array($result);

            if ($news) {
                // 单独查询内容
                $sql_content = "SELECT content FROM `news_content` WHERE id = ?";
                $result_content = $db->query($sql_content, [$id]);
                $content_data = $db->fetch_array($result_content);

                if ($content_data) {
                    $news['content'] = $content_data['content'];
                } else {
                    $news['content'] = '';
                }

                // 缓存新闻详情（使用配置的缓存时间）
                news_cache_set_detail($id, $news);
            }
        }
        
        if (!$news) {
            header("Location: news.php");
            exit;
        }
        
        // 异步更新点击量
        update_news_click($id);
        
        // 获取新闻所属栏目的父栏目
        $parent_cat = [];
        if ($news['catid'] > 0 && isset($all_categories[$news['catid']])) {
            $parent_catid = $all_categories[$news['catid']]['parentid'];
            if ($parent_catid > 0 && isset($all_categories[$parent_catid])) {
                $parent_cat = $all_categories[$parent_catid];
            }
        }
        
        // 获取上一篇和下一篇 - 使用预处理语句
        $sql = "SELECT id, title FROM `news` 
                WHERE id < ? AND is_show = 1 AND catid = ? 
                ORDER BY id DESC LIMIT 1";
        $result = $db->query($sql, [$id, $news['catid']]);
        $prev_news = $db->fetch_array($result);
        
        $sql = "SELECT id, title FROM `news` 
                WHERE id > ? AND is_show = 1 AND catid = ? 
                ORDER BY id ASC LIMIT 1";
        $result = $db->query($sql, [$id, $news['catid']]);
        $next_news = $db->fetch_array($result);
        
        // 获取相关新闻 - 使用预处理语句，包含缩略图
        $sql = "SELECT id, title, addtime, thumb FROM `news`
                WHERE is_show = 1 AND catid = ? AND id != ?
                ORDER BY addtime DESC LIMIT 6";
        $result = $db->query($sql, [$news['catid'], $id]);
        $related_news = [];
        while ($row = $db->fetch_array($result)) {
            $related_news[] = $row;
        }
        
        // 获取热门新闻
        $sql = "SELECT id, title, addtime, click FROM `news` 
                WHERE is_show = 1 ORDER BY click DESC LIMIT 10";
        $result = $db->query($sql);
        $hot_news = [];
        while ($row = $db->fetch_array($result)) {
            $hot_news[] = $row;
        }
        
        // 处理文章内容中的换行
        if (isset($news['content'])) {
            $news['content'] = nl2br($news['content']);
        }
        
        // 设置模板变量
        $tpl->assign('top_categories', $top_categories);
        $tpl->assign('news', $news);
        $tpl->assign('parent_cat', $parent_cat);
        $tpl->assign('prev_news', $prev_news);
        $tpl->assign('next_news', $next_news);
        $tpl->assign('related_news', $related_news);
        $tpl->assign('hot_news', $hot_news);
        $tpl->assign('current_page', 'news');
        
        $tpl->assign('page_title', $news['title']);
        
        // 显示模板
        $tpl->display('news_detail.htm');
        
        // 批量更新点击数
        if (mt_rand(1, 10) == 1) { // 随机触发批量更新，减少数据库写入频率
            update_news_click_batch();
        }
        break;
    
    // 默认跳转到首页
    default:
        header("Location: news.php");
        exit;
} 